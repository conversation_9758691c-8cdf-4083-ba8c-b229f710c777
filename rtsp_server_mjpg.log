root@rk3576-buildroot:/# rtsp_server_pipe --gst-debug=4 -p 8556 -u /stream -i  "( v4l2src device=/dev/video0 ! image/jpeg,format=MJPG,width=2560,height=720,framerate=30/1 ! mppjpe
gdec ! mpph264enc profile=baseline ! rtph264pay name=pay0 pt=96 )"
0:00:00.000459286  1285   0x559d333dc0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.001014347  1285   0x559d333dc0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001259423  1285   0x559d333dc0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001359274  1285   0x559d333dc0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001373994  1285   0x559d333dc0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001419193  1285   0x559d333dc0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007450744  1285   0x559d333dc0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.005996 seconds
0:00:00.007514840  1285   0x559d333dc0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.008076804  1285   0x559d333dc0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.008092754  1285   0x559d333dc0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.008107710  1285   0x559d333dc0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.008117741  1285   0x559d333dc0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.008125763  1285   0x559d333dc0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
0:00:00.008379995  1285   0x559d333dc0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x559d418fd0 for path /stream
stream ready at rtsp://0.0.0.0:8556/stream
0:00:19.973689392  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x559d4239a0 connected to server ip ************, ipv6 = 0
0:00:19.973714700  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x559d4239a0 ip ***********:56040
0:00:19.973958026  1285   0x7f80000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x559d423f50
0:00:19.973957933  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x559d4239a0: attaching to context 0x559d424170
0:00:19.974201512  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x559d4239a0: received a request OPTIONS rtsp://************:8556/stream 1.0
0:00:19.975290616  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x559d4239a0: received a request DESCRIBE rtsp://************:8556/stream 1.0
0:00:19.975326208  1285   0x7f80000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x559d418fd0 for path /stream
0:00:19.975344191  1285   0x7f80000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( v4l2src device=/dev/video0 ! image/jpeg,format=MJPG,width=2560,height=720,framerate=30/1 ! mppjpegdec ! mpph264enc profile=baseline ! rtph264pay name=pay0 pt=96 )'
0:00:19.977234975  1285   0x7f80000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideo4linux2.so" loaded
0:00:19.979035898  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "v4l2src"
0:00:19.979076996  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f7800da50> adding pad 'src'
0:00:19.983082914  1285   0x7f80000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:19.983283444  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mppjpegdec"
0:00:19.983317301  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f78011060> adding pad 'sink'
0:00:19.983339645  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f78011060> adding pad 'src'
0:00:19.983542387  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:19.983569873  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f78028200> adding pad 'sink'
0:00:19.983591407  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f78028200> adding pad 'src'
0:00:19.984902103  1285   0x7f80000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:19.985096371  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:19.985137599  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78034ae0> adding pad 'src'
0:00:19.985159640  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78034ae0> adding pad 'sink'
0:00:19.985196919  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:19.985294903  1285   0x7f80000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstV4l2Src named v4l2src0 to some pad of GstMppJpegDec named mppjpegdec0 (0/0) with caps "image/jpeg, format=(string)MJPG, width=(int)2560, height=(int)720, framerate=(fraction)30/1"
0:00:19.986188622  1285   0x7f80000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:19.986212933  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:19.986345649  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7803ac70> adding pad 'sink'
0:00:19.986369295  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7803ac70> adding pad 'src'
0:00:19.986388838  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:19.986419586  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:19.986436674  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.986455782  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element v4l2src0:(any) to element capsfilter0:sink
0:00:19.986468752  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:19.986481637  1285   0x7f80000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: v4l2src0 and capsfilter0 in same bin, no need for ghost pads
0:00:19.986511966  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link v4l2src0:src and capsfilter0:sink
0:00:19.986534327  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:19.986556689  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked v4l2src0:src and capsfilter0:sink, successful
0:00:19.986567070  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.986578934  1285   0x7f80000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<v4l2src0:src> Received event on flushing pad. Discarding
0:00:19.986597272  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mppjpegdec0:(any)
0:00:19.986609161  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:19.986631834  1285   0x7f80000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mppjpegdec0:sink
0:00:19.986666616  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec0:src> pad has no peer
0:00:19.986706877  1285   0x7f80000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mppjpegdec0 in same bin, no need for ghost pads
0:00:19.986724471  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mppjpegdec0:sink
0:00:19.986750079  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec0:src> pad has no peer
0:00:19.986780465  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mppjpegdec0:sink, successful
0:00:19.986790309  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.986801716  1285   0x7f80000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:19.986826245  1285   0x7f80000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppJpegDec named mppjpegdec0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "(NULL)"
0:00:19.986839559  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mppjpegdec0:(any) to element mpph264enc0:(any)
0:00:19.986853843  1285   0x7f80000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mppjpegdec0:src and mpph264enc0:sink
0:00:19.986870547  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:19.986903055  1285   0x7f80000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mppjpegdec0 and mpph264enc0 in same bin, no need for ghost pads
0:00:19.986919721  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mppjpegdec0:src and mpph264enc0:sink
0:00:19.986934435  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:19.986962840  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mppjpegdec0:src and mpph264enc0:sink, successful
0:00:19.986972660  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.986983538  1285   0x7f80000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mppjpegdec0:src> Received event on flushing pad. Discarding
0:00:19.987005793  1285   0x7f80000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:19.987018328  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element pay0:(any)
0:00:19.987032435  1285   0x7f80000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and pay0:sink
0:00:19.987049180  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:19.987068661  1285   0x7f80000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and pay0 in same bin, no need for ghost pads
0:00:19.987084604  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and pay0:sink
0:00:19.987098626  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:19.987116850  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and pay0:sink, successful
0:00:19.987126560  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.987136781  1285   0x7f80000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:19.987262439  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:19.987281650  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f78034ae0
0:00:19.987294541  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:19.987357747  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:19.987372993  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:19.987383020  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.987396222  1285   0x7f80000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:19.987416373  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:19.987470515  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:19.987490860  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:19.987507608  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:19.987524291  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:19.987540522  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:19.987561699  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:19.987667727  1285   0x7f80000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f7803e260 for url /stream
0:00:19.987755259  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f7803e260
0:00:19.987800045  1285   0x7f80000d30 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f7803fd70
0:00:19.988663912  1285   0x7f80000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:19.988687442  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:19.989188285  1285   0x7f80000d30 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f7803f840 joining bin as session 0
0:00:19.989221513  1285   0x7f80000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:19.989634341  1285   0x7f80000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:19.989745054  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f7c00acd0> adding pad 'sink'
0:00:19.989769407  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f7c00acd0> adding pad 'rtcp_sink'
0:00:19.989787316  1285   0x7f80000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:19.989867372  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7c00c2b0> adding pad 'src'
0:00:19.989882458  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7c00c2b0> adding pad 'sink'
0:00:19.989990812  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:19.990006747  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:19.990019821  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:19.990056657  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:19.990087185  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:19.990101821  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:19.990148907  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:19.990167658  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:19.990178554  1285   0x7f80000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.990204717  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:19.990239312  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:19.990254171  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:19.990263925  1285   0x7f80000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.990282131  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:19.990302517  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:19.990345254  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:19.990355916  1285   0x7f80000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.990371088  1285   0x7f80000d30 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:19.990390061  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:19.990431414  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:19.990474972  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:19.990490295  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:19.990500475  1285   0x7f80000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.990522768  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:19.990557484  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:19.990584709  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:19.990602929  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:19.990614596  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:19.990630108  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:19.990647400  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:19.990657281  1285   0x7f80000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.990695414  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:19.990709573  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:19.990719355  1285   0x7f80000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:19.990739096  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:19.990788315  1285   0x7f80000d30 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f7803e260
0:00:19.990806454  1285   0x7f80000d30 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f7803e260
0:00:19.990819436  1285   0x7f80000d30 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f7803e260
0:00:19.990844590  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:19.990874605  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:19.990887884  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:19.990900333  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:19.990928802  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:19.990945083  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:19.990957237  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:19.990968789  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:19.990984946  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:19.990999432  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:19.991011580  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:19.991023115  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:19.991038072  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:19.991051828  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:19.991063314  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:19.991078508  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:19.991091712  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:19.991123732  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:19.991137315  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:19.991149131  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:19.991165048  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:19.991179398  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:19.991192857  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:19.991204556  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:19.991219668  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:19.991234103  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current NULL pending VOID_PENDING, desired next READY
0:00:19.991247596  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to READY
0:00:19.991259216  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:19.991279638  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 2(READY) successfully
0:00:19.991294540  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:19.991307121  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:19.991318807  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:19.991333794  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:19.991347045  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current NULL pending VOID_PENDING, desired next READY
0:00:20.174601298  1285   0x7f80000d30 INFO                    v4l2 v4l2_calls.c:592:gst_v4l2_open:<v4l2src0:src> Opened device 'FF Camera: FF Camera' (/dev/video0) successfully
0:00:20.174642214  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to READY
0:00:20.174657437  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:20.174688738  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 2(READY) successfully
0:00:20.174707251  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:20.174719582  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:20.174736164  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:20.174751549  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:20.174764066  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:20.174777941  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:20.174805227  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.174839983  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.174858277  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:20.174870897  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.174888558  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:20.174904699  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.174922143  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:20.174934449  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.174950678  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:20.174965293  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.174982808  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:20.174994860  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.175015214  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:20.175031769  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:20.175043913  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.175059172  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:20.175072571  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.175102517  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.175125266  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:20.175137255  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.175153598  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:20.175168931  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.175526471  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:20.175546517  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.175570768  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:20.175591842  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.175693083  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to PAUSED
0:00:20.175707191  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.175726561  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 3(PAUSED) successfully
0:00:20.175743724  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.175764496  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:20.175777256  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.175793638  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:20.175807880  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current READY pending VOID_PENDING, desired next PAUSED
0:00:20.175839474  1285   0x7f80000d30 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<v4l2src0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:20.175876897  1285   0x7f80000d30 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f780101d0 on task 0x7f7c0259f0
0:00:20.175890034  1285   0x7f80000d30 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<v4l2src0:src> created task 0x7f7c0259f0
0:00:20.175995525  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to PAUSED
0:00:20.176117828  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.176139572  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 3(PAUSED) successfully without preroll
0:00:20.176161840  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:20.176176200  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.176161857  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "v4l2src0"
0:00:20.176196600  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:20.176237157  1285   0x7f80000d30 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:20.176248609  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:20.176259718  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:20.176275048  1285   0x7f80000d30 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f7803e260
0:00:20.176286976  1285   0x7f80000d30 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803e260
0:00:20.176307882  1285   0x7f80000d90 INFO                 v4l2src gstv4l2src.c:722:gst_v4l2src_query_preferred_size:<v4l2src0> Detect input 0 as `Input 1`
0:00:20.176336902  1285   0x7f80000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:20.176368168  1285   0x7f80000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:20.176392657  1285   0x7f80000d90 INFO                    v4l2 gstv4l2object.c:1307:gst_v4l2_object_fill_format_list:<v4l2src0:src> got 2 format(s):
0:00:20.176394688  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:20.176418157  1285   0x7f80000d90 INFO                    v4l2 gstv4l2object.c:1311:gst_v4l2_object_fill_format_list:<v4l2src0:src>   YUYV
0:00:20.176442026  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:20.176466757  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:20.176458132  1285   0x7f80000d90 INFO                    v4l2 gstv4l2object.c:1311:gst_v4l2_object_fill_format_list:<v4l2src0:src>   MJPG
0:00:20.176481013  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.176536918  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:20.176553217  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:20.176566180  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:20.176577893  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.176593508  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:20.176608490  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:20.176683495  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:20.176697540  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.176715842  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:20.176732026  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:20.176743964  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.176761898  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:20.176788227  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:20.176801851  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:20.176807382  1285   0x7f80000d90 INFO                    v4l2 gstv4l2object.c:4863:gst_v4l2_object_probe_caps:<v4l2src0:src> probed caps: video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 10/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 10/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)320, height=(int)240, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1, 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1, 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)640, height=(int)480, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)320, height=(int)240, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }
0:00:20.176879071  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.176897604  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:20.176913703  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:20.176927436  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:20.176938852  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.176965975  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:20.176982959  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:20.176996164  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to PLAYING
0:00:20.177008170  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.177024103  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 4(PLAYING) successfully
0:00:20.177039199  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:20.177051682  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:20.177067697  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.177084439  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:20.177101523  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to PLAYING
0:00:20.177113317  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.177131839  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 4(PLAYING) successfully
0:00:20.177132782  1285   0x7f80000d90 WARN                 basesrc gstbasesrc.c:3132:gst_base_src_loop:<v4l2src0> error: Internal data stream error.
0:00:20.177146876  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:20.177168907  1285   0x7f80000d90 WARN                 basesrc gstbasesrc.c:3132:gst_base_src_loop:<v4l2src0> error: streaming stopped, reason not-negotiated (-4)
0:00:20.177183975  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.177222307  1285   0x7f80000d90 INFO        GST_ERROR_SYSTEM gstelement.c:2281:gst_element_message_full_with_details:<v4l2src0> posting message: Internal data stream error.
0:00:20.177224128  1285   0x7f80000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:20.177271357  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:20.177285889  1285   0x7f80000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:20.177297232  1285   0x7f80000d90 INFO        GST_ERROR_SYSTEM gstelement.c:2308:gst_element_message_full_with_details:<v4l2src0> posted error message: Internal data stream error.
0:00:20.177459932  1285   0x7f80000d90 INFO                    task gsttask.c:368:gst_task_func:<v4l2src0:src> Task going to paused
0:00:20.177533916  1285   0x7f80000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803e260: got message type 2048 (new-clock)
0:00:20.177635820  1285   0x7f80000d30 WARN               rtspmedia rtsp-media.c:3306:default_handle_message: 0x7f7803e260: got error Internal data stream error. (../libs/gst/base/gstbasesrc.c(3132): gst_base_src_loop (): /GstPipeline:media-pipeline/GstBin:bin0/GstV4l2Src:v4l2src0:
streaming stopped, reason not-negotiated (-4))
0:00:20.177662077  1285   0x7f80000b70 WARN               rtspmedia rtsp-media.c:3634:wait_preroll: failed to preroll pipeline
0:00:20.177674140  1285   0x7f80000b70 WARN               rtspmedia rtsp-media.c:4004:gst_rtsp_media_prepare: failed to preroll pipeline
0:00:20.177687626  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f7803e260
0:00:20.177699290  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f7803e260
0:00:20.177713794  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to NULL for media 0x7f7803e260
0:00:20.177738929  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.177762178  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.177775153  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:20.177787070  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.177811232  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:20.177828367  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.177841446  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:20.177869014  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.177891133  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:20.177906643  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.177923218  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:20.177935144  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.177954994  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:20.177969153  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:20.177980625  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.178000283  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:20.178014207  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.178038548  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.178052196  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:20.178063866  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.178083738  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:20.178098649  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.178111932  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:20.178123467  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.178142095  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:20.178156962  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.178169661  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to PAUSED
0:00:20.178181394  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.178200205  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 3(PAUSED) successfully
0:00:20.178215290  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.178227537  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:20.178238788  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.178257249  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:20.178271392  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.178284522  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to PAUSED
0:00:20.178295970  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.178316446  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 3(PAUSED) successfully without preroll
0:00:20.178329910  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:20.178342089  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.178361272  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:20.178374523  1285   0x7f80000b70 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:20.178389395  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:20.178401170  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:20.178418506  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change PAUSED to READY, final NULL
0:00:20.178439491  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.178473430  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.178493027  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:20.178505425  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.178526032  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:20.178541940  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.178561297  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:20.178573045  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.178592386  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:20.178607475  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.178636289  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:20.178648401  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.178667951  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:20.178694278  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:20.178706478  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.178727297  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:20.178757235  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.178777752  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:20.178789834  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.178809711  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:20.178825535  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.178966052  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:20.178982339  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.179006233  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:20.179024600  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.179094646  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to READY
0:00:20.179108989  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.179134217  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 2(READY) successfully
0:00:20.179151122  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.179176292  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:20.179188504  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.179227324  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:20.179261782  1285   0x7f80000d90 INFO                    task gsttask.c:370:gst_task_func:<v4l2src0:src> Task resume from paused
0:00:20.179330713  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to READY
0:00:20.179345010  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.179368694  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 2(READY) successfully
0:00:20.179386160  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:20.179398301  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.179418500  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:20.179435927  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PAUSED to READY, pending NULL, next NULL
0:00:20.179447690  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to READY (NULL pending)
0:00:20.179465796  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to NULL, final NULL
0:00:20.179488125  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179513468  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179528792  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:20.179540617  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.179561223  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 1(NULL) successfully
0:00:20.179576972  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179592256  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:20.179603679  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.179623115  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 1(NULL) successfully
0:00:20.179638580  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179655158  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:20.179666338  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.179685790  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 1(NULL) successfully
0:00:20.179702095  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to NULL
0:00:20.179713675  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.179733655  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 1(NULL) successfully
0:00:20.179748662  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179772342  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179788384  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to NULL
0:00:20.179800393  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.179820028  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 1(NULL) successfully
0:00:20.179838313  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179853579  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to NULL
0:00:20.179865528  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.179884761  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 1(NULL) successfully
0:00:20.179900530  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179916039  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to NULL
0:00:20.179927899  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.179947827  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 1(NULL) successfully
0:00:20.179964396  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next NULL
0:00:20.179979027  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:20.179990894  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.180010182  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 1(NULL) successfully
0:00:20.180026416  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current READY pending VOID_PENDING, desired next NULL
0:00:20.180150769  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to NULL
0:00:20.180164896  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.180185881  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 1(NULL) successfully
0:00:20.180202736  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to NULL
0:00:20.180214319  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.180234039  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 1(NULL) successfully
0:00:20.180249814  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to NULL
0:00:20.180261391  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.180281264  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:4035:finish_unprepare: Removing elements of stream 0 from pipeline
0:00:20.180293485  1285   0x7f80000b70 INFO              rtspstream rtsp-stream.c:4153:gst_rtsp_stream_leave_bin: stream 0x7f7803f840 leaving bin
0:00:20.180307887  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking bin0:src_0(0x7f7803ea60) and rtpbin0:send_rtp_sink_0(0x7f7c0105f0)
0:00:20.180326979  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:20.180348251  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtp_src_0'
0:00:20.180366000  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:send_rtp_src(0x7f7c00ec90) and send_rtp_src_0:proxypad1(0x7f7c00f630)
0:00:20.180383305  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:20.180402194  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtp_sink'
0:00:20.180416444  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking send_rtp_sink_0:proxypad2(0x7f7c010970) and rtpsession0:send_rtp_sink(0x7f7c00e8b0)
0:00:20.180429803  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:20.180442149  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtp_src'
0:00:20.180467392  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtp_sink_0'
0:00:20.180488074  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'recv_rtcp_sink_0'
0:00:20.180502389  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking recv_rtcp_sink_0:proxypad4(0x7f7c014120) and rtpsession0:recv_rtcp_sink(0x7f7c013360)
0:00:20.180515793  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:20.180528635  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'recv_rtcp_sink'
0:00:20.180540731  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'sync_src'
0:00:20.180553756  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:sync_src(0x7f7c013850) and rtpssrcdemux0:rtcp_sink(0x7f7c00b320)
0:00:20.180569334  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:20.180604081  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtcp_src_0'
0:00:20.180618430  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:send_rtcp_src(0x7f7c012370) and send_rtcp_src_0:proxypad3(0x7f7c012a60)
0:00:20.180633604  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:20.180649374  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtcp_src'
0:00:20.180669793  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:20.180682160  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:20.180694316  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:20.180710175  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpsession0"
0:00:20.180731734  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpsession0> 0x7f7c0040e0 dispose
0:00:20.180743175  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpsession0> 0x7f7c0040e0 parent class dispose
0:00:20.180765457  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpsession0> 0x7f7c0040e0 finalize
0:00:20.180775988  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpsession0> 0x7f7c0040e0 finalize parent
0:00:20.180789387  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpssrcdemux0"
0:00:20.180805717  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpssrcdemux0> 0x7f7c00acd0 dispose
0:00:20.180816170  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux0> removing pad 'sink'
0:00:20.180830287  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux0> removing pad 'rtcp_sink'
0:00:20.180845068  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpssrcdemux0> 0x7f7c00acd0 parent class dispose
0:00:20.180856355  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpssrcdemux0> 0x7f7c00acd0 finalize
0:00:20.180866203  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpssrcdemux0> 0x7f7c00acd0 finalize parent
0:00:20.180880380  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpstorage0"
0:00:20.180901027  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpstorage0> 0x7f7c00c2b0 dispose
0:00:20.180911744  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage0> removing pad 'src'
0:00:20.180927461  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage0> removing pad 'sink'
0:00:20.180943502  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpstorage0> 0x7f7c00c2b0 parent class dispose
0:00:20.180954447  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpstorage0> 0x7f7c00c2b0 finalize
0:00:20.180964336  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpstorage0> 0x7f7c00c2b0 finalize parent
0:00:20.181007052  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "rtpbin0"
0:00:20.181028463  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpbin0> 0x7f78048100 dispose
0:00:20.181039702  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpbin0> 0x7f78048100 parent class dispose
0:00:20.181053379  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpbin0> 0x7f78048100 finalize
0:00:20.181063566  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpbin0> 0x7f78048100 finalize parent
0:00:20.181095568  1285   0x7f80000b70 ERROR             rtspclient rtsp-client.c:1115:find_media: client 0x559d4239a0: can't prepare media
0:00:20.181111207  1285   0x7f80000d30 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f7803fd70
0:00:20.181179007  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:530:gst_rtsp_media_finalize: finalize media 0x7f7803e260
0:00:20.181212616  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "bin0"
0:00:20.181239779  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<media-pipeline> 0x7f7803ff60 dispose
0:00:20.181276021  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<media-pipeline> 0x7f7803ff60 parent class dispose
0:00:20.181287611  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<media-pipeline> 0x7f7803ff60 finalize
0:00:20.181297834  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<media-pipeline> 0x7f7803ff60 finalize parent
0:00:20.181316301  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking v4l2src0:src(0x7f78010160) and capsfilter0:sink(0x7f7803af60)
0:00:20.181336367  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked v4l2src0:src and capsfilter0:sink
0:00:20.181355665  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking capsfilter0:src(0x7f7803b360) and mppjpegdec0:sink(0x7f780132c0)
0:00:20.181371175  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked capsfilter0:src and mppjpegdec0:sink
0:00:20.181384984  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "capsfilter0"
0:00:20.181404872  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<capsfilter0> 0x7f7803ac70 dispose
0:00:20.181415785  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter0> removing pad 'sink'
0:00:20.181432188  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter0> removing pad 'src'
0:00:20.181458582  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<capsfilter0> 0x7f7803ac70 parent class dispose
0:00:20.181471041  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<capsfilter0> 0x7f7803ac70 finalize
0:00:20.181481568  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<capsfilter0> 0x7f7803ac70 finalize parent
0:00:20.181503252  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking pay0:src(0x7f78034dc0) and src_0:proxypad0(0x7f7803ed30)
0:00:20.181519872  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked pay0:src and src_0:proxypad0
0:00:20.181536612  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mpph264enc0:src(0x7f78028910) and pay0:sink(0x7f78035070)
0:00:20.181551636  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mpph264enc0:src and pay0:sink
0:00:20.181566092  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "pay0"
0:00:20.181581696  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<pay0> 0x7f78034ae0 dispose
0:00:20.181591813  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'src'
0:00:20.181606295  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'sink'
0:00:20.181620867  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<pay0> 0x7f78034ae0 parent class dispose
0:00:20.181639605  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<pay0> 0x7f78034ae0 finalize
0:00:20.181649774  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<pay0> 0x7f78034ae0 finalize parent
0:00:20.181665884  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mppjpegdec0:src(0x7f7801e190) and mpph264enc0:sink(0x7f78028680)
0:00:20.181681696  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mppjpegdec0:src and mpph264enc0:sink
0:00:20.181696896  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "mpph264enc0"
0:00:20.181712401  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mpph264enc0> 0x7f78028200 dispose
0:00:20.181723110  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc0> removing pad 'sink'
0:00:20.181737574  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc0> removing pad 'src'
0:00:20.181752529  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mpph264enc0> 0x7f78028200 parent class dispose
0:00:20.181765028  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mpph264enc0> 0x7f78028200 finalize
0:00:20.181775325  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mpph264enc0> 0x7f78028200 finalize parent
0:00:20.181789062  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "mppjpegdec0"
0:00:20.181803984  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mppjpegdec0> 0x7f78011060 dispose
0:00:20.181814574  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mppjpegdec0> removing pad 'sink'
0:00:20.181829251  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mppjpegdec0> removing pad 'src'
0:00:20.181844488  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mppjpegdec0> 0x7f78011060 parent class dispose
0:00:20.181861070  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mppjpegdec0> 0x7f78011060 finalize
0:00:20.181871689  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mppjpegdec0> 0x7f78011060 finalize parent
0:00:20.181889173  1285   0x7f80000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "v4l2src0"
0:00:20.181903866  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<v4l2src0> 0x7f7800da50 dispose
0:00:20.181914801  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<v4l2src0> removing pad 'src'
0:00:20.181931416  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<v4l2src0> 0x7f7800da50 parent class dispose
0:00:20.181961084  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<v4l2src0> 0x7f7800da50 finalize
0:00:20.181972030  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<v4l2src0> 0x7f7800da50 finalize parent
0:00:20.181982428  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<bin0> 0x7f780366b0 dispose
0:00:20.181992331  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<bin0> removing pad 'src_0'
0:00:20.182010531  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<bin0> 0x7f780366b0 parent class dispose
0:00:20.182021279  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<bin0> 0x7f780366b0 finalize
0:00:20.182031243  1285   0x7f80000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<bin0> 0x7f780366b0 finalize parent
0:00:20.182044998  1285   0x7f80000b70 ERROR             rtspclient rtsp-client.c:3412:handle_describe_request: client 0x559d4239a0: no media
0:00:20.183206543  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x559d4239a0: connection closed
0:00:20.183224123  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x559d4239a0: watch destroyed
0:00:20.183257130  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x559d4239a0
0:00:20.183331352  1285   0x7f80000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x559d423f50
0:00:24.283050190  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x559d4239a0 connected to server ip ************, ipv6 = 0
0:00:24.283075894  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x559d4239a0 ip ***********:56057
0:00:24.283130052  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x559d4239a0: attaching to context 0x559d41dac0
0:00:24.283139532  1285   0x7f80000d90 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x559d41da50
0:00:24.290494432  1285   0x7f80000d90 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x559d4239a0: received a request SETUP rtsp://************:8556/stream 1.0
0:00:24.290531357  1285   0x7f80000d90 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x559d418fd0 for path /stream
0:00:24.290558057  1285   0x7f80000d90 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( v4l2src device=/dev/video0 ! image/jpeg,format=MJPG,width=2560,height=720,framerate=30/1 ! mppjpegdec ! mpph264enc profile=baseline ! rtph264pay name=pay0 pt=96 )'
0:00:24.290636582  1285   0x7f80000d90 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "v4l2src"
0:00:24.290699032  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f70003410> adding pad 'src'
0:00:24.290846632  1285   0x7f80000d90 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mppjpegdec"
0:00:24.290891082  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f70006620> adding pad 'sink'
0:00:24.290929657  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f70006620> adding pad 'src'
0:00:24.290997457  1285   0x7f80000d90 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:24.291032307  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f70009640> adding pad 'sink'
0:00:24.291065257  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f70009640> adding pad 'src'
0:00:24.291106557  1285   0x7f80000d90 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:24.291141532  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f7000a550> adding pad 'src'
0:00:24.291172732  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f7000a550> adding pad 'sink'
0:00:24.291208632  1285   0x7f80000d90 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:24.291337832  1285   0x7f80000d90 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstV4l2Src named v4l2src1 to some pad of GstMppJpegDec named mppjpegdec1 (0/0) with caps "image/jpeg, format=(string)MJPG, width=(int)2560, height=(int)720, framerate=(fraction)30/1"
0:00:24.291362407  1285   0x7f80000d90 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:24.291415257  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7000cbb0> adding pad 'sink'
0:00:24.291449057  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7000cbb0> adding pad 'src'
0:00:24.291474432  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin1> getting state
0:00:24.291506682  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to NULL
0:00:24.291532757  1285   0x7f80000d90 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.291577482  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element v4l2src1:(any) to element capsfilter1:sink
0:00:24.291596482  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:sink
0:00:24.291614332  1285   0x7f80000d90 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: v4l2src1 and capsfilter1 in same bin, no need for ghost pads
0:00:24.291652232  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link v4l2src1:src and capsfilter1:sink
0:00:24.291688432  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter1:src> pad has no peer
0:00:24.291721832  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked v4l2src1:src and capsfilter1:sink, successful
0:00:24.291735982  1285   0x7f80000d90 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.291750082  1285   0x7f80000d90 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<v4l2src1:src> Received event on flushing pad. Discarding
0:00:24.291776807  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter1:src to element mppjpegdec1:(any)
0:00:24.291794157  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:src
0:00:24.291829007  1285   0x7f80000d90 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter1:src and mppjpegdec1:sink
0:00:24.291879532  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec1:src> pad has no peer
0:00:24.291940782  1285   0x7f80000d90 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter1 and mppjpegdec1 in same bin, no need for ghost pads
0:00:24.291965007  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter1:src and mppjpegdec1:sink
0:00:24.292005257  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec1:src> pad has no peer
0:00:24.292050532  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter1:src and mppjpegdec1:sink, successful
0:00:24.292064832  1285   0x7f80000d90 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.292078582  1285   0x7f80000d90 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter1:src> Received event on flushing pad. Discarding
0:00:24.292116707  1285   0x7f80000d90 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppJpegDec named mppjpegdec1 to some pad of GstMppH264Enc named mpph264enc1 (0/0) with caps "(NULL)"
0:00:24.292138507  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mppjpegdec1:(any) to element mpph264enc1:(any)
0:00:24.292161157  1285   0x7f80000d90 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mppjpegdec1:src and mpph264enc1:sink
0:00:24.292185707  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:24.292235507  1285   0x7f80000d90 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mppjpegdec1 and mpph264enc1 in same bin, no need for ghost pads
0:00:24.292260132  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mppjpegdec1:src and mpph264enc1:sink
0:00:24.292281407  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:24.292327282  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mppjpegdec1:src and mpph264enc1:sink, successful
0:00:24.292341482  1285   0x7f80000d90 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.292355232  1285   0x7f80000d90 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mppjpegdec1:src> Received event on flushing pad. Discarding
0:00:24.292386832  1285   0x7f80000d90 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc1 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:24.292408682  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc1:(any) to element pay0:(any)
0:00:24.292446407  1285   0x7f80000d90 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc1:src and pay0:sink
0:00:24.292471657  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:24.292503057  1285   0x7f80000d90 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc1 and pay0 in same bin, no need for ghost pads
0:00:24.292526932  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc1:src and pay0:sink
0:00:24.292549307  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:24.292576357  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc1:src and pay0:sink, successful
0:00:24.292609557  1285   0x7f80000d90 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.292624707  1285   0x7f80000d90 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc1:src> Received event on flushing pad. Discarding
0:00:24.292666882  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay0
0:00:24.292692157  1285   0x7f80000d90 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f7000a550
0:00:24.292708682  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:24.292777707  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad5
0:00:24.292798257  1285   0x7f80000d90 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad5, successful
0:00:24.292811457  1285   0x7f80000d90 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.292824832  1285   0x7f80000d90 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:24.292852907  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin1> adding pad 'src_0'
0:00:24.292913757  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay0
0:00:24.292941182  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay0
0:00:24.292965307  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay1
0:00:24.292988832  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay1
0:00:24.293011982  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay1
0:00:24.293041657  1285   0x7f80000d90 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:24.293176607  1285   0x7f80000d90 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f7000fc30 for url /stream
0:00:24.293276482  1285   0x7f80000d90 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f7000fc30
0:00:24.293285999  1285   0x7f80000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f700044f0
0:00:24.293302457  1285   0x7f80000d90 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:24.293462592  1285   0x7f80000b70 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f700106c0 joining bin as session 0
0:00:24.293492823  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:24.293562940  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:24.293592809  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f78049cd0> adding pad 'sink'
0:00:24.293615108  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f78049cd0> adding pad 'rtcp_sink'
0:00:24.293632528  1285   0x7f80000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:24.293677554  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7800c490> adding pad 'src'
0:00:24.293690067  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7800c490> adding pad 'sink'
0:00:24.293785588  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:00:24.293800665  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:00:24.293813231  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:00:24.293850906  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_sink'
0:00:24.293879069  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_src'
0:00:24.293893366  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:send_rtp_src
0:00:24.293940660  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6
0:00:24.293955390  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6, successful
0:00:24.293965721  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.293991400  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_src_0'
0:00:24.294023616  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink
0:00:24.294036855  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink, successful
0:00:24.294046039  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.294063372  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_sink_0'
0:00:24.294082940  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin1:src_0 and rtpbin1:send_rtp_sink_0
0:00:24.294121933  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin1:src_0 and rtpbin1:send_rtp_sink_0, successful
0:00:24.294131936  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.294144378  1285   0x7f80000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:24.294162285  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin1:send_rtp_src_0
0:00:24.294200415  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtcp_src'
0:00:24.294239453  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8
0:00:24.294253360  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8, successful
0:00:24.294262517  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.294281136  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtcp_src_0'
0:00:24.294313822  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'recv_rtcp_sink'
0:00:24.294340286  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'sync_src'
0:00:24.294358345  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:sync_src
0:00:24.294370024  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux1:rtcp_sink
0:00:24.294386232  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink
0:00:24.294398897  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink, successful
0:00:24.294407692  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.294442087  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink
0:00:24.294455969  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink, successful
0:00:24.294464820  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.294480633  1285   0x7f80000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'recv_rtcp_sink_0'
0:00:24.294528001  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f7000fc30
0:00:24.294545211  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f7000fc30
0:00:24.294557365  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f7000fc30
0:00:24.294580631  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current NULL pending VOID_PENDING, desired next READY
0:00:24.294611320  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current NULL pending VOID_PENDING, desired next READY
0:00:24.294623964  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to READY
0:00:24.294636033  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.294659391  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 2(READY) successfully
0:00:24.294674899  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current NULL pending VOID_PENDING, desired next READY
0:00:24.294687061  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to READY
0:00:24.294698777  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.294714608  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 2(READY) successfully
0:00:24.294728965  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current NULL pending VOID_PENDING, desired next READY
0:00:24.294741008  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to READY
0:00:24.294752227  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.294768252  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 2(READY) successfully
0:00:24.294781874  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to READY
0:00:24.294794168  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.294809795  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 2(READY) successfully
0:00:24.294823338  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current NULL pending VOID_PENDING, desired next READY
0:00:24.294854769  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:24.294868251  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:24.294880456  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.294896484  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 2(READY) successfully
0:00:24.294911576  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current NULL pending VOID_PENDING, desired next READY
0:00:24.294925225  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to READY
0:00:24.294936843  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.294952562  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 2(READY) successfully
0:00:24.294967245  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current NULL pending VOID_PENDING, desired next READY
0:00:24.294980621  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to READY
0:00:24.294992230  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.295009021  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 2(READY) successfully
0:00:24.295023499  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current NULL pending VOID_PENDING, desired next READY
0:00:24.295035854  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to READY
0:00:24.295047267  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.295063155  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 2(READY) successfully
0:00:24.295076837  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src1> current NULL pending VOID_PENDING, desired next READY
0:00:24.478521926  1285   0x7f80000b70 INFO                    v4l2 v4l2_calls.c:592:gst_v4l2_open:<v4l2src1:src> Opened device 'FF Camera: FF Camera' (/dev/video0) successfully
0:00:24.478560546  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src1> completed state change to READY
0:00:24.478576514  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.478600570  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'v4l2src1' changed state to 2(READY) successfully
0:00:24.478617526  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to READY
0:00:24.478629616  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.478646219  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 2(READY) successfully
0:00:24.478662442  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:24.478674681  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:24.478687791  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:24.478713435  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.478747771  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.478765990  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PAUSED
0:00:24.478778893  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.478796201  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 3(PAUSED) successfully
0:00:24.478812141  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.478830040  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PAUSED
0:00:24.478842090  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.478857952  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 3(PAUSED) successfully
0:00:24.478872935  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.478889986  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PAUSED
0:00:24.478902372  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.478918374  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 3(PAUSED) successfully
0:00:24.478934526  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PAUSED
0:00:24.478946654  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.478962117  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 3(PAUSED) successfully
0:00:24.478975577  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.479005044  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.479026934  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:24.479039029  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.479054847  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 3(PAUSED) successfully
0:00:24.479070134  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.479330253  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PAUSED
0:00:24.479349051  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.479371021  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 3(PAUSED) successfully
0:00:24.479390183  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.479485496  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to PAUSED
0:00:24.479504384  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.479522628  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 3(PAUSED) successfully
0:00:24.479539315  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.479559557  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PAUSED
0:00:24.479571902  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.479587940  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 3(PAUSED) successfully
0:00:24.479601794  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.479632760  1285   0x7f80000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<v4l2src1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.479661793  1285   0x7f80000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f700037e0 on task 0x7f78050440
0:00:24.479674467  1285   0x7f80000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<v4l2src1:src> created task 0x7f78050440
0:00:24.479768129  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src1> completed state change to PAUSED
0:00:24.479784310  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.479799518  1285   0x7f80000d30 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "v4l2src1"
0:00:24.479828027  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin1> child 'v4l2src1' changed state to 3(PAUSED) successfully without preroll
0:00:24.479848733  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PAUSED
0:00:24.479861200  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.479874209  1285   0x7f80000d30 INFO                 v4l2src gstv4l2src.c:722:gst_v4l2src_query_preferred_size:<v4l2src1> Detect input 0 as `Input 1`
0:00:24.479885134  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 3(PAUSED) successfully without preroll
0:00:24.479903541  1285   0x7f80000b70 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:24.479914677  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:24.479919160  1285   0x7f80000d30 INFO                    v4l2 gstv4l2object.c:1307:gst_v4l2_object_fill_format_list:<v4l2src1:src> got 2 format(s):
0:00:24.479927953  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.479945831  1285   0x7f80000d30 INFO                    v4l2 gstv4l2object.c:1311:gst_v4l2_object_fill_format_list:<v4l2src1:src>   YUYV
0:00:24.479964699  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f7000fc30
0:00:24.479977688  1285   0x7f80000d30 INFO                    v4l2 gstv4l2object.c:1311:gst_v4l2_object_fill_format_list:<v4l2src1:src>   MJPG
0:00:24.479991607  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7000fc30
0:00:24.480057291  1285   0x7f80000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:24.480077918  1285   0x7f80000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:24.480102902  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.480126579  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.480139777  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PLAYING
0:00:24.480151922  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480169952  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:24.480185268  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.480195507  1285   0x7f80000d30 INFO                    v4l2 gstv4l2object.c:4863:gst_v4l2_object_probe_caps:<v4l2src1:src> probed caps: video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 10/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 10/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)320, height=(int)240, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1, 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1, 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)640, height=(int)480, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)320, height=(int)240, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }
0:00:24.480198097  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PLAYING
0:00:24.480256903  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480274874  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:24.480290862  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.480346604  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PLAYING
0:00:24.480361090  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480386570  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:24.480404263  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:24.480390851  1285   0x7f80000d30 WARN                 basesrc gstbasesrc.c:3132:gst_base_src_loop:<v4l2src1> error: Internal data stream error.
0:00:24.480418142  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480434026  1285   0x7f80000d30 WARN                 basesrc gstbasesrc.c:3132:gst_base_src_loop:<v4l2src1> error: streaming stopped, reason not-negotiated (-4)
0:00:24.480456500  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:24.480476092  1285   0x7f80000d30 INFO        GST_ERROR_SYSTEM gstelement.c:2281:gst_element_message_full_with_details:<v4l2src1> posting message: Internal data stream error.
0:00:24.480495698  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.480498594  1285   0x7f80000d30 INFO        GST_ERROR_SYSTEM gstelement.c:2308:gst_element_message_full_with_details:<v4l2src1> posted error message: Internal data stream error.
0:00:24.480511761  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:24.480540906  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480558765  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:24.480575367  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.480589634  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PLAYING
0:00:24.480601753  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480618302  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:24.480620754  1285   0x7f80000d30 INFO                    task gsttask.c:368:gst_task_func:<v4l2src1:src> Task going to paused
0:00:24.480635270  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.480662545  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to PLAYING
0:00:24.480674520  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480691633  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 4(PLAYING) successfully
0:00:24.480706224  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.480718832  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PLAYING
0:00:24.480730360  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480747028  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:24.480764984  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src1> completed state change to PLAYING
0:00:24.480767276  1285   0x7f80000d30 INFO                    task gsttask.c:370:gst_task_func:<v4l2src1:src> Task resume from paused
0:00:24.480778504  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480792153  1285   0x7f80000d30 INFO                    task gsttask.c:368:gst_task_func:<v4l2src1:src> Task going to paused
0:00:24.480812098  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'v4l2src1' changed state to 4(PLAYING) successfully
0:00:24.480836063  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:24.480848089  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.480864576  1285   0x7f80000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:24.480877525  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:24.480889144  1285   0x7f80000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.481116387  1285   0x7f80000b70 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7000fc30: got message type 2048 (new-clock)
0:00:24.481175290  1285   0x7f80000b70 WARN               rtspmedia rtsp-media.c:3306:default_handle_message: 0x7f7000fc30: got error Internal data stream error. (../libs/gst/base/gstbasesrc.c(3132): gst_base_src_loop (): /GstPipeline:media-pipeline/GstBin:bin1/GstV4l2Src:v4l2src1:
streaming stopped, reason not-negotiated (-4))
0:00:24.481204304  1285   0x7f80000d90 WARN               rtspmedia rtsp-media.c:3634:wait_preroll: failed to preroll pipeline
0:00:24.481216981  1285   0x7f80000d90 WARN               rtspmedia rtsp-media.c:4004:gst_rtsp_media_prepare: failed to preroll pipeline
0:00:24.481235574  1285   0x7f80000d90 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f7000fc30
0:00:24.481247824  1285   0x7f80000d90 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f7000fc30
0:00:24.481264387  1285   0x7f80000d90 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to NULL for media 0x7f7000fc30
0:00:24.481290860  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481314340  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481327196  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PAUSED
0:00:24.481339082  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481363830  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 3(PAUSED) successfully
0:00:24.481380322  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481392911  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PAUSED
0:00:24.481404595  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481424464  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 3(PAUSED) successfully
0:00:24.481440077  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481467177  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PAUSED
0:00:24.481479514  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481500472  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 3(PAUSED) successfully
0:00:24.481515436  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PAUSED
0:00:24.481527674  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481546971  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 3(PAUSED) successfully
0:00:24.481560795  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481584548  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481597760  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:24.481609788  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481629241  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 3(PAUSED) successfully
0:00:24.481644138  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481657303  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PAUSED
0:00:24.481669103  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481694737  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 3(PAUSED) successfully
0:00:24.481710334  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481723607  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to PAUSED
0:00:24.481734825  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481754466  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 3(PAUSED) successfully
0:00:24.481769120  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481781674  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PAUSED
0:00:24.481792922  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481811326  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 3(PAUSED) successfully
0:00:24.481824985  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:24.481838437  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src1> completed state change to PAUSED
0:00:24.481849647  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481868546  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin1> child 'v4l2src1' changed state to 3(PAUSED) successfully without preroll
0:00:24.481881539  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PAUSED
0:00:24.481893380  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:24.481912165  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 3(PAUSED) successfully without preroll
0:00:24.481925327  1285   0x7f80000d90 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:24.481938645  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:24.481949940  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:24.481966842  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change PAUSED to READY, final NULL
0:00:24.481987713  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PAUSED pending VOID_PENDING, desired next READY
0:00:24.482022561  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PAUSED pending VOID_PENDING, desired next READY
0:00:24.482041972  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to READY
0:00:24.482053717  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482074883  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 2(READY) successfully
0:00:24.482090419  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PAUSED pending VOID_PENDING, desired next READY
0:00:24.482109940  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to READY
0:00:24.482121566  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482140828  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 2(READY) successfully
0:00:24.482155657  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PAUSED pending VOID_PENDING, desired next READY
0:00:24.482184365  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to READY
0:00:24.482197271  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482217187  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 2(READY) successfully
0:00:24.482242410  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to READY
0:00:24.482254230  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482273317  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 2(READY) successfully
0:00:24.482301667  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next READY
0:00:24.482321255  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:24.482332729  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482357157  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 2(READY) successfully
0:00:24.482372509  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PAUSED pending VOID_PENDING, desired next READY
0:00:24.482519648  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to READY
0:00:24.482536963  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482561058  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 2(READY) successfully
0:00:24.482580406  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current PAUSED pending VOID_PENDING, desired next READY
0:00:24.482649408  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to READY
0:00:24.482663330  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482685721  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 2(READY) successfully
0:00:24.482703281  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PAUSED pending VOID_PENDING, desired next READY
0:00:24.482727243  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to READY
0:00:24.482739669  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482760038  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 2(READY) successfully
0:00:24.482788214  1285   0x7f80000d30 INFO                    task gsttask.c:370:gst_task_func:<v4l2src1:src> Task resume from paused
0:00:24.482832719  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src1> completed state change to READY
0:00:24.482845938  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482866309  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'v4l2src1' changed state to 2(READY) successfully
0:00:24.482884030  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to READY
0:00:24.482896143  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:24.482916044  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 2(READY) successfully
0:00:24.482933629  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PAUSED to READY, pending NULL, next NULL
0:00:24.482945651  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to READY (NULL pending)
0:00:24.482962730  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to NULL, final NULL
0:00:24.482984971  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483009740  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483025045  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:00:24.483037070  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483057208  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 1(NULL) successfully
0:00:24.483073184  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483088370  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:00:24.483100062  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483118719  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 1(NULL) successfully
0:00:24.483133068  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483148729  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:00:24.483160800  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483180080  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 1(NULL) successfully
0:00:24.483195999  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to NULL
0:00:24.483207666  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483226294  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 1(NULL) successfully
0:00:24.483239519  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483262510  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next NULL
0:00:24.483278289  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to NULL
0:00:24.483290009  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483308890  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 1(NULL) successfully
0:00:24.483323543  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483338807  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to NULL
0:00:24.483350557  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483369218  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 1(NULL) successfully
0:00:24.483383644  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483398697  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to NULL
0:00:24.483410158  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483428753  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 1(NULL) successfully
0:00:24.483442915  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483456966  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to NULL
0:00:24.483468774  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483496160  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 1(NULL) successfully
0:00:24.483510177  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src1> current READY pending VOID_PENDING, desired next NULL
0:00:24.483623799  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src1> completed state change to NULL
0:00:24.483637875  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483659464  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'v4l2src1' changed state to 1(NULL) successfully
0:00:24.483676752  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to NULL
0:00:24.483688853  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483707775  1285   0x7f80000d90 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 1(NULL) successfully
0:00:24.483723864  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to NULL
0:00:24.483735837  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:24.483755348  1285   0x7f80000d90 INFO               rtspmedia rtsp-media.c:4035:finish_unprepare: Removing elements of stream 0 from pipeline
0:00:24.483767628  1285   0x7f80000d90 INFO              rtspstream rtsp-stream.c:4153:gst_rtsp_stream_leave_bin: stream 0x7f700106c0 leaving bin
0:00:24.483781875  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking bin1:src_0(0x7f7000fcd0) and rtpbin1:send_rtp_sink_0(0x7f78048100)
0:00:24.483800917  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked bin1:src_0 and rtpbin1:send_rtp_sink_0
0:00:24.483822568  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin1> removing pad 'send_rtp_src_0'
0:00:24.483838168  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession1:send_rtp_src(0x7f7803c740) and send_rtp_src_0:proxypad6(0x7f7803b8c0)
0:00:24.483855200  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6
0:00:24.483873599  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'send_rtp_sink'
0:00:24.483888191  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking send_rtp_sink_0:proxypad7(0x7f780107c0) and rtpsession1:send_rtp_sink(0x7f7800de20)
0:00:24.483901094  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink
0:00:24.483912800  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'send_rtp_src'
0:00:24.483937472  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin1> removing pad 'send_rtp_sink_0'
0:00:24.483958172  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin1> removing pad 'recv_rtcp_sink_0'
0:00:24.483972852  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking recv_rtcp_sink_0:proxypad9(0x7f7801a840) and rtpsession1:recv_rtcp_sink(0x7f7800f620)
0:00:24.483986023  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink
0:00:24.483998265  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'recv_rtcp_sink'
0:00:24.484009688  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'sync_src'
0:00:24.484022778  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession1:sync_src(0x7f7803f540) and rtpssrcdemux1:rtcp_sink(0x7f78049160)
0:00:24.484037996  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink
0:00:24.484074825  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin1> removing pad 'send_rtcp_src_0'
0:00:24.484089292  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession1:send_rtcp_src(0x7f7801e4a0) and send_rtcp_src_0:proxypad8(0x7f78000930)
0:00:24.484105313  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8
0:00:24.484121746  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'send_rtcp_src'
0:00:24.484142784  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:00:24.484155630  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:00:24.484166968  1285   0x7f80000d90 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:00:24.484182732  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin1> removed child "rtpsession1"
0:00:24.484204865  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpsession1> 0x7f78018e70 dispose
0:00:24.484216266  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpsession1> 0x7f78018e70 parent class dispose
0:00:24.484237739  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpsession1> 0x7f78018e70 finalize
0:00:24.484248387  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpsession1> 0x7f78018e70 finalize parent
0:00:24.484262026  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin1> removed child "rtpssrcdemux1"
0:00:24.484279036  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpssrcdemux1> 0x7f78049cd0 dispose
0:00:24.484289591  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux1> removing pad 'sink'
0:00:24.484307250  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux1> removing pad 'rtcp_sink'
0:00:24.484321722  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpssrcdemux1> 0x7f78049cd0 parent class dispose
0:00:24.484333098  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpssrcdemux1> 0x7f78049cd0 finalize
0:00:24.484342930  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpssrcdemux1> 0x7f78049cd0 finalize parent
0:00:24.484356890  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin1> removed child "rtpstorage1"
0:00:24.484378184  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpstorage1> 0x7f7800c490 dispose
0:00:24.484388972  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage1> removing pad 'src'
0:00:24.484404543  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage1> removing pad 'sink'
0:00:24.484421135  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpstorage1> 0x7f7800c490 parent class dispose
0:00:24.484432259  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpstorage1> 0x7f7800c490 finalize
0:00:24.484441707  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpstorage1> 0x7f7800c490 finalize parent
0:00:24.484485251  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "rtpbin1"
0:00:24.484506300  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpbin1> 0x7f700115b0 dispose
0:00:24.484517789  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpbin1> 0x7f700115b0 parent class dispose
0:00:24.484530545  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpbin1> 0x7f700115b0 finalize
0:00:24.484540653  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpbin1> 0x7f700115b0 finalize parent
0:00:24.484603722  1285   0x7f80000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f700044f0
0:00:24.484603007  1285   0x7f80000d90 ERROR             rtspclient rtsp-client.c:1115:find_media: client 0x559d4239a0: can't prepare media
0:00:24.484781707  1285   0x7f80000d90 INFO               rtspmedia rtsp-media.c:530:gst_rtsp_media_finalize: finalize media 0x7f7000fc30
0:00:24.484855757  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "bin1"
0:00:24.484900107  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<media-pipeline> 0x7f70010b10 dispose
0:00:24.484954082  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<media-pipeline> 0x7f70010b10 parent class dispose
0:00:24.484970682  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<media-pipeline> 0x7f70010b10 finalize
0:00:24.484985607  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<media-pipeline> 0x7f70010b10 finalize parent
0:00:24.485013157  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking v4l2src1:src(0x7f70003770) and capsfilter1:sink(0x7f7000d1c0)
0:00:24.485043632  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked v4l2src1:src and capsfilter1:sink
0:00:24.485071857  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking capsfilter1:src(0x7f7000d520) and mppjpegdec1:sink(0x7f70006a80)
0:00:24.485094707  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked capsfilter1:src and mppjpegdec1:sink
0:00:24.485114007  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "capsfilter1"
0:00:24.485139807  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<capsfilter1> 0x7f7000cbb0 dispose
0:00:24.485154982  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter1> removing pad 'sink'
0:00:24.485177632  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter1> removing pad 'src'
0:00:24.485199407  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<capsfilter1> 0x7f7000cbb0 parent class dispose
0:00:24.485215657  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<capsfilter1> 0x7f7000cbb0 finalize
0:00:24.485230107  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<capsfilter1> 0x7f7000cbb0 finalize parent
0:00:24.485255107  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking pay0:src(0x7f7000a830) and src_0:proxypad5(0x7f70010050)
0:00:24.485278132  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked pay0:src and src_0:proxypad5
0:00:24.485303032  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mpph264enc1:src(0x7f70009e20) and pay0:sink(0x7f7000ac10)
0:00:24.485325582  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mpph264enc1:src and pay0:sink
0:00:24.485345932  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "pay0"
0:00:24.485366257  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<pay0> 0x7f7000a550 dispose
0:00:24.485380732  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'src'
0:00:24.485417757  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'sink'
0:00:24.485440457  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<pay0> 0x7f7000a550 parent class dispose
0:00:24.485483182  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<pay0> 0x7f7000a550 finalize
0:00:24.485498557  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<pay0> 0x7f7000a550 finalize parent
0:00:24.485524382  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mppjpegdec1:src(0x7f70006de0) and mpph264enc1:sink(0x7f70009ac0)
0:00:24.485548457  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mppjpegdec1:src and mpph264enc1:sink
0:00:24.485589132  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "mpph264enc1"
0:00:24.485610782  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mpph264enc1> 0x7f70009640 dispose
0:00:24.485625882  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc1> removing pad 'sink'
0:00:24.485646982  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc1> removing pad 'src'
0:00:24.485667957  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mpph264enc1> 0x7f70009640 parent class dispose
0:00:24.485684532  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mpph264enc1> 0x7f70009640 finalize
0:00:24.485698982  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mpph264enc1> 0x7f70009640 finalize parent
0:00:24.485718507  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "mppjpegdec1"
0:00:24.485738607  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mppjpegdec1> 0x7f70006620 dispose
0:00:24.485783207  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mppjpegdec1> removing pad 'sink'
0:00:24.485806057  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mppjpegdec1> removing pad 'src'
0:00:24.485827932  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mppjpegdec1> 0x7f70006620 parent class dispose
0:00:24.485852182  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mppjpegdec1> 0x7f70006620 finalize
0:00:24.485867532  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mppjpegdec1> 0x7f70006620 finalize parent
0:00:24.485890357  1285   0x7f80000d90 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "v4l2src1"
0:00:24.485910807  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<v4l2src1> 0x7f70003410 dispose
0:00:24.485925482  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<v4l2src1> removing pad 'src'
0:00:24.485946382  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<v4l2src1> 0x7f70003410 parent class dispose
0:00:24.485989757  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<v4l2src1> 0x7f70003410 finalize
0:00:24.486005457  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<v4l2src1> 0x7f70003410 finalize parent
0:00:24.486021007  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<bin1> 0x7f7000c340 dispose
0:00:24.486110082  1285   0x7f80000d90 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<bin1> removing pad 'src_0'
0:00:24.486139482  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<bin1> 0x7f7000c340 parent class dispose
0:00:24.486155182  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<bin1> 0x7f7000c340 finalize
0:00:24.486169607  1285   0x7f80000d90 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<bin1> 0x7f7000c340 finalize parent
0:00:24.486191582  1285   0x7f80000d90 ERROR             rtspclient rtsp-client.c:3125:handle_setup_request: client 0x559d4239a0: media '/stream' not found
0:00:24.486288532  1285   0x7f80000d90 INFO              rtspclient rtsp-client.c:5012:closed: client 0x559d4239a0: connection closed
0:00:24.486312132  1285   0x7f80000d90 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x559d4239a0: watch destroyed
0:00:24.486467382  1285   0x7f80000d90 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x559d4239a0
0:00:24.486574332  1285   0x7f80000d90 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x559d41da50
0:00:24.487323658  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x559d425710 connected to server ip ************, ipv6 = 0
0:00:24.487340633  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x559d425710 ip ***********:56058
0:00:24.487376628  1285   0x7f80000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x559d425760
0:00:24.487387614  1285   0x559d333dc0 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x559d425710: attaching to context 0x559d4257d0
0:00:24.488515537  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x559d425710: received a request OPTIONS rtsp://************:8556 1.0
0:00:24.489390168  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x559d425710: connection closed
0:00:24.489408068  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x559d425710: watch destroyed
0:00:24.489439616  1285   0x7f80000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x559d425710
0:00:24.489520534  1285   0x7f80000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x559d425760