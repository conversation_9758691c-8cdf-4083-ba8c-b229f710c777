root@rk3576-buildroot:/# rtsp_server --hw-encoder --gst-debug 4
[2021-01-01 14:37:41.850] [INFO] === RTSP Server Configuration ===
[2021-01-01 14:37:41.850] [INFO] DDS Topic: Video_Frames
[2021-01-01 14:37:41.850] [INFO] Server: 0.0.0.0:8554/stream
[2021-01-01 14:37:41.850] [INFO] Output: Original size@30fps, H264, 2000000 bps
[2021-01-01 14:37:41.850] [INFO] GOP Size: 15
[2021-01-01 14:37:41.850] [INFO] Hardware Encoder: yes
[2021-01-01 14:37:41.850] [INFO] Max Clients: 10
[2021-01-01 14:37:41.850] [INFO] Buffer Size: 5
[2021-01-01 14:37:41.850] [INFO] Zero Copy: yes
[2021-01-01 14:37:41.850] [INFO] GStreamer Debug Level: 4
[2021-01-01 14:37:41.850] [INFO] Adaptive Bitrate: 500000 - 5000000 bps
[2021-01-01 14:37:41.851] [INFO] GStreamer debug level set to: 4 (*:4)
0:00:00.000074745  1400   0x55792738f0 INFO                GST_INIT gst.c:576:init_pre: Initializing GStreamer Core Library version 1.22.9
0:00:00.000091306  1400   0x55792738f0 INFO                GST_INIT gst.c:577:init_pre: Using library installed in /lib
0:00:00.000104425  1400   0x55792738f0 INFO                GST_INIT gst.c:595:init_pre: Linux rk3576-buildroot 6.1.99-rk3576 #1 SMP Mon Jun 30 10:03:13 CST 2025 aarch64
0:00:00.000326573  1400   0x55792738f0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.000825635  1400   0x55792738f0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001046289  1400   0x55792738f0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001144026  1400   0x55792738f0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001158796  1400   0x55792738f0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001220751  1400   0x55792738f0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007083400  1400   0x55792738f0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.005826 seconds
0:00:00.007152210  1400   0x55792738f0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007771238  1400   0x55792738f0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.007788325  1400   0x55792738f0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.007802082  1400   0x55792738f0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.007811371  1400   0x55792738f0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.007818356  1400   0x55792738f0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
[2021-01-01 14:37:41.858] [INFO] GStreamer initialized successfully
[2021-01-01 14:37:41.859] [INFO] Media factory configured: shared=FALSE, eos_shutdown=TRUE
[2021-01-01 14:37:41.859] [INFO] Supported protocols: UDP, UDP_MCAST, TCP
DDS Reader initialized for topic: Video_Frames
[2021-01-01 14:37:41.864] [INFO] Waiting for first frame from DDS topic: Video_Frames
Subscriber matched
[2021-01-01 14:37:42.331] [INFO] First frame received: 1280x720 format=1196444237, output will be: 1280x720@30fps
[2021-01-01 14:37:42.332] [INFO] Creating pipeline for format: 0x47504a4d (1280x720@30fps)
[2021-01-01 14:37:42.332] [INFO] Generated pipeline: ( appsrc name=source is-live=true do-timestamp=false format=time caps="image/jpeg,format=MJPG,width=1280,height=720,framerate=30/1" ! mppjpegdec ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )
[2021-01-01 14:37:42.332] [INFO] RTSPMediaFactory initialized for topic: Video_Frames
[2021-01-01 14:37:42.332] [INFO] Pipeline: ( appsrc name=source is-live=true do-timestamp=false format=time caps="image/jpeg,format=MJPG,width=1280,height=720,framerate=30/1" ! mppjpegdec ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )
0:00:00.481052928  1400   0x55792738f0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x5579353360 for path /stream

(<unknown>:1400): GLib-GObject-CRITICAL **: 14:37:42.332: g_object_set_is_valid_property: object class 'GstRTSPServer' has no property named 'timeout'
[2021-01-01 14:37:42.332] [INFO] RTSP server initialized: 0.0.0.0:8554/stream
0:00:00.481221949  1400   0x55792738f0 DEBUG             rtspserver rtsp-server.c:882:gst_rtsp_server_create_socket:<GstRTSPServer@0x5579351050> getting address info of 0.0.0.0/8554
0:00:00.481793917  1400   0x55792738f0 DEBUG             rtspserver rtsp-server.c:967:gst_rtsp_server_create_socket:<GstRTSPServer@0x5579351050> opened sending server socket
0:00:00.481834487  1400   0x55792738f0 DEBUG             rtspserver rtsp-server.c:994:gst_rtsp_server_create_socket:<GstRTSPServer@0x5579351050> listening on server socket 0x55794a3100 with queue of 5
[2021-01-01 14:37:42.333] [INFO] RTSP server started on 0.0.0.0:8554/stream
[2021-01-01 14:37:42.333] [INFO] RTSP server is running. Access stream at: rtsp://0.0.0.0:8554/stream
[2021-01-01 14:37:42.333] [INFO] Press Ctrl+C to stop the server
[2021-01-01 14:37:42.333] [INFO] RTSP server main loop started
[2021-01-01 14:37:52.333] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:37:52.333] [INFO] Uptime: 10.0 seconds
[2021-01-01 14:37:52.333] [INFO] Total connections: 0
[2021-01-01 14:37:52.333] [INFO] Active connections: 0
[2021-01-01 14:37:52.333] [INFO] Frames served: 0
[2021-01-01 14:37:52.333] [INFO] Clients connected: 0
[2021-01-01 14:37:52.333] [INFO] Error count: 0
[2021-01-01 14:38:02.334] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:38:02.334] [INFO] Uptime: 20.0 seconds
[2021-01-01 14:38:02.334] [INFO] Total connections: 0
[2021-01-01 14:38:02.334] [INFO] Active connections: 0
[2021-01-01 14:38:02.334] [INFO] Frames served: 0
[2021-01-01 14:38:02.334] [INFO] Clients connected: 0
[2021-01-01 14:38:02.334] [INFO] Error count: 0
[2021-01-01 14:38:12.335] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:38:12.335] [INFO] Uptime: 30.0 seconds
[2021-01-01 14:38:12.335] [INFO] Total connections: 0
[2021-01-01 14:38:12.335] [INFO] Active connections: 0
[2021-01-01 14:38:12.335] [INFO] Frames served: 0
[2021-01-01 14:38:12.335] [INFO] Clients connected: 0
[2021-01-01 14:38:12.335] [INFO] Error count: 0
[2021-01-01 14:38:22.335] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:38:22.335] [INFO] Uptime: 40.0 seconds
[2021-01-01 14:38:22.335] [INFO] Total connections: 0
[2021-01-01 14:38:22.335] [INFO] Active connections: 0
[2021-01-01 14:38:22.335] [INFO] Frames served: 0
[2021-01-01 14:38:22.336] [INFO] Clients connected: 0
[2021-01-01 14:38:22.336] [INFO] Error count: 0
[2021-01-01 14:38:32.336] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:38:32.336] [INFO] Uptime: 50.0 seconds
[2021-01-01 14:38:32.336] [INFO] Total connections: 0
[2021-01-01 14:38:32.336] [INFO] Active connections: 0
[2021-01-01 14:38:32.336] [INFO] Frames served: 0
[2021-01-01 14:38:32.336] [INFO] Clients connected: 0
[2021-01-01 14:38:32.336] [INFO] Error count: 0
0:00:57.513245451  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f68006630 connected to server ip ************, ipv6 = 0
0:00:57.513274896  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f68006630 ip ***********:60126
0:00:57.513292313  1400   0x7f68000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x5579351050> manage client 0x7f68006630
[2021-01-01 14:38:39.364] [INFO] === CLIENT CONNECTED ===
[2021-01-01 14:38:39.364] [INFO] Active connections: 1, Total connections: 1
0:00:57.513660229  1400   0x7f60000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f68006f10
0:00:57.513672533  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f68006630: attaching to context 0x7f68007440
0:00:57.513999229  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f68006630: received a request OPTIONS rtsp://************:8554/stream 1.0
0:00:57.817924004  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f68006630: received a request DESCRIBE rtsp://************:8554/stream 1.0
0:00:57.818018504  1400   0x7f60000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x5579353360 for path /stream
0:00:57.818045429  1400   0x7f60000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=false format=time caps="image/jpeg,format=MJPG,width=1280,height=720,framerate=30/1" ! mppjpegdec ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:57.818862679  1400   0x7f60000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstapp.so" loaded
0:00:57.819286854  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:57.819345204  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f64007460> adding pad 'src'
0:00:57.819398304  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1
0:00:57.822813004  1400   0x7f60000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:57.823146379  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mppjpegdec"
0:00:57.823201604  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f6400bf40> adding pad 'sink'
0:00:57.823236429  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f6400bf40> adding pad 'src'
0:00:57.823566579  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:57.823608029  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f64017e80> adding pad 'sink'
0:00:57.823638854  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f64017e80> adding pad 'src'
0:00:57.825851979  1400   0x7f60000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:00:57.826049804  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:57.826106179  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f6401be90> adding pad 'sink'
0:00:57.826139729  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f6401be90> adding pad 'src'
0:00:57.826187779  1400   0x7f60000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f6401be90> PTS interpolation: no
0:00:57.826210404  1400   0x7f60000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f6401be90> TS inferring: no
0:00:57.828967029  1400   0x7f60000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:57.829292929  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:57.829369779  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f6402b9e0> adding pad 'src'
0:00:57.829442354  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f6402b9e0> adding pad 'sink'
0:00:57.829509979  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:57.829644229  1400   0x7f60000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstMppJpegDec named mppjpegdec0 (0/0) with caps "(NULL)"
0:00:57.829671204  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element mppjpegdec0:(any)
0:00:57.829698429  1400   0x7f60000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and mppjpegdec0:sink
0:00:57.829727204  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:57.829779304  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec0:src> pad has no peer
0:00:57.829865504  1400   0x7f60000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and mppjpegdec0 in same bin, no need for ghost pads
0:00:57.829902929  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and mppjpegdec0:sink
0:00:57.829925254  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:57.829946554  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec0:src> pad has no peer
0:00:57.829995829  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and mppjpegdec0:sink, successful
0:00:57.830013854  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.830030429  1400   0x7f60000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:57.830069629  1400   0x7f60000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppJpegDec named mppjpegdec0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "(NULL)"
0:00:57.830094029  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mppjpegdec0:(any) to element mpph264enc0:(any)
0:00:57.830118629  1400   0x7f60000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mppjpegdec0:src and mpph264enc0:sink
0:00:57.830147279  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:57.830217879  1400   0x7f60000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mppjpegdec0 and mpph264enc0 in same bin, no need for ghost pads
0:00:57.830249204  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mppjpegdec0:src and mpph264enc0:sink
0:00:57.830277829  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:57.830329354  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mppjpegdec0:src and mpph264enc0:sink, successful
0:00:57.830349329  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.830364529  1400   0x7f60000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mppjpegdec0:src> Received event on flushing pad. Discarding
0:00:57.830400154  1400   0x7f60000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:00:57.830423929  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:00:57.830448879  1400   0x7f60000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:00:57.830477279  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:57.830506004  1400   0x7f60000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:00:57.830534354  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:00:57.830558879  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:57.830586654  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:00:57.830603504  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.830620529  1400   0x7f60000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:57.830655829  1400   0x7f60000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:57.830679429  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:00:57.830703554  1400   0x7f60000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:00:57.830735029  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:57.830760654  1400   0x7f60000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:00:57.830785879  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:00:57.830811879  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:57.830834804  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:00:57.830851079  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.830865304  1400   0x7f60000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:00:57.831094029  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:57.831125504  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f6402b9e0
0:00:57.831148629  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:57.831194829  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f64030720: creating stream with index 0 and payloader <pay0>
0:00:57.831425679  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:57.831453379  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:57.831470154  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.831485829  1400   0x7f60000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:57.831518254  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:57.831605604  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f64032000
0:00:57.831630779  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f64032000> set retransmission time 0
0:00:57.831654179  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f64032000> Enabling rate control
0:00:57.831693129  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f64032000> set retransmission pt 97
0:00:57.831719254  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:57.831749229  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:57.831773804  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:57.831797629  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:57.831821929  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:57.831854579  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:57.832017279  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f64032000> set retransmission time 0
[2021-01-01 14:38:39.683] [INFO] factory: 0x5579353360, media: 0x7f64030720, user_data: 0x5579312520
[2021-01-01 14:38:39.683] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 14:38:39.683] [INFO] Got media pipeline: 0x7f6402b750
0:00:57.832126579  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element source
[2021-01-01 14:38:39.683] [INFO] Found appsrc element: 0x7f64007460
[2021-01-01 14:38:39.683] [INFO] Connected appsrc signals
0:00:57.832208854  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:57.832239129  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:57.832261204  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 14:38:39.683] [INFO] Appsrc configured, waiting for need-data callback to start data flow
[2021-01-01 14:38:39.683] [INFO] Media configured successfully
[2021-01-01 14:38:39.683] [INFO] configure_media call completed
0:00:57.832346304  1400   0x7f60000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f64030720 for url /stream
0:00:57.832525279  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f64030720
0:00:57.832543604  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:57.832610479  1400   0x7f60000d80 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f64032f30
0:00:57.834753479  1400   0x7f60000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:57.834796804  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:57.835552379  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:57.835578029  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:57.835605979  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:57.835641879  1400   0x7f60000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:57.835707654  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:57.835739254  1400   0x7f60000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:57.835785679  1400   0x7f60000d80 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f64032000 joining bin as session 0
0:00:57.835843629  1400   0x7f60000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:57.836565004  1400   0x7f60000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:57.836743654  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f5800c380> adding pad 'sink'
0:00:57.836782629  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f5800c380> adding pad 'rtcp_sink'
0:00:57.836812354  1400   0x7f60000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:57.836941554  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f5800d800> adding pad 'src'
0:00:57.836966104  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f5800d800> adding pad 'sink'
0:00:57.837151654  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:57.837178129  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:57.837201429  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:57.837272379  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:57.837326979  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:57.837353629  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:57.837433929  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:57.837461429  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:57.837481304  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.837527879  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:57.837576129  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:57.837601779  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:57.837620754  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.837645779  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:57.837677379  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:57.837748354  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:57.837765704  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.837785229  1400   0x7f60000d80 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:57.837815129  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:57.837891779  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:57.837953404  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:57.837978479  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:57.837997104  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.838027554  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:57.838085854  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:57.838131229  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:57.838162129  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:57.838184929  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:57.838210479  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:57.838235404  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:57.838251404  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.838304954  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:57.838335379  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:57.838349479  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:57.838373679  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:57.838434704  1400   0x7f60000d80 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f64032000> successfully joined bin
0:00:57.838470179  1400   0x7f60000d80 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f64030720
0:00:57.838488479  1400   0x7f60000d80 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f64030720 set blocked 1
0:00:57.838506779  1400   0x7f60000d80 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f64032000> blocked: 1
0:00:57.838529529  1400   0x7f60000d80 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f64030720
0:00:57.838551154  1400   0x7f60000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f64030720
0:00:57.838591729  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:57.838640404  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:57.838660604  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:57.838678979  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.838720654  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:57.838749079  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:57.838769879  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:57.838790904  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.838818854  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:57.838842904  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:57.838866129  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:57.838887979  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.838916129  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:57.838940704  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:57.838959979  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.838992854  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:57.839018404  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:57.839063004  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:57.839083329  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:57.839100679  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.839129429  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:57.839153304  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:00:57.839176504  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:57.839200654  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.839227304  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:57.839254354  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:57.839276104  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:57.839295379  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.839323129  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:57.839349854  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current NULL pending VOID_PENDING, desired next READY
0:00:57.839373479  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to READY
0:00:57.839393604  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.839421354  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 2(READY) successfully
0:00:57.839446504  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:57.839467204  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:57.839486629  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.839514879  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:57.839537354  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:57.839560479  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:57.839588729  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:57.839614679  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:57.839637504  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:57.839662929  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:57.839696104  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.839738479  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.839769479  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:57.839792354  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.839820879  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:57.839848054  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.839878054  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:57.839900479  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.839955329  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:57.839985379  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.840011829  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:57.840029654  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.840052379  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:57.840076254  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:57.840093079  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.840126279  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:57.840146329  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.840182504  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.840212879  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:57.840230054  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.840252904  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:57.840273954  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.840621454  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:57.840642654  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.840668454  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:57.840691254  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.841242829  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:57.841273404  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.841308779  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:57.841339204  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.841497129  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to PAUSED
0:00:57.841517504  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.841546929  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 3(PAUSED) successfully
0:00:57.841568779  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:57.841592354  1400   0x7f60000d80 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:57.841643554  1400   0x7f60000d80 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:57.841706104  1400   0x7f60000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f640077d0 on task 0x7f58024b40
0:00:57.841727279  1400   0x7f60000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f58024b40
0:00:57.841912704  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:57.841935004  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.841990754  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:57.842003012  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:57.842020154  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:57.842033683  1400   0x7f60000de0 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:57.842038829  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.842080829  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:57.842105954  1400   0x7f60000d80 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:57.842123454  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:57.842140079  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:57.842163179  1400   0x7f60000d80 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f64030720
0:00:57.842182354  1400   0x7f60000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f64030720
0:00:57.842259254  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:57.842288529  1400   0x7f60000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:57.842324504  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:57.842357554  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:57.842375579  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:57.842392354  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.842417404  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:57.842439454  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:57.842457304  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:57.842473854  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.842498654  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:57.842520629  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:57.842607729  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:57.842626979  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.842674179  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:57.842694354  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:57.842711229  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.842735404  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:57.842773729  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:57.842793229  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:57.842809879  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.842832979  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:57.842854254  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:57.842872129  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:00:57.842888904  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.842911504  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:57.842932904  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:57.842951229  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:57.842967679  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.842993379  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:57.843017929  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:57.843036504  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to PLAYING
0:00:57.843052879  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.843089804  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 4(PLAYING) successfully
0:00:57.843116529  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:57.843136979  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.843165188  1400   0x7f60000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1
0:00:57.843166204  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:57.843206429  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:57.843212051  1400   0x7f60000de0 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec0:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:00:57.843227279  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.843271104  1400   0x7f60000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:57.843290479  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:57.843307604  1400   0x7f60000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:57.843360701  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f50023c80
0:00:57.843395330  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 140256 bytes, 1 buffers, 0:00:00.033333333
0:00:57.843416823  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:57.843444457  1400   0x7f60000de0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:57.843487329  1400   0x7f60000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f64030720: went from NULL to READY (pending PAUSED)
0:00:57.843489319  1400   0x7f60000de0 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec0:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:00:57.843526589  1400   0x7f60000de0 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
0:00:57.843552990  1400   0x7f60000de0 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec0:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:00:57.843577589  1400   0x7f60000de0 WARN                 basesrc gstbasesrc.c:3132:gst_base_src_loop:<source> error: Internal data stream error.
0:00:57.843588909  1400   0x7f60000de0 WARN                 basesrc gstbasesrc.c:3132:gst_base_src_loop:<source> error: streaming stopped, reason not-negotiated (-4)
0:00:57.843621921  1400   0x7f60000de0 INFO        GST_ERROR_SYSTEM gstelement.c:2281:gst_element_message_full_with_details:<source> posting message: Internal data stream error.
0:00:57.843662220  1400   0x7f60000de0 INFO        GST_ERROR_SYSTEM gstelement.c:2308:gst_element_message_full_with_details:<source> posted error message: Internal data stream error.
0:00:57.843677329  1400   0x7f60000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f64030720: went from READY to PAUSED (pending VOID_PENDING)
0:00:57.843698159  1400   0x7f60000de0 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec0:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:00:57.843716304  1400   0x7f60000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f64030720: got message type 2048 (new-clock)
0:00:57.843726007  1400   0x7f60000de0 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec0:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:00:57.843764614  1400   0x7f60000de0 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec0:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:00:57.843855904  1400   0x7f60000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f64030720: went from PAUSED to PLAYING (pending VOID_PENDING)
0:00:57.843879545  1400   0x7f60000de0 INFO                    task gsttask.c:368:gst_task_func:<source:src> Task going to paused
0:00:57.843920054  1400   0x7f60000d80 WARN               rtspmedia rtsp-media.c:3306:default_handle_message: 0x7f64030720: got error Internal data stream error. (../libs/gst/base/gstbasesrc.c(3132): gst_base_src_loop (): /GstPipeline:media-pipeline/GstBin:bin0/GstAppSrc:source:
streaming stopped, reason not-negotiated (-4))
0:00:57.843947404  1400   0x7f60000d80 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 5
0:00:57.843976579  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 5
0:00:57.843990629  1400   0x7f60000b70 WARN               rtspmedia rtsp-media.c:3634:wait_preroll: failed to preroll pipeline
0:00:57.844001454  1400   0x7f60000b70 WARN               rtspmedia rtsp-media.c:4004:gst_rtsp_media_prepare: failed to preroll pipeline
0:00:57.844015704  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f64030720
0:00:57.844031329  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f64030720
0:00:57.844046929  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:57.844060304  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:4022:finish_unprepare: shutting down
0:00:57.844073179  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to NULL for media 0x7f64030720
0:00:57.844110054  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844143804  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844166129  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:57.844183004  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844216429  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:57.844239654  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844257279  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:57.844273629  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844300479  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:57.844322729  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844345054  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:57.844361479  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844392129  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:57.844415454  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:57.844432229  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844458654  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:57.844478929  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844513354  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844532204  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:57.844548679  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844575754  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:57.844597354  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844615029  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:57.844631029  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844657804  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:57.844679854  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844697979  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:57.844714029  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844746129  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:57.844772854  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844790254  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to PAUSED
0:00:57.844805854  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844834079  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 3(PAUSED) successfully
0:00:57.844858229  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:57.844881279  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:57.844899479  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844927854  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:57.844946179  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:57.844962029  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:57.844988079  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:57.845008304  1400   0x7f60000b70 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:57.845029729  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:57.845048579  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:57.845079079  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change PAUSED to READY, final NULL
0:00:57.845093679  1400   0x7f60000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f64030720: went from PLAYING to PAUSED (pending NULL)
0:00:57.845118879  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next READY
0:00:57.845188379  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next READY
0:00:57.845222204  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:57.845242429  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.845275504  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:57.845303429  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next READY
0:00:57.845334779  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:57.845356954  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.845388779  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:57.845413404  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next READY
0:00:57.845457304  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:57.845477579  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.845508629  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:57.845550254  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:57.845570729  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.845602204  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:57.845647354  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next READY
0:00:57.845679229  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:57.845700254  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.845733354  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:57.845758054  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next READY
0:00:57.845934154  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:57.845958004  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.846014904  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:57.846043354  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next READY
0:00:57.846248404  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:57.846274229  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.846308954  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:57.846337604  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current PAUSED pending VOID_PENDING, desired next READY
0:00:57.846438654  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to READY
0:00:57.846463129  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.846497229  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 2(READY) successfully
0:00:57.846527704  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:1052:gst_app_src_unlock:<source> unlock start
0:00:57.846549803  1400   0x7f60000de0 INFO                    task gsttask.c:370:gst_task_func:<source:src> Task resume from paused
0:00:57.846592754  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:1067:gst_app_src_unlock_stop:<source> unlock stop
0:00:57.846609279  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:1105:gst_app_src_stop:<source> stopping
0:00:57.846644454  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:57.846662129  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.846691454  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:57.846717829  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:57.846739379  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:57.846766904  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:57.846792279  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PAUSED to READY, pending NULL, next NULL
0:00:57.846809404  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to READY (NULL pending)
0:00:57.846836004  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to NULL, final NULL
0:00:57.846852554  1400   0x7f60000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f64030720: went from PAUSED to READY (pending NULL)
0:00:57.846873779  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next NULL
0:00:57.846914954  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next NULL
0:00:57.846936729  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:57.846953579  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.846983929  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 1(NULL) successfully
0:00:57.847010829  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next NULL
0:00:57.847037604  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:57.847054429  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847083204  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 1(NULL) successfully
0:00:57.847111354  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next NULL
0:00:57.847140704  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:57.847162179  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847193479  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 1(NULL) successfully
0:00:57.847221554  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to NULL
0:00:57.847242929  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847273279  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 1(NULL) successfully
0:00:57.847296179  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next NULL
0:00:57.847333704  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next NULL
0:00:57.847361979  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to NULL
0:00:57.847382679  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847413479  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 1(NULL) successfully
0:00:57.847438929  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next NULL
0:00:57.847465704  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to NULL
0:00:57.847485554  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847516354  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 1(NULL) successfully
0:00:57.847542279  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next NULL
0:00:57.847567904  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to NULL
0:00:57.847587954  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847619154  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 1(NULL) successfully
0:00:57.847644129  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current READY pending VOID_PENDING, desired next NULL
0:00:57.847668779  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to NULL
0:00:57.847689904  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847720429  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 1(NULL) successfully
0:00:57.847744179  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next NULL
0:00:57.847769054  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to NULL
0:00:57.847789129  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847825554  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 1(NULL) successfully
0:00:57.847852254  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to NULL
0:00:57.847872129  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.847902479  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 1(NULL) successfully
0:00:57.861213254  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to NULL
0:00:57.861238554  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:57.861269054  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f64030720 set blocked 0
0:00:57.861289679  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f64032000> blocked: 0
0:00:57.861310629  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:4035:finish_unprepare: Removing elements of stream 0 from pipeline
0:00:57.861328629  1400   0x7f60000b70 INFO              rtspstream rtsp-stream.c:4153:gst_rtsp_stream_leave_bin: stream 0x7f64032000 leaving bin
0:00:57.861349754  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking bin0:src_0(0x7f64031160) and rtpbin0:send_rtp_sink_0(0x7f58010f30)
0:00:57.861380379  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:57.861415229  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtp_src_0'
0:00:57.861438204  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:send_rtp_src(0x7f58010070) and send_rtp_src_0:proxypad1(0x7f58010790)
0:00:57.861468829  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:57.861502754  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtp_sink'
0:00:57.861531004  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking send_rtp_sink_0:proxypad2(0x7f580112b0) and rtpsession0:send_rtp_sink(0x7f5800fca0)
0:00:57.861554154  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:57.861576354  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtp_src'
0:00:57.861620404  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtp_sink_0'
0:00:57.861656954  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'recv_rtcp_sink_0'
0:00:57.861681029  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking recv_rtcp_sink_0:proxypad4(0x7f58013b40) and rtpsession0:recv_rtcp_sink(0x7f58012e40)
0:00:57.861705929  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:57.861727954  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'recv_rtcp_sink'
0:00:57.861750404  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'sync_src'
0:00:57.861772729  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:sync_src(0x7f58013320) and rtpssrcdemux0:rtcp_sink(0x7f5800c920)
0:00:57.861801229  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:57.861859079  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtcp_src_0'
0:00:57.861884004  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:send_rtcp_src(0x7f58011f10) and send_rtcp_src_0:proxypad3(0x7f58012640)
0:00:57.861912629  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:57.861942804  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtcp_src'
0:00:57.861979379  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:57.862003329  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:57.862023529  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:57.862053254  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpsession0"
0:00:57.862090829  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpsession0> 0x7f580059f0 dispose
0:00:57.862110579  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpsession0> 0x7f580059f0 parent class dispose
0:00:57.862142754  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpsession0> 0x7f580059f0 finalize
0:00:57.862161104  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpsession0> 0x7f580059f0 finalize parent
0:00:57.862182179  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpssrcdemux0"
0:00:57.862209204  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpssrcdemux0> 0x7f5800c380 dispose
0:00:57.862227354  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux0> removing pad 'sink'
0:00:57.862249229  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux0> removing pad 'rtcp_sink'
0:00:57.862271354  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpssrcdemux0> 0x7f5800c380 parent class dispose
0:00:57.862290579  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpssrcdemux0> 0x7f5800c380 finalize
0:00:57.862305604  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpssrcdemux0> 0x7f5800c380 finalize parent
0:00:57.862326704  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpstorage0"
0:00:57.862354879  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpstorage0> 0x7f5800d800 dispose
0:00:57.862373454  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage0> removing pad 'src'
0:00:57.862398179  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage0> removing pad 'sink'
0:00:57.862422529  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpstorage0> 0x7f5800d800 parent class dispose
0:00:57.862441179  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpstorage0> 0x7f5800d800 finalize
0:00:57.862456679  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpstorage0> 0x7f5800d800 finalize parent
0:00:57.862517404  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "rtpbin0"
0:00:57.862550304  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpbin0> 0x7f6403aaa0 dispose
0:00:57.862569329  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpbin0> 0x7f6403aaa0 parent class dispose
0:00:57.862592429  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpbin0> 0x7f6403aaa0 finalize
0:00:57.862610504  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpbin0> 0x7f6403aaa0 finalize parent
0:00:57.862627479  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 0
0:00:57.862648804  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:4078:finish_unprepare: removing bus watch
0:00:57.862664979  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:3421:watch_destroyed:<GstRTSPMedia@0x7f64030720> source destroyed
0:00:57.862687554  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:4083:finish_unprepare: destroy source
0:00:57.862722204  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:4089:finish_unprepare: stop thread
0:00:57.862748029  1400   0x7f60000b70 ERROR             rtspclient rtsp-client.c:1115:find_media: client 0x7f68006630: can't prepare media
0:00:57.862763029  1400   0x7f60000d80 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f64032f30
0:00:57.862878079  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:530:gst_rtsp_media_finalize: finalize media 0x7f64030720
0:00:57.862903804  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:390:gst_rtsp_stream_finalize: finalize stream 0x7f64032000
0:00:57.862952154  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "bin0"
0:00:57.862995779  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<media-pipeline> 0x7f64032740 dispose
0:00:57.863054854  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<media-pipeline> 0x7f64032740 parent class dispose
0:00:57.863077554  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<media-pipeline> 0x7f64032740 finalize
0:00:57.863095604  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<media-pipeline> 0x7f64032740 finalize parent
0:00:57.863126154  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking pay0:src(0x7f6402bcc0) and src_0:proxypad0(0x7f640314e0)
0:00:57.863159554  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked pay0:src and src_0:proxypad0
0:00:57.863188129  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking h264parse0:src(0x7f6401d040) and pay0:sink(0x7f6402c010)
0:00:57.863218729  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked h264parse0:src and pay0:sink
0:00:57.863242354  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "pay0"
0:00:57.863267304  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<pay0> 0x7f6402b9e0 dispose
0:00:57.863283829  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'src'
0:00:57.863309529  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'sink'
0:00:57.863332804  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<pay0> 0x7f6402b9e0 parent class dispose
0:00:57.863361829  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<pay0> 0x7f6402b9e0 finalize
0:00:57.863380504  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<pay0> 0x7f6402b9e0 finalize parent
0:00:57.863407454  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mpph264enc0:src(0x7f64018550) and h264parse0:sink(0x7f6401cce0)
0:00:57.863437579  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mpph264enc0:src and h264parse0:sink
0:00:57.863464229  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "h264parse0"
0:00:57.863489579  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<h264parse0> 0x7f6401be90 dispose
0:00:57.863507804  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<h264parse0> removing pad 'sink'
0:00:57.863533029  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<h264parse0> removing pad 'src'
0:00:57.863555579  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<h264parse0> 0x7f6401be90 parent class dispose
0:00:57.863595804  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<h264parse0> 0x7f6401be90 finalize
0:00:57.863615254  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<h264parse0> 0x7f6401be90 finalize parent
0:00:57.863642754  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mppjpegdec0:src(0x7f6400c630) and mpph264enc0:sink(0x7f64018300)
0:00:57.863672854  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mppjpegdec0:src and mpph264enc0:sink
0:00:57.863698779  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "mpph264enc0"
0:00:57.863720379  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mpph264enc0> 0x7f64017e80 dispose
0:00:57.863738604  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc0> removing pad 'sink'
0:00:57.863764629  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc0> removing pad 'src'
0:00:57.863789254  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mpph264enc0> 0x7f64017e80 parent class dispose
0:00:57.863812329  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mpph264enc0> 0x7f64017e80 finalize
0:00:57.863828129  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mpph264enc0> 0x7f64017e80 finalize parent
0:00:57.863870079  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking source:src(0x7f64007760) and mppjpegdec0:sink(0x7f6400c3a0)
0:00:57.863897679  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked source:src and mppjpegdec0:sink
0:00:57.863937754  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "mppjpegdec0"
0:00:57.863963029  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mppjpegdec0> 0x7f6400bf40 dispose
0:00:57.863981004  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mppjpegdec0> removing pad 'sink'
0:00:57.864003479  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mppjpegdec0> removing pad 'src'
0:00:57.864026329  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mppjpegdec0> 0x7f6400bf40 parent class dispose
0:00:57.864053704  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mppjpegdec0> 0x7f6400bf40 finalize
0:00:57.864069979  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mppjpegdec0> 0x7f6400bf40 finalize parent
0:00:57.864093829  1400   0x7f60000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "source"
0:00:57.864120329  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<source> 0x7f64007460 dispose
0:00:57.864136729  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<source> removing pad 'src'
0:00:57.864159379  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<source> 0x7f64007460 parent class dispose
0:00:57.864184029  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<source> 0x7f64007460 finalize
0:00:57.864199804  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<source> 0x7f64007460 finalize parent
0:00:57.864215529  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<bin0> 0x7f6402b750 dispose
0:00:57.864230429  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<bin0> removing pad 'src_0'
0:00:57.864258854  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<bin0> 0x7f6402b750 parent class dispose
0:00:57.864277729  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<bin0> 0x7f6402b750 finalize
0:00:57.864292854  1400   0x7f60000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<bin0> 0x7f6402b750 finalize parent
0:00:57.864314079  1400   0x7f60000b70 ERROR             rtspclient rtsp-client.c:3412:handle_describe_request: client 0x7f68006630: no media
0:00:57.864974754  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f68006630: connection closed
0:00:57.865002604  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f68006630: watch destroyed
0:00:57.865037079  1400   0x7f60000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x5579351050> unmanage client 0x7f68006630
0:00:57.865076754  1400   0x7f60000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f68006cb0
0:00:57.865106829  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f68006630
0:00:57.865242629  1400   0x7f60000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f68006f10
[2021-01-01 14:38:42.337] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:38:42.337] [INFO] Uptime: 60.0 seconds
[2021-01-01 14:38:42.337] [INFO] Total connections: 1
[2021-01-01 14:38:42.337] [INFO] Active connections: 1
[2021-01-01 14:38:42.337] [INFO] Frames served: 1
[2021-01-01 14:38:42.337] [INFO] Clients connected: 0
[2021-01-01 14:38:42.337] [INFO] Error count: 0
0:01:02.157089129  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f680089b0 connected to server ip ************, ipv6 = 0
0:01:02.157134154  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f680089b0 ip ***********:60133
0:01:02.157154829  1400   0x7f68000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x5579351050> manage client 0x7f680089b0
[2021-01-01 14:38:44.008] [INFO] === CLIENT CONNECTED ===
[2021-01-01 14:38:44.008] [INFO] Active connections: 2, Total connections: 2
0:01:02.157298144  1400   0x7f60000de0 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f68007c20
0:01:02.157342354  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f680089b0: attaching to context 0x7f68007d40
0:01:02.169785585  1400   0x7f60000de0 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f680089b0: received a request SETUP rtsp://************:8554/stream 1.0
0:01:02.169820820  1400   0x7f60000de0 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x5579353360 for path /stream
0:01:02.169841008  1400   0x7f60000de0 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=false format=time caps="image/jpeg,format=MJPG,width=1280,height=720,framerate=30/1" ! mppjpegdec ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:01:02.169927435  1400   0x7f60000de0 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:01:02.169972667  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f50002900> adding pad 'src'
0:01:02.170019789  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1
0:01:02.170047559  1400   0x7f60000de0 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mppjpegdec"
0:01:02.170081728  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f50003760> adding pad 'sink'
0:01:02.170106880  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f50003760> adding pad 'src'
0:01:02.170163523  1400   0x7f60000de0 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:01:02.170190645  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f50006700> adding pad 'sink'
0:01:02.170210988  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f50006700> adding pad 'src'
0:01:02.170239839  1400   0x7f60000de0 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:01:02.170264941  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f50007950> adding pad 'sink'
0:01:02.170285122  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f50007950> adding pad 'src'
0:01:02.170304711  1400   0x7f60000de0 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f50007950> PTS interpolation: no
0:01:02.170316406  1400   0x7f60000de0 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f50007950> TS inferring: no
0:01:02.170345672  1400   0x7f60000de0 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:01:02.170370495  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f5000b5d0> adding pad 'src'
0:01:02.170390881  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f5000b5d0> adding pad 'sink'
0:01:02.170415919  1400   0x7f60000de0 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:01:02.170488755  1400   0x7f60000de0 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstMppJpegDec named mppjpegdec1 (0/0) with caps "(NULL)"
0:01:02.170505696  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element mppjpegdec1:(any)
0:01:02.170521079  1400   0x7f60000de0 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and mppjpegdec1:sink
0:01:02.170538984  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:01:02.170558328  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec1:src> pad has no peer
0:01:02.170600370  1400   0x7f60000de0 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and mppjpegdec1 in same bin, no need for ghost pads
0:01:02.170630394  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and mppjpegdec1:sink
0:01:02.170645125  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:01:02.170660006  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec1:src> pad has no peer
0:01:02.170691795  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and mppjpegdec1:sink, successful
0:01:02.170702725  1400   0x7f60000de0 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.170714030  1400   0x7f60000de0 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:01:02.170738904  1400   0x7f60000de0 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppJpegDec named mppjpegdec1 to some pad of GstMppH264Enc named mpph264enc1 (0/0) with caps "(NULL)"
0:01:02.170752744  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mppjpegdec1:(any) to element mpph264enc1:(any)
0:01:02.170767016  1400   0x7f60000de0 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mppjpegdec1:src and mpph264enc1:sink
0:01:02.170785504  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:01:02.170819643  1400   0x7f60000de0 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mppjpegdec1 and mpph264enc1 in same bin, no need for ghost pads
0:01:02.170837980  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mppjpegdec1:src and mpph264enc1:sink
0:01:02.170853660  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:01:02.170884516  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mppjpegdec1:src and mpph264enc1:sink, successful
0:01:02.170894866  1400   0x7f60000de0 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.170906723  1400   0x7f60000de0 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mppjpegdec1:src> Received event on flushing pad. Discarding
0:01:02.170929657  1400   0x7f60000de0 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc1 to some pad of GstH264Parse named h264parse1 (0/0) with caps "(NULL)"
0:01:02.170943131  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc1:(any) to element h264parse1:(any)
0:01:02.170957711  1400   0x7f60000de0 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc1:src and h264parse1:sink
0:01:02.170975430  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:01:02.170994585  1400   0x7f60000de0 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc1 and h264parse1 in same bin, no need for ghost pads
0:01:02.171010949  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc1:src and h264parse1:sink
0:01:02.171026426  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:01:02.171046309  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc1:src and h264parse1:sink, successful
0:01:02.171056245  1400   0x7f60000de0 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.171067579  1400   0x7f60000de0 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc1:src> Received event on flushing pad. Discarding
0:01:02.171089071  1400   0x7f60000de0 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse1 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:01:02.171102183  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse1:(any) to element pay0:(any)
0:01:02.171115562  1400   0x7f60000de0 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse1:src and pay0:sink
0:01:02.171133760  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:01:02.171152176  1400   0x7f60000de0 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse1 and pay0 in same bin, no need for ghost pads
0:01:02.171168342  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse1:src and pay0:sink
0:01:02.171183934  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:01:02.171200259  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse1:src and pay0:sink, successful
0:01:02.171209944  1400   0x7f60000de0 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.171220044  1400   0x7f60000de0 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse1:src> Received event on flushing pad. Discarding
0:01:02.171250116  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay0
0:01:02.171266301  1400   0x7f60000de0 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f5000b5d0
0:01:02.171278297  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:01:02.171292319  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f5000f650: creating stream with index 0 and payloader <pay0>
0:01:02.171335302  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad5
0:01:02.171349709  1400   0x7f60000de0 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad5, successful
0:01:02.171358929  1400   0x7f60000de0 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.171371939  1400   0x7f60000de0 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:01:02.171392115  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin1> adding pad 'src_0'
0:01:02.171407673  1400   0x7f60000de0 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f500104c0
0:01:02.171422661  1400   0x7f60000de0 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f500104c0> set retransmission time 0
0:01:02.171434980  1400   0x7f60000de0 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f500104c0> Enabling rate control
0:01:02.171457545  1400   0x7f60000de0 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f500104c0> set retransmission pt 97
0:01:02.171472168  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay0
0:01:02.171490319  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay0
0:01:02.171506993  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay1
0:01:02.171523176  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay1
0:01:02.171540347  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay1
0:01:02.171561106  1400   0x7f60000de0 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:01:02.171640146  1400   0x7f60000de0 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f500104c0> set retransmission time 0
[2021-01-01 14:38:44.022] [INFO] factory: 0x5579353360, media: 0x7f5000f650, user_data: 0x5579312520
[2021-01-01 14:38:44.022] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 14:38:44.022] [INFO] Got media pipeline: 0x7f5000d3c0
0:01:02.171712292  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element source
[2021-01-01 14:38:44.022] [INFO] Found appsrc element: 0x7f50002900
[2021-01-01 14:38:44.022] [INFO] Connected appsrc signals
0:01:02.171769722  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:01:02.171789542  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:01:02.171805775  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 14:38:44.022] [INFO] Appsrc configured, waiting for need-data callback to start data flow
[2021-01-01 14:38:44.022] [INFO] Media configured successfully
[2021-01-01 14:38:44.022] [INFO] configure_media call completed
0:01:02.171866294  1400   0x7f60000de0 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f5000f650 for url /stream
0:01:02.172000160  1400   0x7f60000de0 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f5000f650
0:01:02.172013465  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:01:02.172028185  1400   0x7f60000de0 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:01:02.172052354  1400   0x7f60000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f58024580
0:01:02.172096622  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:01:02.172107638  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:01:02.172147229  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:01:02.172200654  1400   0x7f60000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:01:02.172282279  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:01:02.172334654  1400   0x7f60000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:01:02.172385879  1400   0x7f60000b70 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f500104c0 joining bin as session 0
0:01:02.172426704  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:01:02.172544679  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:01:02.172621654  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f6402b730> adding pad 'sink'
0:01:02.172655204  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f6402b730> adding pad 'rtcp_sink'
0:01:02.172696429  1400   0x7f60000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:01:02.172764179  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f64009350> adding pad 'src'
0:01:02.172800504  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f64009350> adding pad 'sink'
0:01:02.172966254  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:01:02.172987129  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:01:02.173004304  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:01:02.173058904  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_sink'
0:01:02.173098954  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_src'
0:01:02.173118679  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:send_rtp_src
0:01:02.173188729  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6
0:01:02.173237780  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6, successful
0:01:02.173252180  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.173312530  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_src_0'
0:01:02.173365130  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink
0:01:02.173404355  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink, successful
0:01:02.173417630  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.173442155  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_sink_0'
0:01:02.173473130  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin1:src_0 and rtpbin1:send_rtp_sink_0
0:01:02.173538605  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin1:src_0 and rtpbin1:send_rtp_sink_0, successful
0:01:02.173553105  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.173586405  1400   0x7f60000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:01:02.173629980  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin1:send_rtp_src_0
0:01:02.173710980  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtcp_src'
0:01:02.173788680  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8
0:01:02.173810180  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8, successful
0:01:02.173823330  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.173851055  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtcp_src_0'
0:01:02.173901005  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'recv_rtcp_sink'
0:01:02.173940105  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'sync_src'
0:01:02.173978180  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:sync_src
0:01:02.173996330  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux1:rtcp_sink
0:01:02.174019480  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink
0:01:02.174038255  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink, successful
0:01:02.174051080  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.174103480  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink
0:01:02.174126405  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink, successful
0:01:02.174139830  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:01:02.174164055  1400   0x7f60000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'recv_rtcp_sink_0'
0:01:02.174245105  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f500104c0> successfully joined bin
0:01:02.174306105  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f5000f650
0:01:02.174326505  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f5000f650 set blocked 1
0:01:02.174342755  1400   0x7f60000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f500104c0> blocked: 1
0:01:02.174364030  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f5000f650
0:01:02.174382180  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f5000f650
0:01:02.174439155  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current NULL pending VOID_PENDING, desired next READY
0:01:02.174486855  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current NULL pending VOID_PENDING, desired next READY
0:01:02.174505780  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to READY
0:01:02.174524130  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.174558680  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 2(READY) successfully
0:01:02.174609080  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current NULL pending VOID_PENDING, desired next READY
0:01:02.174628080  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to READY
0:01:02.174663905  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.174690805  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 2(READY) successfully
0:01:02.174713230  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current NULL pending VOID_PENDING, desired next READY
0:01:02.174730830  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to READY
0:01:02.174747480  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.174770730  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 2(READY) successfully
0:01:02.174790680  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to READY
0:01:02.174807655  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.174830480  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 2(READY) successfully
0:01:02.174850405  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current NULL pending VOID_PENDING, desired next READY
0:01:02.174894205  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:01:02.174913155  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:01:02.174930080  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.174953230  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 2(READY) successfully
0:01:02.175002280  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current NULL pending VOID_PENDING, desired next READY
0:01:02.175021705  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to READY
0:01:02.175038455  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.175062130  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 2(READY) successfully
0:01:02.175083705  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current NULL pending VOID_PENDING, desired next READY
0:01:02.175102530  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to READY
0:01:02.175118930  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.175141830  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 2(READY) successfully
0:01:02.175163355  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current NULL pending VOID_PENDING, desired next READY
0:01:02.175181605  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to READY
0:01:02.175197880  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.175221105  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 2(READY) successfully
0:01:02.175240530  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:01:02.175257755  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:01:02.175274405  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.175297330  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 2(READY) successfully
0:01:02.175315330  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to READY
0:01:02.175331730  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:01:02.175362955  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 2(READY) successfully
0:01:02.175384780  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:01:02.175402155  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:01:02.175422130  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:01:02.175450280  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.175489530  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.175515305  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PAUSED
0:01:02.175532880  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.175558455  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 3(PAUSED) successfully
0:01:02.175584105  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.175612505  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PAUSED
0:01:02.175630130  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.175655380  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 3(PAUSED) successfully
0:01:02.175679955  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.175703655  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PAUSED
0:01:02.175729730  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.175770205  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 3(PAUSED) successfully
0:01:02.175797780  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PAUSED
0:01:02.175815605  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.175838280  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 3(PAUSED) successfully
0:01:02.175873830  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.175923480  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.175953630  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:01:02.175971330  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.176003305  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 3(PAUSED) successfully
0:01:02.176025855  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.176304605  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PAUSED
0:01:02.176325780  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.176363830  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 3(PAUSED) successfully
0:01:02.176387930  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.176911055  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PAUSED
0:01:02.176947455  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.176984830  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 3(PAUSED) successfully
0:01:02.177017930  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.177189705  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to PAUSED
0:01:02.177211080  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.177262830  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 3(PAUSED) successfully
0:01:02.177285305  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:01:02.177308155  1400   0x7f60000b70 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:01:02.177346305  1400   0x7f60000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:01:02.177392755  1400   0x7f60000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f50002c70 on task 0x7f6407e2d0
0:01:02.177414505  1400   0x7f60000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f6407e2d0
0:01:02.177573305  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:01:02.177594330  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.177618830  1400   0x7f60000d80 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:01:02.177652355  1400   0x7f60000d80 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:01:02.177665155  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin1> child 'source' changed state to 3(PAUSED) successfully without preroll
0:01:02.177706805  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PAUSED
0:01:02.177724680  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.177749305  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 3(PAUSED) successfully without preroll
0:01:02.177787905  1400   0x7f60000b70 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:01:02.177803205  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:01:02.177823055  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:01:02.177844430  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f5000f650
0:01:02.177892830  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f5000f650
0:01:02.177965305  1400   0x7f60000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:01:02.178011330  1400   0x7f60000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:01:02.178048455  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:01:02.178083105  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:01:02.178130030  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PLAYING
0:01:02.178147980  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178173505  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:01:02.178196505  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:01:02.178214580  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PLAYING
0:01:02.178231005  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178253855  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:01:02.178275280  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:01:02.178350655  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PLAYING
0:01:02.178369180  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178393230  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:01:02.178412380  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:01:02.178428855  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178452005  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:01:02.178516780  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:01:02.178536480  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:01:02.178554455  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178603580  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:01:02.178626505  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:01:02.178644430  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PLAYING
0:01:02.178661455  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178684305  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:01:02.178705655  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:01:02.178724005  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PLAYING
0:01:02.178740205  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178774630  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:01:02.178799580  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:01:02.178817830  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to PLAYING
0:01:02.178834505  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178858430  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 4(PLAYING) successfully
0:01:02.178884005  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:01:02.178903405  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.178926705  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:01:02.178936355  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1
0:01:02.178945530  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:01:02.178981830  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.179005805  1400   0x7f60000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:01:02.179019980  1400   0x7f60000d80 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec1:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:01:02.179024730  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:01:02.179060305  1400   0x7f60000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:01:02.179280905  1400   0x7f60000d80 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f5800e5e0
0:01:02.179324630  1400   0x7f60000d80 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 140296 bytes, 1 buffers, 0:00:00.033333333
0:01:02.179337155  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5000f650: went from NULL to READY (pending PAUSED)
0:01:02.179355130  1400   0x7f60000d80 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:01:02.179400030  1400   0x7f60000d80 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:01:02.179475280  1400   0x7f60000d80 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec1:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:01:02.179498380  1400   0x7f60000d80 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
0:01:02.179539130  1400   0x7f60000d80 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec1:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:01:02.179558255  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5000f650: went from READY to PAUSED (pending VOID_PENDING)
0:01:02.179577880  1400   0x7f60000d80 WARN                 basesrc gstbasesrc.c:3132:gst_base_src_loop:<source> error: Internal data stream error.
0:01:02.179594430  1400   0x7f60000b70 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f5000f650: got message type 2048 (new-clock)
0:01:02.179596855  1400   0x7f60000d80 WARN                 basesrc gstbasesrc.c:3132:gst_base_src_loop:<source> error: streaming stopped, reason not-negotiated (-4)
0:01:02.179651780  1400   0x7f60000d80 INFO        GST_ERROR_SYSTEM gstelement.c:2281:gst_element_message_full_with_details:<source> posting message: Internal data stream error.
0:01:02.179687930  1400   0x7f60000d80 INFO        GST_ERROR_SYSTEM gstelement.c:2308:gst_element_message_full_with_details:<source> posted error message: Internal data stream error.
0:01:02.179723180  1400   0x7f60000d80 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec1:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:01:02.179734705  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5000f650: went from PAUSED to PLAYING (pending VOID_PENDING)
0:01:02.179760955  1400   0x7f60000d80 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec1:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:01:02.179809680  1400   0x7f60000b70 WARN               rtspmedia rtsp-media.c:3306:default_handle_message: 0x7f5000f650: got error Internal data stream error. (../libs/gst/base/gstbasesrc.c(3132): gst_base_src_loop (): /GstPipeline:media-pipeline/GstBin:bin1/GstAppSrc:source:
streaming stopped, reason not-negotiated (-4))
0:01:02.179820705  1400   0x7f60000d80 WARN                GST_CAPS gstpad.c:5832:pre_eventfunc_check:<mppjpegdec1:sink> caps image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1 not accepted
0:01:02.179836655  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 5
0:01:02.179888367  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 5
0:01:02.179901931  1400   0x7f60000de0 WARN               rtspmedia rtsp-media.c:3634:wait_preroll: failed to preroll pipeline
0:01:02.179917916  1400   0x7f60000de0 WARN               rtspmedia rtsp-media.c:4004:gst_rtsp_media_prepare: failed to preroll pipeline
0:01:02.179931777  1400   0x7f60000de0 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f5000f650
0:01:02.179943937  1400   0x7f60000de0 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f5000f650
0:01:02.179957357  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:01:02.179968067  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:4022:finish_unprepare: shutting down
0:01:02.179978335  1400   0x7f60000de0 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to NULL for media 0x7f5000f650
0:01:02.180010165  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180025755  1400   0x7f60000d80 INFO                    task gsttask.c:368:gst_task_func:<source:src> Task going to paused
0:01:02.180037028  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180064745  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PAUSED
0:01:02.180078697  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180106832  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 3(PAUSED) successfully
0:01:02.180126226  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180142822  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PAUSED
0:01:02.180155727  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180178277  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 3(PAUSED) successfully
0:01:02.180195166  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180211199  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PAUSED
0:01:02.180223948  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180250390  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 3(PAUSED) successfully
0:01:02.180272351  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PAUSED
0:01:02.180286269  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180309467  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 3(PAUSED) successfully
0:01:02.180327581  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180354593  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180368610  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:01:02.180380440  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180402641  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 3(PAUSED) successfully
0:01:02.180418813  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180431761  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PAUSED
0:01:02.180444112  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180464985  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 3(PAUSED) successfully
0:01:02.180480842  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180495217  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PAUSED
0:01:02.180506649  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180528072  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 3(PAUSED) successfully
0:01:02.180543935  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180557270  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to PAUSED
0:01:02.180568700  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180590849  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 3(PAUSED) successfully
0:01:02.180605633  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PAUSED
0:01:02.180619192  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:01:02.180630713  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180652014  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin1> child 'source' changed state to 3(PAUSED) successfully without preroll
0:01:02.180666286  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PAUSED
0:01:02.180678982  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:01:02.180699854  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 3(PAUSED) successfully without preroll
0:01:02.180715062  1400   0x7f60000de0 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:01:02.180730456  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PLAYING to PAUSED, pending NULL, next READY
0:01:02.180742289  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:01:02.180761700  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change PAUSED to READY, final NULL
0:01:02.180783205  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5000f650: went from PLAYING to PAUSED (pending NULL)
0:01:02.180784566  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PAUSED pending VOID_PENDING, desired next READY
0:01:02.180849265  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PAUSED pending VOID_PENDING, desired next READY
0:01:02.180871433  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to READY
0:01:02.180883904  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.180906211  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 2(READY) successfully
0:01:02.180923026  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PAUSED pending VOID_PENDING, desired next READY
0:01:02.180943227  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to READY
0:01:02.180955574  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.180977265  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 2(READY) successfully
0:01:02.180993248  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PAUSED pending VOID_PENDING, desired next READY
0:01:02.181023729  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to READY
0:01:02.181035546  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.181056727  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 2(READY) successfully
0:01:02.181085621  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to READY
0:01:02.181097916  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.181118877  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 2(READY) successfully
0:01:02.181151045  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next READY
0:01:02.181173186  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:01:02.181185670  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.181206998  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 2(READY) successfully
0:01:02.181225388  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PAUSED pending VOID_PENDING, desired next READY
0:01:02.181272381  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to READY
0:01:02.181286491  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.181309766  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 2(READY) successfully
0:01:02.181327143  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PAUSED pending VOID_PENDING, desired next READY
0:01:02.181505408  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to READY
0:01:02.181522504  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.181547103  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 2(READY) successfully
0:01:02.181566131  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current PAUSED pending VOID_PENDING, desired next READY
0:01:02.181639328  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to READY
0:01:02.181654181  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.181677226  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 2(READY) successfully
0:01:02.181696518  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:1052:gst_app_src_unlock:<source> unlock start
0:01:02.181720655  1400   0x7f60000d80 INFO                    task gsttask.c:370:gst_task_func:<source:src> Task resume from paused
0:01:02.181780251  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:1067:gst_app_src_unlock_stop:<source> unlock stop
0:01:02.181794300  1400   0x7f60000de0 DEBUG                 appsrc gstappsrc.c:1105:gst_app_src_stop:<source> stopping
0:01:02.181823814  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:01:02.181837287  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.181862011  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 2(READY) successfully
0:01:02.181880946  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to READY
0:01:02.181894080  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:01:02.181914761  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 2(READY) successfully
0:01:02.181933780  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PAUSED to READY, pending NULL, next NULL
0:01:02.181946338  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to READY (NULL pending)
0:01:02.181965041  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to NULL, final NULL
0:01:02.181988717  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current READY pending VOID_PENDING, desired next NULL
0:01:02.181991655  1400   0x7f60000b70 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5000f650: went from PAUSED to READY (pending NULL)
0:01:02.182016891  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current READY pending VOID_PENDING, desired next NULL
0:01:02.182051447  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:01:02.182064123  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182087658  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 1(NULL) successfully
0:01:02.182105850  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current READY pending VOID_PENDING, desired next NULL
0:01:02.182122042  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:01:02.182134505  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182155578  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 1(NULL) successfully
0:01:02.182172233  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current READY pending VOID_PENDING, desired next NULL
0:01:02.182189071  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:01:02.182201567  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182222271  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 1(NULL) successfully
0:01:02.182239422  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to NULL
0:01:02.182251980  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182272458  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 1(NULL) successfully
0:01:02.182286976  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current READY pending VOID_PENDING, desired next NULL
0:01:02.182313167  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next NULL
0:01:02.182330720  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to NULL
0:01:02.182343814  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182365546  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 1(NULL) successfully
0:01:02.182385557  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current READY pending VOID_PENDING, desired next NULL
0:01:02.182403097  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to NULL
0:01:02.182415432  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182437427  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 1(NULL) successfully
0:01:02.182456183  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current READY pending VOID_PENDING, desired next NULL
0:01:02.182472234  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to NULL
0:01:02.182484334  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182505163  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 1(NULL) successfully
0:01:02.182523156  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec1> current READY pending VOID_PENDING, desired next NULL
0:01:02.182538665  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec1> completed state change to NULL
0:01:02.182550713  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182571419  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mppjpegdec1' changed state to 1(NULL) successfully
0:01:02.182599458  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next NULL
0:01:02.182616125  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to NULL
0:01:02.182629038  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182649953  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 1(NULL) successfully
0:01:02.182667238  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to NULL
0:01:02.182679410  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182699733  1400   0x7f60000de0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 1(NULL) successfully
0:01:02.182721154  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to NULL
0:01:02.182733991  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:01:02.182750263  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f5000f650 set blocked 0
0:01:02.182764354  1400   0x7f60000de0 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f500104c0> blocked: 0
0:01:02.182779277  1400   0x7f60000de0 INFO               rtspmedia rtsp-media.c:4035:finish_unprepare: Removing elements of stream 0 from pipeline
0:01:02.182790969  1400   0x7f60000de0 INFO              rtspstream rtsp-stream.c:4153:gst_rtsp_stream_leave_bin: stream 0x7f500104c0 leaving bin
0:01:02.182805425  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking bin1:src_0(0x7f5000fad0) and rtpbin1:send_rtp_sink_0(0x7f64031d10)
0:01:02.182823942  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked bin1:src_0 and rtpbin1:send_rtp_sink_0
0:01:02.182847569  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin1> removing pad 'send_rtp_src_0'
0:01:02.182863035  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession1:send_rtp_src(0x7f6400a880) and send_rtp_src_0:proxypad6(0x7f6400a210)
0:01:02.182880557  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6
0:01:02.182900662  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'send_rtp_sink'
0:01:02.182916503  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking send_rtp_sink_0:proxypad7(0x7f6403cd30) and rtpsession1:send_rtp_sink(0x7f6400abc0)
0:01:02.182929615  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink
0:01:02.182941666  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'send_rtp_src'
0:01:02.182968140  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin1> removing pad 'send_rtp_sink_0'
0:01:02.182989031  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin1> removing pad 'recv_rtcp_sink_0'
0:01:02.183003742  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking recv_rtcp_sink_0:proxypad9(0x7f6402efb0) and rtpsession1:recv_rtcp_sink(0x7f6403d180)
0:01:02.183017530  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink
0:01:02.183031814  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'recv_rtcp_sink'
0:01:02.183044500  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'sync_src'
0:01:02.183058165  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession1:sync_src(0x7f6403d4e0) and rtpssrcdemux1:rtcp_sink(0x7f6403bb00)
0:01:02.183075454  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink
0:01:02.183114895  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin1> removing pad 'send_rtcp_src_0'
0:01:02.183131836  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession1:send_rtcp_src(0x7f6402b990) and send_rtcp_src_0:proxypad8(0x7f6403d9d0)
0:01:02.183150614  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8
0:01:02.183168333  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession1> removing pad 'send_rtcp_src'
0:01:02.183191224  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:01:02.183204764  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:01:02.183217061  1400   0x7f60000de0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:01:02.183234883  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin1> removed child "rtpsession1"
0:01:02.183258061  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpsession1> 0x7f6400b280 dispose
0:01:02.183270042  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpsession1> 0x7f6400b280 parent class dispose
0:01:02.183292691  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpsession1> 0x7f6400b280 finalize
0:01:02.183303209  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpsession1> 0x7f6400b280 finalize parent
0:01:02.183318250  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin1> removed child "rtpssrcdemux1"
0:01:02.183335300  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpssrcdemux1> 0x7f6402b730 dispose
0:01:02.183346432  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux1> removing pad 'sink'
0:01:02.183360211  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux1> removing pad 'rtcp_sink'
0:01:02.183375199  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpssrcdemux1> 0x7f6402b730 parent class dispose
0:01:02.183386573  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpssrcdemux1> 0x7f6402b730 finalize
0:01:02.183396632  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpssrcdemux1> 0x7f6402b730 finalize parent
0:01:02.183410467  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin1> removed child "rtpstorage1"
0:01:02.183430535  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpstorage1> 0x7f64009350 dispose
0:01:02.183441729  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage1> removing pad 'src'
0:01:02.183457739  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage1> removing pad 'sink'
0:01:02.183474364  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpstorage1> 0x7f64009350 parent class dispose
0:01:02.183485748  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpstorage1> 0x7f64009350 finalize
0:01:02.183496075  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpstorage1> 0x7f64009350 finalize parent
0:01:02.183541054  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "rtpbin1"
0:01:02.183562196  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpbin1> 0x7f50011430 dispose
0:01:02.183573429  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpbin1> 0x7f50011430 parent class dispose
0:01:02.183587915  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpbin1> 0x7f50011430 finalize
0:01:02.183598025  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpbin1> 0x7f50011430 finalize parent
0:01:02.183609862  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 0
0:01:02.183624413  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:4078:finish_unprepare: removing bus watch
0:01:02.183637353  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:3421:watch_destroyed:<GstRTSPMedia@0x7f5000f650> source destroyed
0:01:02.183651684  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:4083:finish_unprepare: destroy source
0:01:02.183660901  1400   0x7f60000de0 DEBUG              rtspmedia rtsp-media.c:4089:finish_unprepare: stop thread
0:01:02.183681931  1400   0x7f60000de0 ERROR             rtspclient rtsp-client.c:1115:find_media: client 0x7f680089b0: can't prepare media
0:01:02.183702680  1400   0x7f60000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f58024580
0:01:02.183771955  1400   0x7f60000de0 INFO               rtspmedia rtsp-media.c:530:gst_rtsp_media_finalize: finalize media 0x7f5000f650
0:01:02.183788615  1400   0x7f60000de0 DEBUG             rtspstream rtsp-stream.c:390:gst_rtsp_stream_finalize: finalize stream 0x7f500104c0
0:01:02.183820818  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "bin1"
0:01:02.183848935  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<media-pipeline> 0x7f50010750 dispose
0:01:02.183886054  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<media-pipeline> 0x7f50010750 parent class dispose
0:01:02.183898016  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<media-pipeline> 0x7f50010750 finalize
0:01:02.183924526  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<media-pipeline> 0x7f50010750 finalize parent
0:01:02.183946034  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking pay0:src(0x7f5000b8f0) and src_0:proxypad5(0x7f5000fe50)
0:01:02.183965850  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked pay0:src and src_0:proxypad5
0:01:02.183984736  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking h264parse1:src(0x7f50008b00) and pay0:sink(0x7f5000bd30)
0:01:02.184003227  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked h264parse1:src and pay0:sink
0:01:02.184019348  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "pay0"
0:01:02.184036655  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<pay0> 0x7f5000b5d0 dispose
0:01:02.184047429  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'src'
0:01:02.184063564  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'sink'
0:01:02.184078819  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<pay0> 0x7f5000b5d0 parent class dispose
0:01:02.184098342  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<pay0> 0x7f5000b5d0 finalize
0:01:02.184109438  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<pay0> 0x7f5000b5d0 finalize parent
0:01:02.184127585  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mpph264enc1:src(0x7f50006ff0) and h264parse1:sink(0x7f500087a0)
0:01:02.184144749  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mpph264enc1:src and h264parse1:sink
0:01:02.184160722  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "h264parse1"
0:01:02.184176427  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<h264parse1> 0x7f50007950 dispose
0:01:02.184187444  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<h264parse1> removing pad 'sink'
0:01:02.184202707  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<h264parse1> removing pad 'src'
0:01:02.184217649  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<h264parse1> 0x7f50007950 parent class dispose
0:01:02.184244852  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<h264parse1> 0x7f50007950 finalize
0:01:02.184255907  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<h264parse1> 0x7f50007950 finalize parent
0:01:02.184273144  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mppjpegdec1:src(0x7f50003e30) and mpph264enc1:sink(0x7f50006b80)
0:01:02.184289717  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mppjpegdec1:src and mpph264enc1:sink
0:01:02.184305714  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "mpph264enc1"
0:01:02.184321837  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mpph264enc1> 0x7f50006700 dispose
0:01:02.184333038  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc1> removing pad 'sink'
0:01:02.184348888  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc1> removing pad 'src'
0:01:02.184364100  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mpph264enc1> 0x7f50006700 parent class dispose
0:01:02.184376932  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mpph264enc1> 0x7f50006700 finalize
0:01:02.184387624  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mpph264enc1> 0x7f50006700 finalize parent
0:01:02.184406087  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking source:src(0x7f50002c00) and mppjpegdec1:sink(0x7f50003bc0)
0:01:02.184422611  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked source:src and mppjpegdec1:sink
0:01:02.184438260  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "mppjpegdec1"
0:01:02.184453542  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mppjpegdec1> 0x7f50003760 dispose
0:01:02.184463729  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mppjpegdec1> removing pad 'sink'
0:01:02.184478759  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mppjpegdec1> removing pad 'src'
0:01:02.184493691  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mppjpegdec1> 0x7f50003760 parent class dispose
0:01:02.184510988  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mppjpegdec1> 0x7f50003760 finalize
0:01:02.184521992  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mppjpegdec1> 0x7f50003760 finalize parent
0:01:02.184537643  1400   0x7f60000de0 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin1> removed child "source"
0:01:02.184556555  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<source> 0x7f50002900 dispose
0:01:02.184567806  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<source> removing pad 'src'
0:01:02.184583290  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<source> 0x7f50002900 parent class dispose
0:01:02.184598812  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<source> 0x7f50002900 finalize
0:01:02.184610139  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<source> 0x7f50002900 finalize parent
0:01:02.184620931  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<bin1> 0x7f5000d3c0 dispose
0:01:02.184631062  1400   0x7f60000de0 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<bin1> removing pad 'src_0'
0:01:02.184650653  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<bin1> 0x7f5000d3c0 parent class dispose
0:01:02.184662179  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<bin1> 0x7f5000d3c0 finalize
0:01:02.184673280  1400   0x7f60000de0 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<bin1> 0x7f5000d3c0 finalize parent
0:01:02.184689404  1400   0x7f60000de0 ERROR             rtspclient rtsp-client.c:3125:handle_setup_request: client 0x7f680089b0: media '/stream' not found
0:01:02.186346713  1400   0x7f60000de0 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f680089b0: connection closed
0:01:02.186366372  1400   0x7f60000de0 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f680089b0: watch destroyed
0:01:02.186390622  1400   0x7f60000de0 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x5579351050> unmanage client 0x7f680089b0
0:01:02.186416113  1400   0x7f60000de0 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f680079c0
0:01:02.186434768  1400   0x7f60000de0 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f680089b0
0:01:02.186514226  1400   0x7f60000de0 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f68007c20
0:01:02.193619981  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f68008110 connected to server ip ************, ipv6 = 0
0:01:02.193640338  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f68008110 ip ***********:60134
0:01:02.193654910  1400   0x7f68000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x5579351050> manage client 0x7f68008110
[2021-01-01 14:38:44.044] [INFO] === CLIENT CONNECTED ===
[2021-01-01 14:38:44.044] [INFO] Active connections: 3, Total connections: 3
0:01:02.193754955  1400   0x7f60000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f68009740
0:01:02.193760839  1400   0x7f68000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f68008110: attaching to context 0x7f680095f0
0:01:02.195765505  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f68008110: received a request OPTIONS rtsp://************:8554 1.0
0:01:02.196733530  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f68008110: connection closed
0:01:02.196760755  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f68008110: watch destroyed
0:01:02.196794230  1400   0x7f60000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x5579351050> unmanage client 0x7f68008110
0:01:02.196839105  1400   0x7f60000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f68008a60
0:01:02.196876105  1400   0x7f60000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f68008110
0:01:02.197000980  1400   0x7f60000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f68009740