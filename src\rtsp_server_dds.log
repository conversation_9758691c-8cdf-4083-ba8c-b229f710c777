root@rk3576-buildroot:/# rtsp_server_dds --gst-debug=4 -p 8557 -u /stream
0:00:00.000396252  1491   0x558c448e20 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.000905414  1491   0x558c448e20 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001131448  1491   0x558c448e20 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001245097  1491   0x558c448e20 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001261005  1491   0x558c448e20 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001324260  1491   0x558c448e20 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007513963  1491   0x558c448e20 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.006153 seconds
0:00:00.007584849  1491   0x558c448e20 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.008150757  1491   0x558c448e20 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.008167982  1491   0x558c448e20 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.008185940  1491   0x558c448e20 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.008195772  1491   0x558c448e20 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.008203102  1491   0x558c448e20 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
Initializing DDS reader for topic: Video_Frames
Start init DDS reader: Video_Frames
Create share memery qos success
Create participant success
Register type success
Create subscriber success
Create topic success
DDS Reader initialized for topic: Video_FramesWaiting for first DDS frame...
Subscriber matched
First frame received: 640x480, format=0x56595559
Pipeline: ( appsrc name=source is-live=true do-timestamp=false format=time caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )
0:00:00.148927631  1491   0x558c448e20 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x558c6b9560 for path /stream
RTSP server ready at rtsp://127.0.0.1:8557/stream
Press Ctrl+C to stop
0:00:15.213302594  1491   0x558c448e20 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x558c6c4b20 connected to server ip ************, ipv6 = 0
0:00:15.213331510  1491   0x558c448e20 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x558c6c4b20 ip ***********:61902
0:00:15.213749215  1491   0x558c448e20 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x558c6c4b20: attaching to context 0x558c6c5410
0:00:15.213756646  1491   0x7f84000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x558c6c51a0
0:00:15.223879671  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x558c6c4b20: received a request OPTIONS rtsp://************:8557/stream 1.0
0:00:15.225784146  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x558c6c4b20: received a request DESCRIBE rtsp://************:8557/stream 1.0
0:00:15.225841146  1491   0x7f84000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x558c6b9560 for path /stream
0:00:15.225865971  1491   0x7f84000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=false format=time caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:15.226693721  1491   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstapp.so" loaded
0:00:15.227122946  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:15.227183096  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78007760> adding pad 'src'
0:00:15.228961046  1491   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:15.229216396  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:15.229281071  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7800d050> adding pad 'sink'
0:00:15.229324196  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7800d050> adding pad 'src'
0:00:15.230376671  1491   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoconvertscale.so" loaded
0:00:15.230934896  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:15.230994421  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f78017910> adding pad 'sink'
0:00:15.231031896  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f78017910> adding pad 'src'
0:00:15.234384771  1491   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:15.234767346  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:15.234825471  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f7801c070> adding pad 'sink'
0:00:15.234859171  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f7801c070> adding pad 'src'
0:00:15.236996771  1491   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:00:15.237237646  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:15.237295721  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78024b90> adding pad 'sink'
0:00:15.237330296  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78024b90> adding pad 'src'
0:00:15.237382971  1491   0x7f84000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f78024b90> PTS interpolation: no
0:00:15.237400771  1491   0x7f84000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f78024b90> TS inferring: no
0:00:15.240141221  1491   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:15.240473146  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:15.240535621  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78034a40> adding pad 'src'
0:00:15.240568021  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78034a40> adding pad 'sink'
0:00:15.240623646  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:15.240774646  1491   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue0 (0/0) with caps "(NULL)"
0:00:15.240801621  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue0:(any)
0:00:15.240824946  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue0:sink
0:00:15.240862646  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:15.240888896  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue0 in same bin, no need for ghost pads
0:00:15.240928521  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue0:sink
0:00:15.240952246  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:15.240976696  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue0:sink, successful
0:00:15.240991096  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.241006071  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:15.241052096  1491   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue0 to some pad of GstVideoScale named videoscale0 (0/0) with caps "(NULL)"
0:00:15.241073771  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue0:(any) to element videoscale0:(any)
0:00:15.241098121  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue0:src and videoscale0:sink
0:00:15.241126546  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:15.241827246  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue0 and videoscale0 in same bin, no need for ghost pads
0:00:15.241860746  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue0:src and videoscale0:sink
0:00:15.241893821  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:15.242489496  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue0:src and videoscale0:sink, successful
0:00:15.242506096  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.242521421  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue0:src> Received event on flushing pad. Discarding
0:00:15.242576821  1491   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:15.242606596  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:15.242751896  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f780397d0> adding pad 'sink'
0:00:15.242784871  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f780397d0> adding pad 'src'
0:00:15.242812096  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:15.242859821  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:15.242882596  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.242908746  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale0:(any) to element capsfilter0:sink
0:00:15.242926396  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:15.242943821  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale0 and capsfilter0 in same bin, no need for ghost pads
0:00:15.242968446  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale0:src and capsfilter0:sink
0:00:15.243617796  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:15.243687696  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale0:src and capsfilter0:sink, successful
0:00:15.243703071  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.243718271  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale0:src> Received event on flushing pad. Discarding
0:00:15.243748646  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mpph264enc0:(any)
0:00:15.243769521  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:15.243791771  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mpph264enc0:sink
0:00:15.244554971  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:15.244641221  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mpph264enc0 in same bin, no need for ghost pads
0:00:15.244669471  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mpph264enc0:sink
0:00:15.245429621  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:15.245554596  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mpph264enc0:sink, successful
0:00:15.245574321  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.245591171  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:15.245655296  1491   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:00:15.245682196  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:00:15.245709621  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:00:15.245737821  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:15.245766471  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:00:15.245796371  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:00:15.245820596  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:15.245852121  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:00:15.245869021  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.245883046  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:15.245918646  1491   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:15.245947221  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:00:15.245970546  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:00:15.245995546  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:15.246021846  1491   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:00:15.246044946  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:00:15.246065821  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:15.246088946  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:00:15.246102421  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.246115896  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:00:15.246322046  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:15.246350096  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f78034a40
0:00:15.246366671  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:15.246455971  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:15.246476021  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:15.246489121  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.246502696  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:15.246530871  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:15.246620246  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:15.246651896  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:15.246678996  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:15.246704821  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:15.246730771  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:15.246765046  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
=== MEDIA CONFIGURE CALLBACK ===
0:00:15.246999571  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element source
Found appsrc, connecting signals
Configured appsrc properties
0:00:15.247125446  1491   0x7f84000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f7803a190 for url /stream
0:00:15.247289396  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f7803a190
0:00:15.247323749  1491   0x7f84000d80 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f7803f500
0:00:15.249753771  1491   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:15.249803121  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:15.250601480  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:15.250629920  1491   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:15.250679607  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:15.250700159  1491   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:15.250729332  1491   0x7f84000d80 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f7803eb10 joining bin as session 0
0:00:15.250756367  1491   0x7f84000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:15.251154794  1491   0x7f84000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:15.251251035  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f7400aa40> adding pad 'sink'
0:00:15.251273874  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f7400aa40> adding pad 'rtcp_sink'
0:00:15.251291482  1491   0x7f84000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:15.251367606  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7400bec0> adding pad 'src'
0:00:15.251381271  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7400bec0> adding pad 'sink'
0:00:15.251490740  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:15.251506614  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:15.251518833  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:15.251554961  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:15.251581145  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:15.251595016  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:15.251644256  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:15.251660570  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:15.251671593  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.251698802  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:15.251733649  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:15.251747803  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:15.251756911  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.251774240  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:15.251797053  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:15.251835213  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:15.251845544  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.251858103  1491   0x7f84000d80 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:15.251876827  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:15.251914614  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:15.251955799  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:15.251969853  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:15.251979140  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.252001981  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:15.252034228  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:15.252060657  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:15.252078822  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:15.252090621  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:15.252105846  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:15.252118583  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:15.252127573  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.252167975  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:15.252181668  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:15.252190647  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.252207393  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:15.252254775  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f7803a190
0:00:15.252272798  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f7803a190
0:00:15.252285274  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f7803a190
0:00:15.252310342  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252339230  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252352183  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:15.252364053  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252387607  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:15.252403139  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252414835  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:15.252425510  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252441100  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:15.252454960  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252466673  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:15.252482038  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252497552  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:15.252510672  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:15.252521386  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252536053  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:15.252548875  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252582566  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252595966  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:15.252607277  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252622843  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:15.252636967  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252649236  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:15.252660844  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252676057  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:15.252690408  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252704027  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:15.252715372  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252730754  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:15.252744404  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252756037  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:15.252767232  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252785855  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:15.252800496  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252812402  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:15.252823260  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252838702  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:15.252855733  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current NULL pending VOID_PENDING, desired next READY
0:00:15.252867606  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:15.252878660  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252893117  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:15.252906969  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:15.252918445  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:15.252929082  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252944894  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:15.252957448  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:15.252968439  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.252983142  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:15.252996551  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:15.253007772  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:15.253021035  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:15.253039502  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.253065397  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.253082455  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:15.253098288  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.253114064  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:15.253128197  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.253144293  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:15.253155676  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.253170585  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:15.253184745  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.253212700  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:15.253225148  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.253240420  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:15.253255775  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:15.253267577  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.253282952  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:15.253295511  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.253320794  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.253341545  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:15.253353105  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.253368063  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:15.253382164  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.253645024  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:15.253663600  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.253697632  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:15.253717531  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.254098066  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:15.254119062  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.254142118  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:15.254162207  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.254181720  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:15.254193435  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.254209190  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:15.254224213  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.254241987  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:15.254253688  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.254269587  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:15.254284494  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.254329246  1491   0x7f84000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7800d890 on task 0x7f7405edd0
0:00:15.254343276  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue0:src> created task 0x7f7405edd0
0:00:15.254470232  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:15.254485202  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.254502483  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:15.254517989  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:15.254555330  1491   0x7f84000d80 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.254586575  1491   0x7f84000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f78007ad0 on task 0x7f7405f740
0:00:15.254598857  1491   0x7f84000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f7405f740
0:00:15.254694612  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:15.254708723  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.254725342  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:15.254743430  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:15.254754878  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.254774110  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:15.254789460  1491   0x7f84000d80 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:15.254794446  1491   0x7f84000ff0 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:15.254843981  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:15.254853796  1491   0x7f84000ff0 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:15.254867711  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.254897932  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f7803a190
0:00:15.254909825  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803a190
0:00:15.254960090  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:15.254979647  1491   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:15.255002576  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255024779  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255037113  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:15.255048701  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255066425  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:15.255081470  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255093836  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:15.255105700  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255121651  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:15.255135532  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255214264  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:15.255228201  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255245710  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:15.255259591  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:15.255271136  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255286964  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:15.255315508  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255329066  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:15.255340038  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255355182  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:15.255369043  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255380944  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:00:15.255391890  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255406862  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:15.255420327  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255432757  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:15.255443466  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255458314  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:15.255471531  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255482764  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:15.255493272  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255507707  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:15.255521231  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255532642  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PLAYING
0:00:15.255544617  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255559842  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:15.255584982  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.255597975  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PLAYING
0:00:15.255610277  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255625878  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:15.255643947  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:15.255659327  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255674643  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:15.255688505  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:15.255699687  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.255701145  1491   0x7f84000ff0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:15.255718559  1491   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:15.255749856  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:15.255762479  1491   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.256086188  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 2048 (new-clock)
Pushed buffer, result: 0, frame: 0
0:00:15.256355480  1491   0x7f84000ff0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.256389873  1491   0x7f84000ff0 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
Pushed buffer, result: 0, frame: 1
0:00:15.257220321  1491   0x7f84000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
Pushed buffer, result: 0, frame: 2
0:00:15.257663021  1491   0x7f84000de0 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:15.257713221  1491   0x7f84000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:15.258041771  1491   0x7f84000de0 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applying YUY2 1280x720 (2560x720)
Pushed buffer, result: 0, frame: 3
0:00:15.260117974  1491   0x7f84000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
0:00:15.278538201  1491   0x7f84000de0 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7801c7d0 on task 0x7f6c036980
0:00:15.278567881  1491   0x7f84000de0 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f6c036980
0:00:15.282338343  1491   0x7f84001200 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.283045110  1491   0x7f84001200 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse0> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:15.283149764  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:15.283177944  1491   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:15.283220548  1491   0x7f84001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:15.283437889  1491   0x7f84001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)453229193, timestamp-offset=(uint)161226024, seqnum-offset=(uint)2056, a-framerate=(string)30
0:00:15.283481396  1491   0x7f84001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:15.283509953  1491   0x7f84001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:15.283534997  1491   0x7f84001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:15.283628113  1491   0x7f84001200 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f7803eb10: new sender source 0x7f5c010770
0:00:15.283693873  1491   0x7f84001200 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)453229193, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)2056, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:15.283736915  1491   0x7f84001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:15.283772883  1491   0x7f84001200 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f7803eb10 received caps 0x7f5c00c370, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)453229193, timestamp-offset=(uint)161226024, seqnum-offset=(uint)2056, a-framerate=(string)30
0:00:15.284000812  1491   0x7f84001200 INFO               videometa gstvideometa.c:1100:gst_video_time_code_meta_api_get_type: registering
0:00:15.284095711  1491   0x7f84001200 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:00:15.284196141  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:15.284236522  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f7803a190 is prerolled
0:00:15.284286347  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:15.284364022  1491   0x7f84000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:15.284459447  1491   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:15.284496397  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://************:8557/stream/
0:00:15.288010347  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x558c6c4b20: received a request SETUP rtsp://************:8557/stream/stream=0 1.0
0:00:15.288061022  1491   0x7f84000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x558c6b9560 for path /stream/stream=0
0:00:15.288081722  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f7803a190 for path /stream
0:00:15.288096997  1491   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:15.288114497  1491   0x7f84000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f7803a190 was not prepared
0:00:15.288226747  1491   0x7f84000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f78049c90
0:00:15.288251697  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f78049c90
0:00:15.288305947  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=49938-49939
0:00:15.288409897  1491   0x7f84000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f7803a190 in session 0x7f78048190
0:00:15.290531797  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x558c6c4b20: received a request PLAY rtsp://************:8557/stream/ 1.0
0:00:15.290611347  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:15.290803222  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f7804b050> adding pad 'src'
0:00:15.290866372  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:00:15.290903472  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:15.290967447  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:00:15.290985622  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.291003247  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:00:15.292379197  1491   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:00:15.292420747  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:15.292799672  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78056080> adding pad 'src'
0:00:15.292922397  1491   0x7f84000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:00:15.292963797  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:00:15.292984422  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.293030822  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:15.293088497  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:15.293141922  1491   0x7f84000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.293273772  1491   0x7f84000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f780564b0 on task 0x7f78056f90
0:00:15.293297972  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f78056f90
0:00:15.293620272  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:15.293645422  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:15.293684622  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:15.293708447  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:00:15.293726747  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
Pushed buffer, result: 0, frame: 4
0:00:15.293785964  1491   0x7f84001410 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:00:15.293850606  1491   0x7f84001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:15.293868422  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:00:15.293873287  1491   0x7f84001410 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:15.293908697  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:00:15.293916672  1491   0x7f84001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:15.294060147  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:00:15.294077997  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.294099802  1491   0x7f84001410 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.294116822  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:15.294165417  1491   0x7f84001410 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:00:15.294166922  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78057f70> adding pad 'src'
0:00:15.294291247  1491   0x7f84000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:00:15.294331597  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:00:15.294350397  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.294382647  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:15.294430697  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:15.294470372  1491   0x7f84000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.294507297  1491   0x7f84000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f78058370 on task 0x7f78058a90
0:00:15.294526872  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f78058a90
0:00:15.294698872  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:15.294719522  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:15.294804897  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:00:15.294828322  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:00:15.294852297  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.294864647  1491   0x7f84001620 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:00:15.294957122  1491   0x7f84001620 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:15.294959447  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:00:15.294993722  1491   0x7f84001620 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:15.295022172  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:00:15.295064097  1491   0x7f84001620 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:15.295090372  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:00:15.295110297  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.295148322  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:15.295171947  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:15.295204447  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:00:15.295237997  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:15.295259622  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:15.295289672  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:00:15.295309222  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:00:15.295328947  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.295382397  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:15.295569622  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f78059b40> adding pad 'sink'
0:00:15.295638947  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:15.295949097  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f7805bf40> adding pad 'sink'
0:00:15.296085247  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:00:15.296107122  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:00:15.296135622  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:00:15.296163622  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:00:15.296195172  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:00:15.296212622  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.296228197  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:00:15.296280347  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:15.296304747  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:15.296341097  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:00:15.296365597  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:15.296426847  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:15.296449897  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:00:15.296478647  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:00:15.296505422  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:15.296528597  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.296554922  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:00:15.296579047  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:00:15.296666722  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:00:15.296687572  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.296758122  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:15.296820997  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f7805e8b0> adding pad 'sink'
0:00:15.296877047  1491   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:15.296931997  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f7805f000> adding pad 'sink'
0:00:15.297051697  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:00:15.297072897  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:00:15.297099822  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:00:15.297122797  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:00:15.297152522  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:00:15.297170747  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.297185847  1491   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:00:15.297252797  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:15.297272997  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:15.297297272  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:00:15.297327972  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:15.297349397  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.297378847  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:15.297401497  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:15.297425072  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:00:15.297437891  1491   0x7f84000de0 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:15.297480072  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:15.297505422  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.297562347  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:00:15.297590672  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:15.297636522  1491   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:00:15.297656847  1491   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.297691897  1491   0x7f84000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:49938-49939
0:00:15.297953272  1491   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f7803a190> Handle going back to 0 for none live not seekable streams.
0:00:15.297979322  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:15.298049617  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:15.298089106  1491   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:15.298114697  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:15.298252022  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f7803a190, target state PAUSED
0:00:15.298290197  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f7803a190 do_state 1
0:00:15.298309472  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f7803a190
0:00:15.298326972  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f7803a190
0:00:15.298346272  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803a190
0:00:15.298363472  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:15.298376758  1491   0x7f84001200 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:15.298393030  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
0:00:15.298395952  1491   0x7f84001200 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:15.298432000  1491   0x7f84001200 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:15.298699297  1491   0x7f84001830 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:15.298716726  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:15.298898222  1491   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:15.299106047  1491   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:15.299191597  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.299217022  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:00:15.299250697  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.299301897  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:15.299335172  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.299371372  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:15.299388872  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:15.299411697  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.299429372  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:00:15.299454872  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.299478622  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:15.299508272  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.299525647  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:00:15.299542097  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.299577522  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:15.299618497  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.299674547  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.299691547  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:15.299718997  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:15.299768522  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.299787622  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:15.299804547  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:15.299825272  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.299840572  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:15.299856472  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:15.299876047  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:15.299920122  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:15.299940472  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.299986947  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.300003247  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:15.300019822  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:15.300049872  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.300065622  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:15.300088197  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:15.300118272  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.300136522  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:15.300160622  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:15.300181597  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.300215597  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:15.300240722  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:15.300261222  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.300276597  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:15.300316722  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:15.300337947  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.300353497  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:15.300369672  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:15.300387872  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.300402697  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:15.300418772  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:15.300444272  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:15.300462022  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:15.300509722  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.300526722  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:15.300551647  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:15.300571272  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:15.300590397  1491   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:15.300610772  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:15.300627622  1491   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.300730034  1491   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:15.302707932  1491   0x7f84001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, seqnum-offset=(uint)2056, timestamp-offset=(uint)161226024, ssrc=(uint)453229193, a-framerate=(string)30
0:00:15.302853570  1491   0x7f84001200 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f7803eb10 received caps 0x7f5c00c0b0, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, seqnum-offset=(uint)2056, timestamp-offset=(uint)161226024, ssrc=(uint)453229193, a-framerate=(string)30
Pushed buffer, result: 0, frame: 5
Pushed buffer, result: 0, frame: 6
Pushed buffer, result: 0, frame: 7
Pushed buffer, result: 0, frame: 8
Pushed buffer, result: 0, frame: 9
Pushed buffer, result: 0, frame: 10
Pushed buffer, result: 0, frame: 11
Pushed buffer, result: 0, frame: 12
0:00:15.578357109  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 13
0:00:15.611699310  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 14
0:00:15.645052087  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 15
0:00:15.678376862  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 16
0:00:15.711714134  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 17
0:00:15.745041664  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 18
0:00:15.778519793  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 19
0:00:15.811958747  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 20
0:00:15.845101402  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 21
0:00:15.878382727  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 22
Pushed buffer, result: 0, frame: 23
0:00:15.945066720  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 24
Pushed buffer, result: 0, frame: 25
0:00:16.011732266  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 26
Pushed buffer, result: 0, frame: 27
Pushed buffer, result: 0, frame: 28
0:00:16.111727755  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 29
Pushed buffer, result: 0, frame: 30
Pushed buffer, result: 0, frame: 31
Pushed buffer, result: 0, frame: 32
0:00:16.245072034  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 33
0:00:16.278733399  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 34
Pushed buffer, result: 0, frame: 35
0:00:16.345110858  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 36
Pushed buffer, result: 0, frame: 37
0:00:16.411698422  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 38
Pushed buffer, result: 0, frame: 39
0:00:16.478380394  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 40
Pushed buffer, result: 0, frame: 41
0:00:16.545038454  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 42
Pushed buffer, result: 0, frame: 43
0:00:16.611749178  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 44
Pushed buffer, result: 0, frame: 45
0:00:16.678420219  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 46
Pushed buffer, result: 0, frame: 47
0:00:16.745409675  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 48
0:00:16.778890225  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 49
Pushed buffer, result: 0, frame: 50
0:00:16.845228513  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 51
0:00:16.912072662  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 52
Pushed buffer, result: 0, frame: 53
0:00:16.978431500  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 54
Pushed buffer, result: 0, frame: 55
0:00:17.045104333  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 56
Pushed buffer, result: 0, frame: 57
0:00:17.112012271  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 58
Pushed buffer, result: 0, frame: 59
0:00:17.169963547  1491   0x7f74013410 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:17.170096647  1491   0x7f74013410 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:17.170294989  1491   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:17.170357038  1491   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:17.170382097  1491   0x7f74013410 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)453229193, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)2056, clock-rate=(int)90000, octets-sent=(guint64)438739, packets-sent=(guint64)354, octets-received=(guint64)438739, packets-received=(guint64)354, bytes-received=(guint64)452899, bitrate=(guint64)0, packets-lost=(int)-2, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400322438553954657, sr-rtptime=(uint)161393557, sr-octet-count=(uint)438739, sr-packet-count=(uint)354;
0:00:17.178597879  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 60
Pushed buffer, result: 0, frame: 61
Pushed buffer, result: 0, frame: 62
0:00:17.281781149  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 63
Pushed buffer, result: 0, frame: 64
0:00:17.345430141  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 65
Pushed buffer, result: 0, frame: 66
0:00:17.411950079  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 67
Pushed buffer, result: 0, frame: 68
0:00:17.478629751  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 69
Pushed buffer, result: 0, frame: 70
Pushed buffer, result: 0, frame: 71
0:00:17.578621191  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 72
Pushed buffer, result: 0, frame: 73
Pushed buffer, result: 0, frame: 74
0:00:17.678636230  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 75
Pushed buffer, result: 0, frame: 76
Pushed buffer, result: 0, frame: 77
0:00:17.781763561  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 78
Pushed buffer, result: 0, frame: 79
Pushed buffer, result: 0, frame: 80
0:00:17.878615369  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 81
Pushed buffer, result: 0, frame: 82
Pushed buffer, result: 0, frame: 83
0:00:17.978613147  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 84
Pushed buffer, result: 0, frame: 85
Pushed buffer, result: 0, frame: 86
0:00:18.078617835  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 87
Pushed buffer, result: 0, frame: 88
0:00:18.158594644  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f7803eb10: new source 0x7f60013960
0:00:18.158728290  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3438283491, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:49939, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:18.158772430  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:49939 in 1 transports
0:00:18.158787500  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f7803eb10: found transport 0x7f7804a620 for source  0x7f60013960
0:00:18.158802414  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f7803eb10: source 0x7f60013960 for transport 0x7f7804a620
0:00:18.158824967  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f7803eb10: source 0x7f60013960 in transport 0x7f7804a620 is active
0:00:18.158836929  1491   0x7f84001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78049c90 alive
0:00:18.158886073  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f7803eb10: new SDES 0x7f60013960
Pushed buffer, result: 0, frame: 89
0:00:18.178631634  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 90
Pushed buffer, result: 0, frame: 91
Pushed buffer, result: 0, frame: 92
0:00:18.281570877  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 93
Pushed buffer, result: 0, frame: 94
Pushed buffer, result: 0, frame: 95
0:00:18.378609453  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 96
Pushed buffer, result: 0, frame: 97
Pushed buffer, result: 0, frame: 98
0:00:18.478602088  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 99
Pushed buffer, result: 0, frame: 100
Pushed buffer, result: 0, frame: 101
0:00:18.578910429  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 102
Pushed buffer, result: 0, frame: 103
Pushed buffer, result: 0, frame: 104
0:00:18.678738166  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 105
Pushed buffer, result: 0, frame: 106
Pushed buffer, result: 0, frame: 107
0:00:18.782177638  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 108
Pushed buffer, result: 0, frame: 109
Pushed buffer, result: 0, frame: 110
0:00:18.912022168  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 111
Pushed buffer, result: 0, frame: 112
Pushed buffer, result: 0, frame: 113
Pushed buffer, result: 0, frame: 114
0:00:19.045261201  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 115
Pushed buffer, result: 0, frame: 116
Pushed buffer, result: 0, frame: 117
Pushed buffer, result: 0, frame: 118
0:00:19.178612421  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 119
Pushed buffer, result: 0, frame: 120
Pushed buffer, result: 0, frame: 121
0:00:19.281238844  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 122
Pushed buffer, result: 0, frame: 123
Pushed buffer, result: 0, frame: 124
Pushed buffer, result: 0, frame: 125
0:00:19.411929065  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 126
Pushed buffer, result: 0, frame: 127
Pushed buffer, result: 0, frame: 128
Pushed buffer, result: 0, frame: 129
0:00:19.545269190  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 130
Pushed buffer, result: 0, frame: 131
Pushed buffer, result: 0, frame: 132
Pushed buffer, result: 0, frame: 133
0:00:19.678608212  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 134
Pushed buffer, result: 0, frame: 135
Pushed buffer, result: 0, frame: 136
0:00:19.781572443  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 137
Pushed buffer, result: 0, frame: 138
Pushed buffer, result: 0, frame: 139
Pushed buffer, result: 0, frame: 140
Pushed buffer, result: 0, frame: 141
0:00:19.945265681  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 142
Pushed buffer, result: 0, frame: 143
Pushed buffer, result: 0, frame: 144
Pushed buffer, result: 0, frame: 145
Pushed buffer, result: 0, frame: 146
0:00:20.111944116  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
Pushed buffer, result: 0, frame: 147
Pushed buffer, result: 0, frame: 148
Pushed buffer, result: 0, frame: 149
Pushed buffer, result: 0, frame: 150
Pushed buffer, result: 0, frame: 151
0:00:20.281557956  1491   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a190: got message type 16 (tag)
0:00:20.304393250  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f7803eb10: source 0x7f60013960 in transport 0x7f7804a620 is active
0:00:20.304420655  1491   0x7f84001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78049c90 alive
0:00:20.304448289  1491   0x7f84001410 INFO              rtspstream rtsp-stream.c:2488:on_bye_ssrc: 0x7f7803eb10: source 0x7f60013960 bye
0:00:20.304608922  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x558c6c4b20: received a request TEARDOWN rtsp://************:8557/stream/ 1.0
0:00:20.304674372  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f7803a190, target state PLAYING
0:00:20.304696397  1491   0x7f84000b70 INFO              rtspstream rtsp-stream.c:4774:update_transport: removing ***********:49938-49939
0:00:20.304748022  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f7803a190 do_state 1
0:00:20.304765772  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f7803a190
0:00:20.304782722  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f7803a190
0:00:20.304803572  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to NULL for media 0x7f7803a190
0:00:20.304865872  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.304941972  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:20.304961047  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305003847  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 3(PAUSED) successfully
0:00:20.305026697  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305067497  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink0> completed state change to PAUSED
0:00:20.305090122  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305121122  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 3(PAUSED) successfully
0:00:20.305144247  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305161322  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:20.305175722  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305214372  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 3(PAUSED) successfully
0:00:20.305237522  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305254322  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:20.305270547  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305294797  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 3(PAUSED) successfully
0:00:20.305318397  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305352822  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305371172  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:20.305388047  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305416822  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:20.305439047  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305456072  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:20.305472772  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305495247  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:20.305516247  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305541272  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:20.305560897  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305598922  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:20.305623422  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:20.305641247  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305730497  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:20.305754022  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305804922  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305824197  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:20.305840897  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305865272  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:20.305887847  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305905847  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:20.305922572  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.305952222  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:20.305974022  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.305992147  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:20.306008497  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.306033222  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:20.306056772  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.306073722  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:20.306089872  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.306114622  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:20.306136147  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.306155147  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:20.306171347  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.306193922  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:20.306215447  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.306232572  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:20.306249147  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.306273547  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:20.306294447  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.306315472  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:20.306335047  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.306358247  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:20.306378947  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:20.306395972  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.306423397  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:20.306496722  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:20.306517047  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PAUSED
0:00:20.306534397  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:20.306556622  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 3(PAUSED) successfully
0:00:20.306578422  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 3(PAUSED) successfully
0:00:20.306599322  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 3(PAUSED) successfully
0:00:20.306625547  1491   0x7f84000b70 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:20.306649972  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:20.306667397  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:20.306688322  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change PAUSED to READY, final NULL
0:00:20.306751122  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.306888072  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to READY
0:00:20.306907722  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
Pushed buffer, result: 0, frame: 152
0:00:20.306933847  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 2(READY) successfully
0:00:20.306962172  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.307038577  1491   0x7f84001200 WARN              rtph264pay gstrtph264pay.c:889:gst_rtp_h264_pay_send_sps_pps:<pay0> Problem pushing SPS
0:00:20.307063096  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink0> completed state change to READY
0:00:20.307085446  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.307107885  1491   0x7f84001200 WARN              rtph264pay gstrtph264pay.c:903:gst_rtp_h264_pay_send_sps_pps:<pay0> Problem pushing PPS
0:00:20.307112471  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 2(READY) successfully
0:00:20.307158371  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.307194746  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to READY
0:00:20.307212996  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.307237221  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 2(READY) successfully
0:00:20.307258871  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.307291446  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to READY
0:00:20.307309346  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.307331221  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 2(READY) successfully
0:00:20.307354446  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.307403721  1491   0x7f84000b70 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f7803eb10 received caps (nil), (NULL)
0:00:20.307456971  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.307484196  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:20.307508721  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.307533921  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:20.307556096  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.307588321  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:20.307606121  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.307674396  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:20.307697821  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.307788796  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:20.307807646  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.307832571  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:20.307880921  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:20.307899096  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.307922546  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:20.307980771  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.308030121  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:20.308050146  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.308073796  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:20.308108071  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.308227071  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:20.308246546  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.308269896  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:20.308292421  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.310302607  1491   0x7f84001200 INFO                  mppenc gstmppenc.c:1097:gst_mpp_enc_loop:<mpph264enc0> flushing
0:00:20.310323354  1491   0x7f84001200 INFO                    task gsttask.c:368:gst_task_func:<mpph264enc0:src> Task going to paused
0:00:20.310350249  1491   0x7f84001200 INFO                    task gsttask.c:370:gst_task_func:<mpph264enc0:src> Task resume from paused
0:00:20.310957946  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:20.310990121  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.311026471  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:20.311062446  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.311115996  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:20.311134496  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.311160271  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:20.311186321  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.311484921  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:20.311511921  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.311541096  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:20.311571021  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.311646671  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:20.311665346  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.311690046  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:20.311732294  1491   0x7f84000ff0 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<source> pausing after gst_base_src_get_range() = flushing
0:00:20.311818421  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:20.311839746  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.311863921  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:20.311890446  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:20.311907971  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.311930921  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:20.311958621  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PAUSED pending VOID_PENDING, desired next READY
0:00:20.311998721  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to READY
0:00:20.312017396  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:20.312039696  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 2(READY) successfully
0:00:20.312060671  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 2(READY) successfully
0:00:20.312081196  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 2(READY) successfully
0:00:20.312109446  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PAUSED to READY, pending NULL, next NULL
0:00:20.312129821  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to READY (NULL pending)
0:00:20.312153196  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to NULL, final NULL
0:00:20.312200296  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current READY pending VOID_PENDING, desired next NULL
0:00:20.312224796  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to NULL
0:00:20.312242821  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.312312921  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 1(NULL) successfully
0:00:20.312340521  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current READY pending VOID_PENDING, desired next NULL
0:00:20.312362771  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink0> completed state change to NULL
0:00:20.312380396  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.312404921  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 1(NULL) successfully
0:00:20.312428871  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current READY pending VOID_PENDING, desired next NULL
0:00:20.312449396  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to NULL
0:00:20.312466746  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.312488096  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 1(NULL) successfully
0:00:20.312509446  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current READY pending VOID_PENDING, desired next NULL
0:00:20.312530096  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to NULL
0:00:20.312547496  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.312569171  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 1(NULL) successfully
0:00:20.312592071  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next NULL
0:00:20.312628946  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next NULL
0:00:20.312650496  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:20.312668321  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.312693871  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 1(NULL) successfully
0:00:20.312719021  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next NULL
0:00:20.312741021  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:20.312758346  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.312780671  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 1(NULL) successfully
0:00:20.312801746  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next NULL
0:00:20.312825821  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:20.312842746  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.312865021  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 1(NULL) successfully
0:00:20.312888146  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to NULL
0:00:20.312905521  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.312927721  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 1(NULL) successfully
0:00:20.312946796  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next NULL
0:00:20.312985071  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next NULL
0:00:20.313008021  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to NULL
0:00:20.313025371  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313067146  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 1(NULL) successfully
0:00:20.313089671  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next NULL
0:00:20.313111221  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to NULL
0:00:20.313128271  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313150096  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 1(NULL) successfully
0:00:20.313171696  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next NULL
0:00:20.313193196  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to NULL
0:00:20.313229096  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313252621  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 1(NULL) successfully
0:00:20.313274321  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next NULL
0:00:20.313295721  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:20.313312996  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313335321  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 1(NULL) successfully
0:00:20.313356821  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next NULL
0:00:20.313377096  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to NULL
0:00:20.313394071  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313416146  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 1(NULL) successfully
0:00:20.313436946  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next NULL
0:00:20.313457721  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to NULL
0:00:20.313474196  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313495871  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 1(NULL) successfully
0:00:20.313515346  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next NULL
0:00:20.313534921  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to NULL
0:00:20.313551846  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313573971  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 1(NULL) successfully
0:00:20.313595446  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to NULL
0:00:20.313612396  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313633921  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 1(NULL) successfully
0:00:20.313656446  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current READY pending VOID_PENDING, desired next NULL
0:00:20.313678596  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to NULL
0:00:20.313695521  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313716521  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 1(NULL) successfully
0:00:20.313736821  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 1(NULL) successfully
0:00:20.313755821  1491   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 1(NULL) successfully
0:00:20.313892096  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to NULL
0:00:20.313912296  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.313939421  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4035:finish_unprepare: Removing elements of stream 0 from pipeline
0:00:20.313958246  1491   0x7f84000b70 INFO              rtspstream rtsp-stream.c:4153:gst_rtsp_stream_leave_bin: stream 0x7f7803eb10 leaving bin
0:00:20.313979021  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking bin0:src_0(0x7f7803d5d0) and rtpbin0:send_rtp_sink_0(0x7f7400feb0)
0:00:20.314008621  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:20.314040371  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtp_src_0'
0:00:20.314060821  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpbin0:send_rtp_src_0(0x7f7400ece0) and tee0:sink(0x7f78059de0)
0:00:20.314084096  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpbin0:send_rtp_src_0 and tee0:sink
0:00:20.314110546  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:send_rtp_src(0x7f7400e800) and send_rtp_src_0:proxypad1(0x7f7400f060)
0:00:20.314134271  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:20.314160721  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtp_sink'
0:00:20.314181146  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking send_rtp_sink_0:proxypad2(0x7f74010230) and rtpsession0:send_rtp_sink(0x7f7400e420)
0:00:20.314200146  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:20.314217646  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtp_src'
0:00:20.314254146  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtp_sink_0'
0:00:20.314285696  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink0> completed state change to NULL
0:00:20.314316071  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking tee0:src_0(0x7f7805d1a0) and multiudpsink0:sink(0x7f7805c320)
0:00:20.314340121  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked tee0:src_0 and multiudpsink0:sink
0:00:20.314362171  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "multiudpsink0"
0:00:20.314396571  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<multiudpsink0> 0x7f7805bf40 dispose
0:00:20.314411896  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<multiudpsink0> removing pad 'sink'
0:00:20.314433571  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<multiudpsink0> 0x7f7805bf40 parent class dispose
0:00:20.314490371  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<multiudpsink0> 0x7f7805bf40 finalize
0:00:20.314506946  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<multiudpsink0> 0x7f7805bf40 finalize parent
0:00:20.314524946  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to NULL
0:00:20.314546296  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "tee0"
0:00:20.314570521  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<tee0> removing pad 'src_0'
0:00:20.314592671  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<tee0> 0x7f78059b40 dispose
0:00:20.314607021  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<tee0> removing pad 'sink'
0:00:20.314630771  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<tee0> 0x7f78059b40 parent class dispose
0:00:20.314647721  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<tee0> 0x7f78059b40 finalize
0:00:20.314662171  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<tee0> 0x7f78059b40 finalize parent
0:00:20.314686196  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:20.314703896  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:20.314725146  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to READY, final NULL
0:00:20.314788013  1491   0x7f84001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = flushing
0:00:20.314940821  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from PAUSED to READY, pending NULL, next NULL
0:00:20.314963821  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to READY (NULL pending)
0:00:20.314986546  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change READY to NULL, final NULL
0:00:20.315020671  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to NULL
0:00:20.315041596  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.315073896  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking udpsrc0:src(0x7f78056440) and funnel0:funnelpad0(0x7f78057810)
0:00:20.315098321  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked udpsrc0:src and funnel0:funnelpad0
0:00:20.315118096  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "udpsrc0"
0:00:20.315141321  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<udpsrc0> 0x7f78056080 dispose
0:00:20.315156496  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<udpsrc0> removing pad 'src'
0:00:20.315182496  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<udpsrc0> 0x7f78056080 parent class dispose
0:00:20.315207546  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<udpsrc0> 0x7f78056080 finalize
0:00:20.315223571  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<udpsrc0> 0x7f78056080 finalize parent
0:00:20.315256196  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:20.315274221  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:20.315295396  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to READY, final NULL
0:00:20.315353221  1491   0x7f84001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = flushing
0:00:20.315478496  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from PAUSED to READY, pending NULL, next NULL
0:00:20.315499721  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to READY (NULL pending)
0:00:20.315524596  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change READY to NULL, final NULL
0:00:20.315558896  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to NULL
0:00:20.315576871  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:20.315609521  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking udpsrc1:src(0x7f78058300) and funnel0:funnelpad1(0x7f780591a0)
0:00:20.315632896  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked udpsrc1:src and funnel0:funnelpad1
0:00:20.315652146  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "udpsrc1"
0:00:20.315679571  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<udpsrc1> 0x7f78057f70 dispose
0:00:20.315694471  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<udpsrc1> removing pad 'src'
0:00:20.315716921  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<udpsrc1> 0x7f78057f70 parent class dispose
0:00:20.315738446  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<udpsrc1> 0x7f78057f70 finalize
0:00:20.315753246  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<udpsrc1> 0x7f78057f70 finalize parent
0:00:20.315771921  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to NULL
0:00:20.315797271  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking tee1:src_0(0x7f7805fec0) and multiudpsink1:sink(0x7f7805f3c0)
0:00:20.315822221  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked tee1:src_0 and multiudpsink1:sink
0:00:20.315843496  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "multiudpsink1"
0:00:20.315866146  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to NULL
0:00:20.315891771  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpbin0:send_rtcp_src_0(0x7f74011530) and tee1:sink(0x7f7805ea50)
0:00:20.315915796  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:20.315938371  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "tee1"
0:00:20.315963446  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<tee1> removing pad 'src_0'
0:00:20.315986946  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<tee1> 0x7f7805e8b0 dispose
0:00:20.316003271  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<tee1> removing pad 'sink'
0:00:20.316027821  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<tee1> 0x7f7805e8b0 parent class dispose
0:00:20.316045371  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<tee1> 0x7f7805e8b0 finalize
0:00:20.316059746  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<tee1> 0x7f7805e8b0 finalize parent
0:00:20.316077996  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to NULL
0:00:20.316102096  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking funnel0:src(0x7f7804b280) and rtpbin0:recv_rtcp_sink_0(0x7f74012cd0)
0:00:20.316124746  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:20.316146196  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "funnel0"
0:00:20.316169096  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<funnel0> removing pad 'funnelpad0'
0:00:20.316192296  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<funnel0> removing pad 'funnelpad1'
0:00:20.316212846  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<funnel0> 0x7f7804b050 dispose
0:00:20.316227521  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<funnel0> removing pad 'src'
0:00:20.316250946  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<funnel0> 0x7f7804b050 parent class dispose
0:00:20.316266546  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<funnel0> 0x7f7804b050 finalize
0:00:20.316280571  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<funnel0> 0x7f7804b050 finalize parent
0:00:20.316299271  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'recv_rtcp_sink_0'
0:00:20.316320996  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking recv_rtcp_sink_0:proxypad4(0x7f74012f40) and rtpsession0:recv_rtcp_sink(0x7f74012070)
0:00:20.316340571  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:20.316360546  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'recv_rtcp_sink'
0:00:20.316378096  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'sync_src'
0:00:20.316398596  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:sync_src(0x7f740124b0) and rtpssrcdemux0:rtcp_sink(0x7f7400afe0)
0:00:20.316423771  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:20.316483371  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtcp_src_0'
0:00:20.316513897  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:send_rtcp_src(0x7f740112d0) and send_rtcp_src_0:proxypad3(0x7f740118b0)
0:00:20.316537572  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:20.316561747  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtcp_src'
0:00:20.316590697  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:20.316609022  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:20.316626047  1491   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:20.316646297  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpsession0"
0:00:20.316670297  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpsession0> 0x7f74005a70 dispose
0:00:20.316686647  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpsession0> 0x7f74005a70 parent class dispose
0:00:20.316715772  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpsession0> 0x7f74005a70 finalize
0:00:20.316731322  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpsession0> 0x7f74005a70 finalize parent
0:00:20.316752572  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpssrcdemux0"
0:00:20.316775822  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpssrcdemux0> 0x7f7400aa40 dispose
0:00:20.316791147  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux0> removing pad 'sink'
0:00:20.316812947  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux0> removing pad 'rtcp_sink'
0:00:20.316834447  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpssrcdemux0> 0x7f7400aa40 parent class dispose
0:00:20.316850997  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpssrcdemux0> 0x7f7400aa40 finalize
0:00:20.316865722  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpssrcdemux0> 0x7f7400aa40 finalize parent
0:00:20.316886622  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpstorage0"
0:00:20.316914947  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpstorage0> 0x7f7400bec0 dispose
0:00:20.316930447  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage0> removing pad 'src'
0:00:20.316954447  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage0> removing pad 'sink'
0:00:20.316979097  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpstorage0> 0x7f7400bec0 parent class dispose
0:00:20.316994547  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpstorage0> 0x7f7400bec0 finalize
0:00:20.317009097  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpstorage0> 0x7f7400bec0 finalize parent
0:00:20.317073872  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "rtpbin0"
0:00:20.317105172  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpbin0> 0x7f780465e0 dispose
0:00:20.317120972  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpbin0> 0x7f780465e0 parent class dispose
0:00:20.317141197  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpbin0> 0x7f780465e0 finalize
0:00:20.317156547  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpbin0> 0x7f780465e0 finalize parent
0:00:20.317226372  1491   0x7f84000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f78048190
0:00:20.317241507  1491   0x7f84000d80 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<multiudpsink1> 0x7f7805f000 dispose
0:00:20.317260583  1491   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<multiudpsink1> removing pad 'sink'
0:00:20.317244047  1491   0x7f84000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f7803a190 was not prepared
0:00:20.317284168  1491   0x7f84000d80 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<multiudpsink1> 0x7f7805f000 parent class dispose
0:00:20.317332668  1491   0x7f84000d80 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<multiudpsink1> 0x7f7805f000 finalize
0:00:20.317296972  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4169:gst_rtsp_media_unprepare: media 0x7f7803a190 was already unprepared
0:00:20.317345391  1491   0x7f84000d80 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<multiudpsink1> 0x7f7805f000 finalize parent
0:00:20.317386697  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x558c6c4b20: sending close message
0:00:20.317401563  1491   0x7f84000d80 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f7803f500
0:00:20.317512797  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x558c6c4b20: session 0x7f78049c90 removed
0:00:20.317536772  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x558c6c4b20: unwatch session 0x7f78049c90
0:00:20.317566722  1491   0x7f84000b70 INFO               rtspmedia rtsp-media.c:530:gst_rtsp_media_finalize: finalize media 0x7f7803a190
0:00:20.317616947  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "bin0"
0:00:20.317663997  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<media-pipeline> 0x7f7803ef40 dispose
0:00:20.317719572  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<media-pipeline> 0x7f7803ef40 parent class dispose
0:00:20.317736522  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<media-pipeline> 0x7f7803ef40 finalize
0:00:20.317752097  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<media-pipeline> 0x7f7803ef40 finalize parent
0:00:20.317780997  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking videoscale0:src(0x7f78017fa0) and capsfilter0:sink(0x7f7803c6c0)
0:00:20.317811022  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked videoscale0:src and capsfilter0:sink
0:00:20.317840047  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking capsfilter0:src(0x7f7803c910) and mpph264enc0:sink(0x7f7801c4f0)
0:00:20.317862597  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked capsfilter0:src and mpph264enc0:sink
0:00:20.317881997  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "capsfilter0"
0:00:20.317907547  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<capsfilter0> 0x7f780397d0 dispose
0:00:20.317923097  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter0> removing pad 'sink'
0:00:20.317945947  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter0> removing pad 'src'
0:00:20.317967597  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<capsfilter0> 0x7f780397d0 parent class dispose
0:00:20.317983422  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<capsfilter0> 0x7f780397d0 finalize
0:00:20.317997997  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<capsfilter0> 0x7f780397d0 finalize parent
0:00:20.318023297  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking pay0:src(0x7f78034d20) and src_0:proxypad0(0x7f7803d880)
0:00:20.318046322  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked pay0:src and src_0:proxypad0
0:00:20.318070997  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking h264parse0:src(0x7f78025dc0) and pay0:sink(0x7f78034fd0)
0:00:20.318093572  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked h264parse0:src and pay0:sink
0:00:20.318114797  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "pay0"
0:00:20.318143747  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mpph264enc0:src(0x7f7801c760) and h264parse0:sink(0x7f780259e0)
0:00:20.318166772  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mpph264enc0:src and h264parse0:sink
0:00:20.318188822  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "h264parse0"
0:00:20.318210097  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<h264parse0> 0x7f78024b90 dispose
0:00:20.318225472  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<h264parse0> removing pad 'sink'
0:00:20.318246347  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<h264parse0> removing pad 'src'
0:00:20.318267797  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<h264parse0> 0x7f78024b90 parent class dispose
0:00:20.318349672  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<h264parse0> 0x7f78024b90 finalize
0:00:20.318366722  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<h264parse0> 0x7f78024b90 finalize parent
0:00:20.318387422  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "mpph264enc0"
0:00:20.318408597  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mpph264enc0> 0x7f7801c070 dispose
0:00:20.318423672  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc0> removing pad 'sink'
0:00:20.318445072  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc0> removing pad 'src'
0:00:20.318467047  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mpph264enc0> 0x7f7801c070 parent class dispose
0:00:20.318494197  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mpph264enc0> 0x7f7801c070 finalize
0:00:20.318509622  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mpph264enc0> 0x7f7801c070 finalize parent
0:00:20.318534897  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking queue0:src(0x7f7800d820) and videoscale0:sink(0x7f78017cf0)
0:00:20.318559197  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked queue0:src and videoscale0:sink
0:00:20.318581522  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "videoscale0"
0:00:20.318602047  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<videoscale0> 0x7f78017910 dispose
0:00:20.318616822  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<videoscale0> removing pad 'sink'
0:00:20.318638197  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<videoscale0> removing pad 'src'
0:00:20.318659947  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<videoscale0> 0x7f78017910 parent class dispose
0:00:20.318740772  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<videoscale0> 0x7f78017910 finalize
0:00:20.318757697  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<videoscale0> 0x7f78017910 finalize parent
0:00:20.318782797  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking source:src(0x7f78007a60) and queue0:sink(0x7f7800d430)
0:00:20.318806772  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked source:src and queue0:sink
0:00:20.318829347  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "queue0"
0:00:20.318850247  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<queue0> 0x7f7800d050 dispose
0:00:20.318865097  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<queue0> removing pad 'sink'
0:00:20.318889472  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<queue0> removing pad 'src'
0:00:20.318914097  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<queue0> 0x7f7800d050 parent class dispose
0:00:20.318931697  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<queue0> 0x7f7800d050 finalize
0:00:20.318946247  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<queue0> 0x7f7800d050 finalize parent
0:00:20.318969447  1491   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "source"
0:00:20.318996147  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<source> 0x7f78007760 dispose
0:00:20.319011197  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<source> removing pad 'src'
0:00:20.319032872  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<source> 0x7f78007760 parent class dispose
0:00:20.319057172  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<source> 0x7f78007760 finalize
0:00:20.319071947  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<source> 0x7f78007760 finalize parent
0:00:20.319087072  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<bin0> 0x7f78036690 dispose
0:00:20.319101372  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<bin0> removing pad 'src_0'
0:00:20.319118447  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<bin0> 0x7f78036690 parent class dispose
0:00:20.319133947  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<bin0> 0x7f78036690 finalize
0:00:20.319148297  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<bin0> 0x7f78036690 finalize parent
0:00:20.319240797  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<pay0> 0x7f78034a40 dispose
0:00:20.319257522  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'src'
0:00:20.319278572  1491   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'sink'
0:00:20.319299697  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<pay0> 0x7f78034a40 parent class dispose
0:00:20.319338697  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<pay0> 0x7f78034a40 finalize
0:00:20.319354047  1491   0x7f84000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<pay0> 0x7f78034a40 finalize parent
0:00:20.319394797  1491   0x7f84000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f78049c90
0:00:20.319470797  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x558c6c4b20: connection closed
0:00:20.319494022  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x558c6c4b20: watch destroyed
0:00:20.319537147  1491   0x7f84000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x558c6c4b20
0:00:20.319623297  1491   0x7f84000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x558c6c51a0