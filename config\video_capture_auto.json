{"_description": "Video capture configuration with auto-selection examples", "video_capture_examples": {"fully_automatic": {"_comment": "Automatically select all parameters - best for plug-and-play", "source_type": "v4l2", "device": "/dev/video0", "width": 0, "height": 0, "fps": 0, "format": 0, "use_dma": true, "buffer_count": 4}, "resolution_specified": {"_comment": "Specify resolution, auto-select format and fps", "source_type": "v4l2", "device": "/dev/video0", "width": 1280, "height": 720, "fps": 0, "format": 0, "use_dma": true, "buffer_count": 4}, "format_specified": {"_comment": "Specify MJPEG format, auto-select resolution and fps", "source_type": "v4l2", "device": "/dev/video0", "width": 0, "height": 0, "fps": 0, "format": 1196444237, "_format_note": "1196444237 = V4L2_PIX_FMT_MJPEG", "use_dma": true, "buffer_count": 4}, "fully_manual": {"_comment": "Manually specify all parameters - fallback to auto if not supported", "source_type": "v4l2", "device": "/dev/video0", "width": 1920, "height": 1080, "fps": 30, "format": 1448695129, "_format_note": "1448695129 = V4L2_PIX_FMT_YUYV", "use_dma": true, "buffer_count": 4}}, "common_pixel_formats": {"_comment": "Common V4L2 pixel format values for reference", "YUYV": 1448695129, "UYVY": 1498831189, "MJPEG": 1196444237, "H264": 875967048, "H265": 1211250229, "RGB24": 859981650, "BGR24": 861030210, "YUV420": 842093913, "NV12": 842094158, "NV21": 825382478}, "usage_examples": {"command_line": {"_comment": "Command line usage examples", "auto_all": "./video_capture_main --auto", "auto_resolution": "./video_capture_main --width 0 --height 0", "auto_fps": "./video_capture_main --fps 0", "auto_format": "./video_capture_main --format 0", "mixed": "./video_capture_main --width 1280 --height 720 --fps 0 --format 0"}}}