{"_description": "Video capture configuration with strict matching and auto-selection examples", "video_capture_examples": {"fully_automatic": {"_comment": "Automatically select all parameters - best for device exploration", "source_type": "v4l2", "device": "/dev/video0", "width": 0, "height": 0, "fps": 0, "format": 0, "use_dma": true, "buffer_count": 4}, "strict_all_parameters": {"_comment": "Strictly enforce all parameters - program exits if not supported", "source_type": "v4l2", "device": "/dev/video0", "width": 1920, "height": 1080, "fps": 30, "format": 1448695129, "_format_note": "1448695129 = V4L2_PIX_FMT_YUYV - MUST be supported", "use_dma": true, "buffer_count": 4}, "format_priority": {"_comment": "Specify MJPEG format (highest priority), auto-select others", "source_type": "v4l2", "device": "/dev/video0", "width": 0, "height": 0, "fps": 0, "format": 1196444237, "_format_note": "1196444237 = V4L2_PIX_FMT_MJPEG - MUST be supported", "use_dma": true, "buffer_count": 4}, "fps_priority": {"_comment": "Specify 60fps (medium priority), auto-select format and size", "source_type": "v4l2", "device": "/dev/video0", "width": 0, "height": 0, "fps": 60, "format": 0, "_fps_note": "60fps MUST be supported or program exits", "use_dma": true, "buffer_count": 4}, "size_priority": {"_comment": "Specify 4K resolution (lowest priority), auto-select format and fps", "source_type": "v4l2", "device": "/dev/video0", "width": 3840, "height": 2160, "fps": 0, "format": 0, "_size_note": "4K resolution MUST be supported or program exits", "use_dma": true, "buffer_count": 4}}, "common_pixel_formats": {"_comment": "Common V4L2 pixel format values - supports both fourcc and integer", "_usage": "Use fourcc strings (YUYV, MJPG) or integer values in configuration", "YUYV": 1448695129, "UYVY": 1498831189, "MJPG": 1196444237, "H264": 875967048, "H265": 1211250229, "RGB3": 859981650, "BGR3": 861030210, "YUV4": 842093913, "NV12": 842094158, "NV21": 825382478, "_note": "Command line: --format YUYV or --format 1448695129", "_help": "Use --format help to see all supported formats"}, "usage_examples": {"command_line": {"_comment": "Command line usage examples with strict matching", "auto_all": "./video_capture_main --auto", "strict_format_int": "./video_capture_main --format 1196444237", "strict_format_fourcc": "./video_capture_main --format MJPG", "strict_fps": "./video_capture_main --fps 60", "strict_size": "./video_capture_main --width 1920 --height 1080", "strict_all": "./video_capture_main --width 1920 --height 1080 --fps 30 --format 1448695129", "mixed_auto": "./video_capture_main --format MJPG --width 0 --height 0 --fps 0", "show_formats": "./video_capture_main --format help", "_note": "Specified parameters are strictly enforced - program exits if not supported"}, "priority_examples": {"_comment": "Parameter priority demonstration", "format_wins": {"_scenario": "If device supports MJPEG@720p@30fps but not MJPEG@1080p@60fps", "_command": "./video_capture_main --format 1196444237 --width 1920 --height 1080 --fps 60", "_result": "Program exits - format has highest priority but combination not supported"}, "fps_wins": {"_scenario": "If device supports YUYV@720p@30fps but not YUYV@720p@60fps", "_command": "./video_capture_main --fps 60 --width 1280 --height 720 --format 0", "_result": "Program exits - fps specified but not supported at that resolution"}, "size_flexible": {"_scenario": "Si<PERSON> has lowest priority, more likely to find matches", "_command": "./video_capture_main --width 1280 --height 720 --fps 0 --format 0", "_result": "More likely to succeed - only size is strictly enforced"}}}}