#ifndef RTSP_SERVER_H
#define RTSP_SERVER_H

#include "common.h"
#include "capture_config.h"
#include <gst/gst.h>
#include <gst/rtsp-server/rtsp-server.h>
#include <gst/app/gstappsrc.h>
#include <gst/video/video.h>
#include <linux/videodev2.h>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <chrono>

// 视频格式转换器 - 高效零拷贝转换
class VideoFormatConverter {
private:
    GstElement* pipeline_;
    GstElement* appsrc_;
    GstElement* appsink_;
    GstElement* videoconvert_;
    GstElement* videoscale_;
    GstElement* encoder_;
    GstElement* capsfilter_input_;
    GstElement* capsfilter_output_;
    
    // 输入输出格式
    int input_width_, input_height_;
    int input_format_;
    int output_width_, output_height_;
    int output_fps_;
    bool use_hardware_;
    
    // 性能监控
    std::atomic<uint64_t> frames_converted_{0};
    std::atomic<uint64_t> conversion_time_us_{0};
    
public:
    VideoFormatConverter() : pipeline_(nullptr), appsrc_(nullptr), appsink_(nullptr),
                           videoconvert_(nullptr), videoscale_(nullptr), encoder_(nullptr),
                           capsfilter_input_(nullptr), capsfilter_output_(nullptr),
                           input_width_(0), input_height_(0), input_format_(0),
                           output_width_(1280), output_height_(720), output_fps_(30),
                           use_hardware_(true) {}
    
    ~VideoFormatConverter() { cleanup(); }
    
    bool init(int input_width, int input_height, int input_format,
              int output_width, int output_height, int output_fps,
              bool use_hardware = true);
    
    bool convert_frame(const Frame& input_frame, GstBuffer** output_buffer);
    void cleanup();
    
    // 获取统计信息
    uint64_t get_frames_converted() const { return frames_converted_.load(); }
    double get_avg_conversion_time_ms() const {
        uint64_t frames = frames_converted_.load();
        return frames > 0 ? (double)conversion_time_us_.load() / frames / 1000.0 : 0.0;
    }
};

// RTSP媒体工厂 - 为每个客户端创建媒体流
class RTSPMediaFactory {
private:
    RTSPServerConfig config_;
    std::shared_ptr<DDSVideoReader> dds_reader_;
    std::unique_ptr<VideoFormatConverter> converter_;
    GstRTSPMediaFactory* factory_;

    // 当前视频参数
    std::atomic<int> current_width_{0};
    std::atomic<int> current_height_{0};
    std::atomic<int> current_format_{0};
    std::atomic<bool> format_changed_{false};

    // 性能统计
    std::atomic<uint64_t> clients_connected_{0};
    std::atomic<uint64_t> frames_served_{0};

public:
    RTSPMediaFactory(const RTSPServerConfig& config);
    ~RTSPMediaFactory();

    bool init();
    void cleanup();
    GstRTSPMediaFactory* get_factory() { return factory_; }

    // GStreamer回调
    static void media_configure_callback(GstRTSPMediaFactory* factory, GstRTSPMedia* media, gpointer user_data);
    static void need_data_callback(GstElement* appsrc, guint unused, gpointer user_data);
    static void enough_data_callback(GstElement* appsrc, gpointer user_data);

    // 统计信息
    uint64_t get_clients_connected() const { return clients_connected_.load(); }
    uint64_t get_frames_served() const { return frames_served_.load(); }

private:
    bool wait_for_first_frame();
    std::string v4l2_format_to_gst_format(int v4l2_format);
    void configure_media(GstRTSPMedia* media);
    void feed_data(GstElement* appsrc);
    bool update_converter_if_needed(const Frame& frame);
    std::string create_pipeline_description();
};

// RTSP服务器服务
class RTSPServerService {
private:
    RTSPServerConfig config_;
    
    // GStreamer RTSP服务器组件
    GstRTSPServer* server_;
    GstRTSPMountPoints* mounts_;
    std::unique_ptr<RTSPMediaFactory> factory_;
    
    // 服务控制
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_requested_{false};
    std::thread server_thread_;
    
    // 性能监控
    std::atomic<uint64_t> total_connections_{0};
    std::atomic<uint64_t> active_connections_{0};
    std::chrono::steady_clock::time_point start_time_;
    
    // 错误处理
    std::atomic<uint64_t> error_count_{0};
    std::string last_error_;
    std::mutex error_mutex_;
    
public:
    RTSPServerService() : server_(nullptr), mounts_(nullptr) {}
    ~RTSPServerService() { stop(); }
    
    bool init(const RTSPServerConfig& config);
    bool start();
    void stop();
    bool is_running() const { return running_.load(); }
    
    // 统计信息
    struct ServerStats {
        uint64_t total_connections;
        uint64_t active_connections;
        uint64_t frames_served;
        uint64_t clients_connected;
        double uptime_seconds;
        uint64_t error_count;
        std::string last_error;
        double avg_conversion_time_ms;
    };
    
    ServerStats get_stats() const;
    void print_stats() const;
    
    // 配置更新
    bool update_bitrate(int new_bitrate);
    bool update_quality(int new_gop_size);
    
private:
    void run();
    void handle_client_connected();
    void handle_client_disconnected();
    
    // GStreamer回调
    static void client_connected_callback(GstRTSPServer* server, GstRTSPClient* client, gpointer user_data);
    static void client_disconnected_callback(GstRTSPServer* server, GstRTSPClient* client, gpointer user_data);
};

// 便利函数
namespace RTSPServerUtils {
    // 初始化GStreamer
    bool init_gstreamer();

    // 调试功能
    void set_gstreamer_debug_level(int level);

    // 格式转换辅助函数
    std::string v4l2_format_to_gst_caps(int v4l2_format, int width, int height, int fps);
    std::string create_encoder_pipeline(const RTSPServerConfig& config, bool use_hardware);

    // 性能优化
    void optimize_gst_pipeline_for_realtime(GstElement* pipeline);
    bool check_hardware_encoder_support(const std::string& codec);

    // 网络优化
    void configure_rtsp_transport_params(GstRTSPServer* server);
    void set_rtsp_buffer_sizes(GstRTSPServer* server, int buffer_size);
}

#endif // RTSP_SERVER_H
