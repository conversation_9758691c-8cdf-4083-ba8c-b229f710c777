#!/bin/bash

echo "=== RTSP Server Debug: appsrc vs v4l2src ==="
echo "对比测试appsrc(FastDDS)和v4l2src的差异"
echo

# 编译项目
echo "1. 编译项目..."
cd /home/<USER>/video_service
make clean > /dev/null 2>&1
if make > build.log 2>&1; then
    echo "✅ 编译成功"
else
    echo "❌ 编译失败，查看 build.log"
    tail -10 build.log
    exit 1
fi
echo

# 测试1: 使用v4l2src的rtsp_server_pipe (已知工作)
echo "2. 测试v4l2src版本 (rtsp_server_pipe)..."
echo "启动rtsp_server_pipe在端口8556..."
timeout 15 ./build/rtsp_server_pipe --gst-debug=3 -p 8556 -u /stream \
    -i "( v4l2src device=/dev/video0 ! video/x-raw,format=YUY2,width=640,height=480,framerate=30/1 ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )" \
    > v4l2_test.log 2>&1 &
V4L2_PID=$!

sleep 3

# 测试v4l2src连接
echo "测试VLC连接v4l2src版本..."
timeout 8 vlc --intf dummy --play-and-exit rtsp://************:8556/stream > /dev/null 2>&1 &
VLC1_PID=$!

sleep 10
kill $V4L2_PID 2>/dev/null
wait $V4L2_PID 2>/dev/null

echo "✅ v4l2src测试完成"
echo

# 测试2: 使用appsrc的rtsp_server_dds (新版本)
echo "3. 测试appsrc版本 (rtsp_server_dds)..."
echo "启动rtsp_server_dds在端口8557..."
timeout 15 ./build/rtsp_server_dds --gst-debug=3 -p 8557 -u /stream \
    > appsrc_test.log 2>&1 &
APPSRC_PID=$!

sleep 3

# 测试appsrc连接
echo "测试VLC连接appsrc版本..."
timeout 8 vlc --intf dummy --play-and-exit rtsp://************:8557/stream > /dev/null 2>&1 &
VLC2_PID=$!

sleep 10
kill $APPSRC_PID 2>/dev/null
wait $APPSRC_PID 2>/dev/null

echo "✅ appsrc测试完成"
echo

# 分析结果
echo "4. 分析对比结果..."
echo

echo "=== V4L2SRC版本分析 ==="
if [ -f v4l2_test.log ]; then
    # 检查RTP统计
    rtp_packets_v4l2=$(grep -o "packets-sent=(guint64)[0-9]*" v4l2_test.log | tail -1 | grep -o "[0-9]*")
    rtp_octets_v4l2=$(grep -o "octets-sent=(guint64)[0-9]*" v4l2_test.log | tail -1 | grep -o "[0-9]*")
    
    echo "RTP统计:"
    echo "  发送包数: ${rtp_packets_v4l2:-0}"
    echo "  发送字节: ${rtp_octets_v4l2:-0}"
    
    # 检查pipeline状态
    if grep -q "PLAYING" v4l2_test.log; then
        echo "✅ Pipeline状态: PLAYING"
    else
        echo "⚠️  Pipeline状态: 未达到PLAYING"
    fi
    
    # 检查编码器
    if grep -q "mpph264enc" v4l2_test.log; then
        echo "✅ 硬件编码器: 正常加载"
    else
        echo "⚠️  硬件编码器: 未找到"
    fi
else
    echo "❌ v4l2_test.log 不存在"
fi
echo

echo "=== APPSRC版本分析 ==="
if [ -f appsrc_test.log ]; then
    # 检查DDS数据
    dds_frames=$(grep -c "Read DDS frame" appsrc_test.log)
    need_data_count=$(grep -c "NEED DATA CALLBACK" appsrc_test.log)
    
    echo "DDS数据:"
    echo "  读取帧数: $dds_frames"
    echo "  need-data回调: $need_data_count"
    
    # 检查RTP统计
    rtp_packets_appsrc=$(grep -o "packets-sent=(guint64)[0-9]*" appsrc_test.log | tail -1 | grep -o "[0-9]*")
    rtp_octets_appsrc=$(grep -o "octets-sent=(guint64)[0-9]*" appsrc_test.log | tail -1 | grep -o "[0-9]*")
    
    echo "RTP统计:"
    echo "  发送包数: ${rtp_packets_appsrc:-0}"
    echo "  发送字节: ${rtp_octets_appsrc:-0}"
    
    # 检查pipeline状态
    if grep -q "PLAYING" appsrc_test.log; then
        echo "✅ Pipeline状态: PLAYING"
    else
        echo "⚠️  Pipeline状态: 未达到PLAYING"
    fi
    
    # 检查appsrc配置
    if grep -q "Configured appsrc properties" appsrc_test.log; then
        echo "✅ appsrc配置: 成功"
    else
        echo "⚠️  appsrc配置: 可能有问题"
    fi
else
    echo "❌ appsrc_test.log 不存在"
fi
echo

# 对比分析
echo "=== 对比分析 ==="
if [ -n "$rtp_packets_v4l2" ] && [ -n "$rtp_packets_appsrc" ]; then
    echo "RTP包数对比:"
    echo "  v4l2src: $rtp_packets_v4l2 包"
    echo "  appsrc:  $rtp_packets_appsrc 包"
    
    if [ "$rtp_packets_v4l2" -gt "$rtp_packets_appsrc" ]; then
        echo "🔍 v4l2src发送了更多RTP包，appsrc可能有数据流问题"
    elif [ "$rtp_packets_appsrc" -gt 1 ]; then
        echo "✅ appsrc也在发送RTP包，问题可能在其他地方"
    else
        echo "❌ appsrc几乎没有发送RTP包，确认是appsrc数据流问题"
    fi
fi

if [ "$dds_frames" -gt 0 ]; then
    echo "✅ DDS数据正常，问题在appsrc到pipeline的数据传递"
else
    echo "❌ DDS数据读取有问题，需要检查DDS连接"
fi
echo

# 关键差异分析
echo "=== 关键差异分析 ==="
echo "1. 数据源差异:"
echo "   - v4l2src: 直接从摄像头读取，连续数据流"
echo "   - appsrc: 从DDS读取，需要手动推送"
echo
echo "2. 时间戳差异:"
echo "   - v4l2src: GStreamer自动生成时间戳"
echo "   - appsrc: 需要手动设置时间戳"
echo
echo "3. 数据流控制:"
echo "   - v4l2src: GStreamer内部控制"
echo "   - appsrc: 依赖need-data/enough-data回调"
echo

# 调试建议
echo "=== 调试建议 ==="
if [ "$dds_frames" -gt 0 ] && [ "${rtp_packets_appsrc:-0}" -le 1 ]; then
    echo "🔧 问题定位: appsrc数据推送机制"
    echo "建议检查:"
    echo "1. appsrc的caps设置是否与实际数据匹配"
    echo "2. 时间戳设置是否正确"
    echo "3. need-data回调是否持续触发"
    echo "4. GstBuffer的创建和推送是否正确"
    echo
    echo "🧪 下一步调试:"
    echo "1. 在appsrc版本中添加更详细的日志"
    echo "2. 检查GstBuffer的PTS/DTS设置"
    echo "3. 验证caps协商过程"
    echo "4. 对比两个版本的GStreamer调试信息"
elif [ "$dds_frames" -eq 0 ]; then
    echo "🔧 问题定位: DDS数据读取"
    echo "建议检查:"
    echo "1. DDS topic是否正确发布数据"
    echo "2. video_capture是否正常运行"
    echo "3. DDS网络连接是否正常"
else
    echo "✅ 两个版本都工作正常，问题可能在其他地方"
fi

echo
echo "=== 测试完成 ==="
echo "详细日志:"
echo "  v4l2src版本: v4l2_test.log"
echo "  appsrc版本: appsrc_test.log"
