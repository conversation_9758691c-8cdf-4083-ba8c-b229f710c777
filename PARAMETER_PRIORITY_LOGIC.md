# VideoCaptureService 参数优先级和严格匹配逻辑

## 核心逻辑变更

根据用户要求，修改了VideoCaptureService的参数处理逻辑：

### 🎯 **新的行为规则**

1. **严格匹配原则** - 指定的参数必须被设备支持，否则程序退出
2. **参数优先级** - format > fps > size（从高到低）
3. **自动选择** - 只有设为0的参数才会自动选择
4. **不再回退** - 不支持指定参数时直接退出，不回退到自动选择

## 🔄 **匹配算法流程**

### 1. 参数分类
```cpp
bool auto_format = (config_.format == 0);
bool auto_fps = (config_.fps <= 0);
bool auto_size = (config_.width <= 0 || config_.height <= 0);
```

### 2. 处理模式
- **完全自动** - 所有参数都为0，使用`findTheBestFormat()`
- **严格匹配** - 有任何指定参数，使用`find_matching_format()`

### 3. 严格匹配算法
按优先级顺序匹配：

```cpp
// 优先级1: Format (最高)
if (!auto_format && fmtdesc.pixelformat != config.format) {
    continue; // 格式不匹配，跳过
}

// 优先级2: FPS (中等)  
if (!auto_fps && device_fps != config.fps) {
    continue; // 帧率不匹配，跳过
}

// 优先级3: Size (最低)
if (!auto_size && (width != config.width || height != config.height)) {
    continue; // 尺寸不匹配，跳过
}
```

## 📋 **使用场景示例**

### 场景1: 严格指定格式
```bash
./video_capture_main --format 1196444237  # MJPEG
```
- **行为**: 必须使用MJPEG格式，其他参数自动选择
- **失败**: 如果设备不支持MJPEG，程序退出

### 场景2: 严格指定帧率
```bash
./video_capture_main --fps 60
```
- **行为**: 必须60fps，格式和尺寸自动选择
- **失败**: 如果设备不支持60fps，程序退出

### 场景3: 严格指定尺寸
```bash
./video_capture_main --width 1920 --height 1080
```
- **行为**: 必须1080p，格式和帧率自动选择
- **失败**: 如果设备不支持1080p，程序退出

### 场景4: 混合指定
```bash
./video_capture_main --format 1196444237 --fps 30 --width 0 --height 0
```
- **行为**: 必须MJPEG@30fps，尺寸自动选择
- **失败**: 如果设备不支持MJPEG@30fps的任何尺寸，程序退出

## 🚨 **错误处理**

### 失败日志示例
```
[INFO] Using specified parameters (format=1196444237, fps=60, size=1920x1080)
[ERROR] Device does not support the specified parameters
[ERROR] Required: format=1196444237, fps=60, size=1920x1080
[ERROR] Failed to init V4L2 device
```

### 成功日志示例
```
[INFO] Using specified parameters (format=1196444237, fps=0, size=0x0)
[INFO] Found matching format: 1280x720@30fps, format=1196444237
[INFO] Final format: 1280x720@30fps, format=1196444237
```

## 🔧 **代码实现要点**

### 1. 新增函数
- `find_matching_format()` - 按优先级严格匹配参数
- 移除了`validate_v4l2_format()` - 功能合并到匹配算法中

### 2. 修改的文件
- `include/video_capture.h` - 核心匹配逻辑
- `src/video_capture_main.cpp` - 命令行参数和帮助信息
- `include/capture_config.h` - 配置结构注释
- `config/config.json` - 配置文件注释
- `config/video_capture_auto.json` - 示例配置

### 3. 关键逻辑变更
```cpp
// 旧逻辑: 回退机制
if (!validate_v4l2_format(v4l2_fd_, config_)) {
    LOG_W("Specified format not supported, falling back to auto-selection");
    // 回退到自动选择...
}

// 新逻辑: 严格匹配
if (!find_matching_format(v4l2_fd_, config_, selectedFormat, auto_format, auto_fps, auto_size)) {
    LOG_E("Device does not support the specified parameters");
    return false; // 直接退出
}
```

## 🎯 **优势**

1. **明确的错误反馈** - 参数不支持时立即退出，便于问题定位
2. **严格的配置控制** - 确保系统按预期参数运行
3. **优先级清晰** - format > fps > size的优先级便于理解
4. **灵活的混合模式** - 可以指定部分参数，其余自动选择

## 📝 **使用建议**

1. **开发阶段** - 先用`--auto`探测设备能力
2. **测试阶段** - 逐个指定参数测试支持情况
3. **生产环境** - 使用严格参数确保一致性
4. **问题排查** - 关注错误日志中的"Required"信息

这个修改确保了系统在指定配置下的严格性和可预测性，同时保持了自动选择的灵活性。
