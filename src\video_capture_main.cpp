#include "video_capture.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>
#include <cstring>
#include <map>
#include <string>
#include <algorithm>

// 全局变量
std::unique_ptr<VideoCaptureService> g_capture_service;
std::atomic<bool> g_stop{false};

// V4L2像素格式转换 - 遵循v4l2-ctl工具的转换规则
// 将四字符格式名称转换为V4L2像素格式整数值
std::map<std::string, uint32_t> pixel_format_map = {
    // 常用YUV格式
    {"YUYV", 0x56595559},  // V4L2_PIX_FMT_YUYV - 1448695129
    {"UYVY", 0x59565955},  // V4L2_PIX_FMT_UYVY - 1498831189
    {"YUV4", 0x34565559},  // V4L2_PIX_FMT_YUV420 - 842093913
    {"YU12", 0x32315559},  // V4L2_PIX_FMT_YUV420 - 842093913
    {"NV12", 0x3231564E},  // V4L2_PIX_FMT_NV12 - 842094158
    {"NV21", 0x3132564E},  // V4L2_PIX_FMT_NV21 - 825382478

    // RGB格式
    {"RGB3", 0x33424752},  // V4L2_PIX_FMT_RGB24 - 859981650
    {"BGR3", 0x33524742},  // V4L2_PIX_FMT_BGR24 - 861030210
    {"RGBP", 0x50424752},  // V4L2_PIX_FMT_RGB565 - 1346520914
    {"BGRP", 0x50524742},  // V4L2_PIX_FMT_BGR565 - 1346454594

    // 压缩格式
    {"MJPG", 0x47504A4D},  // V4L2_PIX_FMT_MJPEG - 1196444237
    {"JPEG", 0x4745504A},  // V4L2_PIX_FMT_JPEG - 1195724874
    {"H264", 0x34363248},  // V4L2_PIX_FMT_H264 - 875967048
    {"H265", 0x35363248},  // V4L2_PIX_FMT_H265 - 1211250229
    {"HEVC", 0x43564548},  // V4L2_PIX_FMT_HEVC - 1129727304

    // 其他格式
    {"GREY", 0x59455247},  // V4L2_PIX_FMT_GREY - 1497715271
    {"Y16 ", 0x20363159},  // V4L2_PIX_FMT_Y16 - 540422489
};

// 将四字符格式字符串转换为V4L2像素格式整数
uint32_t fourcc_to_pixelformat(const std::string& fourcc) {
    // 转换为大写
    std::string upper_fourcc = fourcc;
    std::transform(upper_fourcc.begin(), upper_fourcc.end(), upper_fourcc.begin(), ::toupper);

    // 查找预定义映射
    auto it = pixel_format_map.find(upper_fourcc);
    if (it != pixel_format_map.end()) {
        return it->second;
    }

    // 如果没有找到预定义映射，按照v4l2-ctl的规则直接转换
    // 四字符字符串转换为32位整数（小端序）
    if (fourcc.length() == 4) {
        return (static_cast<uint32_t>(fourcc[0]) << 0) |
               (static_cast<uint32_t>(fourcc[1]) << 8) |
               (static_cast<uint32_t>(fourcc[2]) << 16) |
               (static_cast<uint32_t>(fourcc[3]) << 24);
    }

    return 0; // 无效格式
}

// 将V4L2像素格式整数转换为四字符字符串（用于显示）
std::string pixelformat_to_fourcc(uint32_t pixelformat) {
    char fourcc[5] = {0};
    fourcc[0] = static_cast<char>((pixelformat >> 0) & 0xFF);
    fourcc[1] = static_cast<char>((pixelformat >> 8) & 0xFF);
    fourcc[2] = static_cast<char>((pixelformat >> 16) & 0xFF);
    fourcc[3] = static_cast<char>((pixelformat >> 24) & 0xFF);
    return std::string(fourcc);
}

// 解析格式参数（支持整数和四字符字符串）
uint32_t parse_format_parameter(const char* optarg) {
    if (!optarg) return 0;

    std::string format_str(optarg);

    // 尝试解析为整数
    char* endptr;
    long format_int = strtol(optarg, &endptr, 0);
    if (*endptr == '\0' && format_int >= 0) {
        // 成功解析为整数
        return static_cast<uint32_t>(format_int);
    }

    // 尝试解析为四字符格式
    uint32_t format_fourcc = fourcc_to_pixelformat(format_str);
    if (format_fourcc != 0) {
        return format_fourcc;
    }

    // 解析失败
    std::cerr << "Invalid format: " << optarg << std::endl;
    std::cerr << "Supported formats: YUYV, UYVY, MJPG, H264, RGB3, BGR3, NV12, etc." << std::endl;
    std::cerr << "Or use integer value (e.g., 1448695129 for YUYV)" << std::endl;
    return 0;
}

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_capture_service) {
                g_capture_service->stop();
            }
            g_stop.store(true);
            break;
        case SIGUSR1:
        case SIGUSR2:
            if (g_capture_service) {
                g_capture_service->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -s, --source TYPE     Video source type (v4l2|rtsp)\n"
              << "  -d, --device PATH     V4L2 device path (default: /dev/video0)\n"
              << "  -u, --url URL         RTSP URL\n"
              << "  -w, --width WIDTH     Video width (0=auto, default: 1280)\n"
              << "  -h, --height HEIGHT   Video height (0=auto, default: 720)\n"
              << "  -f, --fps FPS         Frame rate (0=auto, default: 30)\n"
              << "  --format FORMAT       Pixel format (0=auto, YUYV, MJPG, H264, etc.)\n"
              << "  -t, --tcp             Use TCP for RTSP (default: UDP)\n"
              << "  --no-dma              Disable DMA buffers\n"
              << "  --auto                Auto-select all parameters\n"
              << "  --help                Show this help message\n"
              << "\n"
              << "Parameter Priority (when specified):\n"
              << "  1. Format (highest) - Must be supported, program exits if not\n"
              << "  2. FPS (medium)     - Must be supported, program exits if not\n"
              << "  3. Size (lowest)    - Must be supported, program exits if not\n"
              << "\n"
              << "Auto-selection (set to 0):\n"
              << "  Only parameters set to 0 will be auto-selected\n"
              << "  Specified parameters are strictly enforced\n"
              << "\n"
              << "Format specification:\n"
              << "  Four-character codes: YUYV, UYVY, MJPG, H264, RGB3, BGR3, NV12, etc.\n"
              << "  Integer values: 1448695129 (YUYV), 1196444237 (MJPG), etc.\n"
              << "  Use 0 for auto-selection\n";
}

int main(int argc, char* argv[]) {    
    // 默认配置
    CaptureConfig config;
    config.source_type = V4L2_SOURCE;
    config.device = "/dev/video0";
    config.width = 1280;
    config.height = 720;
    config.fps = 30;
    config.use_tcp = false;
    config.use_dma = true;
    config.buffer_count = 4;
    
    // 解析命令行参数
    static struct option long_options[] = {
        {"source", required_argument, 0, 's'},
        {"device", required_argument, 0, 'd'},
        {"url", required_argument, 0, 'u'},
        {"width", required_argument, 0, 'w'},
        {"height", required_argument, 0, 'h'},
        {"fps", required_argument, 0, 'f'},
        {"format", required_argument, 0, 'F'},
        {"tcp", no_argument, 0, 't'},
        {"no-dma", no_argument, 0, 1},
        {"auto", no_argument, 0, 2},
        {"help", no_argument, 0, 3},
        {0, 0, 0, 0}
    };
    
    int c;
    while ((c = getopt_long(argc, argv, "s:d:u:w:h:f:F:t", long_options, nullptr)) != -1) {
        switch (c) {
            case 's':
                if (strcmp(optarg, "v4l2") == 0) {
                    config.source_type = V4L2_SOURCE;
                } else if (strcmp(optarg, "rtsp") == 0) {
                    config.source_type = RTSP_SOURCE;
                } else {
                    std::cerr << "Invalid source type: " << optarg << std::endl;
                    return 1;
                }
                break;
            case 'd':
                config.device = optarg;
                break;
            case 'u':
                config.url = optarg;
                break;
            case 'w':
                config.width = atoi(optarg);
                break;
            case 'h':
                config.height = atoi(optarg);
                break;
            case 'f':
                config.fps = atoi(optarg);
                break;
            case 'F':
                config.format = parse_format_parameter(optarg);
                if (config.format == 0 && strcmp(optarg, "0") != 0) {
                    // 解析失败且不是显式的"0"
                    return 1;
                }
                break;
            case 't':
                config.use_tcp = true;
                break;
            case 1:
                config.use_dma = false;
                break;
            case 2:
                // --auto: 启用所有参数的自动选择
                config.width = 0;
                config.height = 0;
                config.fps = 0;
                config.format = 0;
                std::cout << "Auto-selection enabled for all parameters\n";
                break;
            case 3:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // 验证配置
    if (config.source_type == RTSP_SOURCE && config.url.empty()) {
        std::cerr << "RTSP URL is required for RTSP source" << std::endl;
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    
    LOG_I("Starting video capture service...");
    LOG_I("Source: %s", (config.source_type == V4L2_SOURCE) ? "V4L2" : "RTSP");
    if (config.source_type == V4L2_SOURCE) {
        LOG_I("Device: %s, Resolution: %dx%d, FPS: %d, DMA: %s",
              config.device.c_str(), config.width, config.height, config.fps,
              config.use_dma ? "enabled" : "disabled");
    } else {
        LOG_I("URL: %s, TCP: %s", config.url.c_str(), config.use_tcp ? "yes" : "no");
    }
    
    try {
        // 创建并初始化服务
        g_capture_service = std::make_unique<VideoCaptureService>();
        if (!g_capture_service->init(config)) {
            LOG_E("Failed to initialize video capture service");
            return 1;
        }
       
        g_stop.store(false);
        // 启动服务
        g_capture_service->start();
        
        // 主循环 - 定期输出统计信息
        while (!g_stop.load()) {
            VideoCaptureService::Stats stats;
            std::this_thread::sleep_for(std::chrono::seconds(5));
            g_capture_service->get_stats(stats);
            LOG_I("Stats - Captured: %lu, Dropped: %lu, FPS: %.1f, CPU: %.1f%%",
                  stats.frames_captured, stats.frames_dropped, stats.fps, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("Video capture service stopped");
    return 0;
}
