{"video_capture": {"source_type": "v4l2", "device": "/dev/video0", "rtsp_url": "", "_comment_parameters": "0=auto-select, specified value=strict match (exit if not supported)", "_comment_priority": "Priority when specified: format > fps > size", "width": 1280, "height": 720, "fps": 30, "format": 0, "_comment_format": "Pixel format: 0=auto, 'YUYV'=1448695129, 'MJPG'=1196444237, 'H264'=875967048, etc.", "use_tcp": false, "use_dma": true, "buffer_count": 4, "ffmpeg": {"rtsp_timeout_ms": 5000, "rtsp_retry_count": 3, "buffer_size": 1024000, "debug_level": 2}}, "video_converter": {"enable_hardware_acceleration": true, "ai_output_format": "RGB24", "cloud_output_format": "H264", "ai_resolution": {"width": 640, "height": 640}, "cloud_resolution": {"width": 1280, "height": 720}}, "ai_processor": {"engine_type": "onnx", "model_path": "/opt/models/yolov5s.onnx", "confidence_threshold": 0.5, "input_width": 640, "input_height": 640, "num_classes": 80, "class_names": ["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat", "traffic light", "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat", "dog", "horse", "sheep", "cow", "elephant", "bear", "zebra", "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee", "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove", "skateboard", "surfboard", "tennis racket", "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple", "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch", "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse", "remote", "keyboard", "cell phone", "microwave", "oven", "toaster", "sink", "refrigerator", "book", "clock", "vase", "scissors", "teddy bear", "hair drier", "toothbrush"]}, "cloud_streamer": {"type": "rtmp", "rtmp_url": "rtmp://live.example.com/live/stream_key", "webrtc_signaling_server": "wss://signaling.example.com", "webrtc_room_id": "room1", "bitrate": 2000000, "keyframe_interval": 60}, "dds": {"domain_id": 0, "topics": {"video_frames": "Video_Frames", "ai_frames": "AI_Frames", "cloud_frames": "Cloud_Frames", "ai_results": "AI_Results"}}, "rtsp_server": {"dds_topic": "Video_Frames", "server_address": "0.0.0.0", "server_port": 8554, "mount_point": "/stream", "output_video": {"width": 1280, "height": 720, "fps": 30, "codec": "H264", "bitrate": 2000000, "gop_size": 30}, "encoder": {"use_hardware_encoder": true, "fallback_to_software": true, "preset": "low-latency", "rate_control": "CBR"}, "performance": {"zero_copy_mode": true, "buffer_size": 5, "max_clients": 10, "thread_priority": 80}}, "logging": {"level": "INFO", "file": "/var/log/video_service.log", "max_size_mb": 100, "max_files": 5}, "system": {"log_level": "INFO", "log_file": "/var/log/video_service/video_service.log", "pid_file": "/var/run/video_service.pid", "working_directory": "/opt/video_service", "cpu_affinity": {"video_capture": [0, 1], "video_converter": [2, 3], "ai_processor": [4, 5], "cloud_streamer": [6, 7], "rtsp_server": [8, 9]}, "priority": {"video_capture": 90, "video_converter": 80, "ai_processor": 70, "cloud_streamer": 60, "rtsp_server": 85}}}