# RTSP Server 修改总结

## 修改概述

根据用户要求，对RTSP服务器进行了以下三个主要修改：

1. **移除视频缩放功能** - RTSP服务器不再对从Fast DDS获取的视频进行缩放，按原尺寸编码
2. **移除输出尺寸参数** - 从配置和参数中移除width和height设置
3. **添加MJPEG解码支持** - 对于V4L2_PIX_FMT_MJPEG格式的视频，在pipeline中增加mppjpegdec解码器

## 详细修改内容

### 1. 配置结构修改

**文件**: `include/capture_config.h`
- 移除 `output_width` 和 `output_height` 字段
- 保留 `output_fps` 用于帧率控制

```cpp
// 修改前
int output_width = 1280;
int output_height = 720;
int output_fps = 30;

// 修改后  
int output_fps = 30;
```

### 2. VideoFormatConverter类修改

**文件**: `include/rtsp_server.h` 和 `src/rtsp_server.cpp`

#### 头文件修改
- 移除 `output_width_` 和 `output_height_` 成员变量
- 修改 `init()` 函数签名，移除输出尺寸参数

```cpp
// 修改前
bool init(int input_width, int input_height, int input_format,
          int output_width, int output_height, int output_fps,
          bool use_hardware = true);

// 修改后
bool init(int input_width, int input_height, int input_format,
          int output_fps, bool use_hardware = true);
```

#### 实现文件修改
- 更新 `init()` 函数实现
- 移除输出尺寸相关的初始化和日志

### 3. Pipeline生成逻辑修改

**文件**: `src/rtsp_server.cpp`

#### 移除缩放逻辑
```cpp
// 修改前 - 包含缩放逻辑
if (input_width != config_.output_width || input_height != config_.output_height) {
    pipeline << "! videoscale ! video/x-raw,width=" << config_.output_width;
    pipeline << ",height=" << config_.output_height << " ";
}

// 修改后 - 移除缩放，直接处理
```

#### 添加MJPEG解码支持
```cpp
// 新增 - MJPEG格式解码
if (input_format == V4L2_PIX_FMT_MJPEG) {
    pipeline << "! mppjpegdec ";
}
```

### 4. 命令行参数修改

**文件**: `src/rtsp_server_main.cpp`

#### 移除width和height选项
- 从帮助信息中移除 `-w, --width` 和 `-h, --height` 选项
- 从 `getopt_long` 选项字符串中移除 `w:h:`
- 移除对应的case处理逻辑
- 移除JSON配置文件中的width/height解析
- 更新配置验证逻辑

#### 更新配置打印
```cpp
// 修改前
LOG_I("Output: %dx%d@%dfps, %s, %d bps", 
      config.output_width, config.output_height, config.output_fps,
      config.output_codec.c_str(), config.output_bitrate);

// 修改后
LOG_I("Output: Original size@%dfps, %s, %d bps", 
      config.output_fps, config.output_codec.c_str(), config.output_bitrate);
```

### 5. 测试文件修改

**文件**: `test/test_rtsp_server.cpp` 和 `test/test_rtsp_simple.cpp`
- 移除测试配置中的 `output_width` 和 `output_height` 设置
- 更新无效配置测试逻辑

## Pipeline处理流程

### 原始视频格式 (YUY2, UYVY, I420等)
```
appsrc -> queue -> [编码器] -> rtpXXXpay
```

### MJPEG格式 (新增)
```
appsrc -> queue -> mppjpegdec -> [编码器] -> rtpXXXpay
```

### 已编码格式 (H264, H265)
```
appsrc -> hXXXparse -> rtpXXXpay
```

## 关键优势

1. **保持原始质量** - 不进行缩放，避免质量损失
2. **减少计算开销** - 移除不必要的缩放处理
3. **支持MJPEG** - 通过mppjpegdec硬件解码器高效处理MJPEG格式
4. **简化配置** - 减少用户需要配置的参数

## 编译和测试

修改完成后，建议进行以下测试：

```bash
# 编译
make clean
make rtsp_server_main

# 测试不同格式
./build/rtsp_server_main --topic Video_Frames --fps 30

# 使用VLC测试
vlc rtsp://************:8554/stream
```

## 注意事项

1. **MJPEG支持** - 确保系统支持mppjpegdec硬件解码器
2. **原始尺寸** - 输出视频将保持DDS输入的原始尺寸
3. **配置兼容性** - 旧的配置文件中的width/height参数将被忽略
4. **性能优化** - 移除缩放后，整体性能应有所提升

所有修改都已完成，代码应该可以正常编译和运行。
