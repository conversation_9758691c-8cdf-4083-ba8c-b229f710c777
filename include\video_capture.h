
#ifndef VIDEO_CAPTURE_H
#define VIDEO_CAPTURE_H

#include "common.h"
#include <condition_variable>
#include <atomic>
#include <queue>
#include <thread>

// V4L2相关结构
struct V4L2Buffer {
    void* start;
    size_t length;
    int dma_fd;
};

// FFmpeg RTSP客户端封装 (仅获取编码包，不解码)
class RTSPClient {
private:
    AVFormatContext* format_ctx_;
    AVPacket* packet_;
    int video_stream_index_;
    std::string url_;
    bool use_tcp_;
    bool connected_;

    // 流信息
    AVCodecParameters* codec_params_;
    int width_;
    int height_;
    AVRational time_base_;



public:
    RTSPClient() : format_ctx_(nullptr), packet_(nullptr), video_stream_index_(-1),
                   use_tcp_(false), connected_(false), codec_params_(nullptr),
                   width_(0), height_(0) {
        time_base_ = {1, 90000}; // 默认RTP时间基
    }

    ~RTSPClient() {
        cleanup();
    }

    bool init(const std::string& url, bool use_tcp = false, int timeout_us = 1000000);
    Frame get_frame();  // 返回编码包而非解码帧
    void cleanup();
    bool is_connected() const { return connected_; }

    // 获取流信息
    int get_width() const { return width_; }
    int get_height() const { return height_; }
    AVCodecParameters* get_codec_params() const { return codec_params_; }
};

class VideoCaptureService {

    typedef struct {
        int width;
        int height;
        int denominator;
        int numerator;
        int pixelformat;    // V4L2_PIX_FMT_XXX
    } VideoFormat;

private:
    CaptureConfig config_;
    std::atomic<bool> stop_requested_{false};
    std::atomic<bool> running_{false};
    std::thread capture_thread_;

    // V4L2相关
    int v4l2_fd_ = -1;
    std::vector<V4L2Buffer> v4l2_buffers_;
    v4l2_format v4l2_fmt_;
    bool use_dma_ = false;

    // RTSP相关
    std::unique_ptr<RTSPClient> rtsp_client_;

    // DDS
    std::unique_ptr<DDSVideoWriter> dds_writer_;

    // 统计信息
    std::atomic<uint64_t> frame_id_counter_{0};
    std::atomic<uint64_t> frames_captured_{0};
    std::atomic<uint64_t> frames_dropped_{0};

    // 性能监控
    CPUMonitor cpu_monitor_;
    std::chrono::steady_clock::time_point last_adjust_time_;
    int current_frame_interval_ms_ = 33;  // 30fps

    // 查找最合适的格式
    VideoFormat findTheBestFormat(int fd, CaptureConfig& config) {
        VideoFormat bestFormat = {0, 0, 0, 0, 0};
        
        struct v4l2_fmtdesc fmtdesc;
        memset(&fmtdesc, 0, sizeof(fmtdesc));
        fmtdesc.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        while (ioctl(fd, VIDIOC_ENUM_FMT, &fmtdesc) == 0) {
            fmtdesc.index++;
            if (fmtdesc.pixelformat != V4L2_PIX_FMT_YUYV &&
                fmtdesc.pixelformat != V4L2_PIX_FMT_NV12 &&
                fmtdesc.pixelformat != V4L2_PIX_FMT_RGB24 &&
                fmtdesc.pixelformat != V4L2_PIX_FMT_MJPEG &&
                fmtdesc.pixelformat != V4L2_PIX_FMT_BGR24) { 
                continue;
            }
            struct v4l2_frmsizeenum frmsizeenum;
            memset(&frmsizeenum, 0, sizeof(frmsizeenum));
            frmsizeenum.pixel_format = fmtdesc.pixelformat;
            while (ioctl(fd, VIDIOC_ENUM_FRAMESIZES, &frmsizeenum) == 0) {
                frmsizeenum.index++;
                if (frmsizeenum.type != V4L2_FRMSIZE_TYPE_DISCRETE) {
                    continue;
                }

                const uint32_t fmt_size = frmsizeenum.discrete.width * frmsizeenum.discrete.height;
                const uint32_t config_size = config.width * config.height;
                const float size_ratio = static_cast<float>(fmt_size) / static_cast<float>(config_size);
                if (size_ratio > 2.3 || size_ratio < 0.3) {
                    continue;
                }
                struct v4l2_frmivalenum frmivalenum;
                memset(&frmivalenum, 0, sizeof(frmivalenum));
                frmivalenum.pixel_format = fmtdesc.pixelformat;
                frmivalenum.width = frmsizeenum.discrete.width;
                frmivalenum.height = frmsizeenum.discrete.height;
                while (ioctl(fd, VIDIOC_ENUM_FRAMEINTERVALS, &frmivalenum) == 0) {
                    frmivalenum.index++;
                    if (frmivalenum.type != V4L2_FRMIVAL_TYPE_DISCRETE) {
                        continue;
                    }
                    int fps = frmivalenum.discrete.denominator / frmivalenum.discrete.numerator;
                    if (fps >= 25) {
                        bestFormat.width = frmsizeenum.discrete.width;
                        bestFormat.height = frmsizeenum.discrete.height;
                        bestFormat.denominator = frmivalenum.discrete.denominator;
                        bestFormat.numerator = frmivalenum.discrete.numerator;
                        bestFormat.pixelformat = fmtdesc.pixelformat;
                        LOG_I("Find best format width: %d, height: %d, fps: %d, format: %d", bestFormat.width, bestFormat.height, fps, bestFormat.pixelformat);
                        return bestFormat;
                    }
                }
            }
        }
        return bestFormat;
    }

public:
    VideoCaptureService() = default;
    ~VideoCaptureService() {
        stop();
    }

    bool init(const CaptureConfig& config) {
        config_ = config;

        // 初始化DDS Writer
        dds_writer_ = std::make_unique<DDSVideoWriter>("Video_Frames", 5);
        if (!dds_writer_) {
            LOG_E("Failed to initialize DDS writer");
            return false;
        }
        LOG_I("Create capture dds writer success");

        if (config.source_type == V4L2_SOURCE) {
            return init_v4l2();
        } else if (config.source_type == V4L2_SOURCE) {
            return init_rtsp();
        }

        LOG_E("Unknown video source type");
        return false;
    }


    void start() {
        if (running_.load()) {
            LOG_W("Video capture already running");
            return;
        }

        stop_requested_.store(false);
        capture_thread_ = std::thread(&VideoCaptureService::run, this);
        running_.store(true);
        LOG_I("Video capture started");
    }

    void stop() {
        if (!running_.load()) {
            return;
        }

        stop_requested_.store(true);
        if (capture_thread_.joinable()) {
            capture_thread_.join();
        }
        running_.store(false);

        cleanup();
        LOG_I("Video capture stopped");
    }

    void run() {
        LOG_I("Video capture thread started");
        last_adjust_time_ = std::chrono::steady_clock::now();

        while (!stop_requested_.load()) {
            try {
                auto frame = capture_frame();
                if (frame.valid) {
                    if (dds_writer_->write(frame)) {
                        frames_captured_.fetch_add(1);
                    } else {
                        frames_dropped_.fetch_add(1);
                        LOG_W("Failed to publish frame %lu", frame.frame_id);
                    }
                } else {
                    frames_dropped_.fetch_add(1);
                }

                // 动态帧率控制
                adjust_capture_rate();

            } catch (const std::exception& e) {
                LOG_E("Capture loop exception: %s", e.what());
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LOG_I("Video capture thread stopped");
    }

    // 信号处理函数
    void handle_signal(int signal) {
        switch (signal) {
            case SIGUSR1:
                // 降低分辨率/帧率
                current_frame_interval_ms_ = std::min(current_frame_interval_ms_ + 20, 100);
                LOG_I("Signal SIGUSR1: reduced framerate to %d ms", current_frame_interval_ms_);
                break;
            case SIGUSR2:
                // 恢复分辨率/帧率
                current_frame_interval_ms_ = std::max(current_frame_interval_ms_ - 20, 33);
                LOG_I("Signal SIGUSR2: increased framerate to %d ms", current_frame_interval_ms_);
                break;
            default:
                break;
        }
    }

    // 获取统计信息
    struct Stats {
        uint64_t frames_captured;
        uint64_t frames_dropped;
        float fps;
        float cpu_usage;
    };

    void get_stats(Stats &stats) {
        static auto last_time = std::chrono::steady_clock::now();
        static uint64_t last_frames = 0;

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_time).count();

        uint64_t current_frames = frames_captured_.load();
        float fps = 0.0f;
        if (elapsed > 0) {
            fps = (float)(current_frames - last_frames) / elapsed;
        }

        last_time = now;
        last_frames = current_frames;

        stats.frames_captured = current_frames;
        stats.frames_dropped = frames_dropped_.load();
        stats.fps = fps;
        stats.cpu_usage = cpu_monitor_.get_usage();
    }

private:
    bool init_v4l2() {
        // 打开设备
        LOG_I("Start init v4l2 device: %s", config_.device.c_str());
        v4l2_fd_ = open(config_.device.c_str(), O_RDWR | O_NONBLOCK);
        if (v4l2_fd_ < 0) {
            LOG_E("Failed to open V4L2 device %s: %s",
                  config_.device.c_str(), strerror(errno));
            return false;
        }
        LOG_I("Open device %s success", config_.device.c_str());

        // 查询设备能力
        v4l2_capability cap;
        if (ioctl(v4l2_fd_, VIDIOC_QUERYCAP, &cap) < 0) {
            LOG_E("Failed to query V4L2 capabilities: %s", strerror(errno));
            return false;
        }

        if (!(cap.capabilities & V4L2_CAP_VIDEO_CAPTURE)) {
            LOG_E("Device does not support video capture");
            return false;
        }
        LOG_I("Find capabilities of video capture device.");

        VideoFormat bestFormat = findTheBestFormat(v4l2_fd_, config_);
        if (bestFormat.width == 0) {
            LOG_E("Failed to find suitable format");
            return false;
        }
        config_.width = bestFormat.width;
        config_.height = bestFormat.height;
        config_.fps = bestFormat.denominator / bestFormat.numerator;
        config_.format = bestFormat.pixelformat;
        LOG_D("Best format: %dx%d, %d/%d, %d", bestFormat.width, bestFormat.height,
              bestFormat.denominator, bestFormat.numerator, bestFormat.pixelformat);

        // 设置格式
        memset(&v4l2_fmt_, 0, sizeof(v4l2_fmt_));
        v4l2_fmt_.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        v4l2_fmt_.fmt.pix.width = bestFormat.width;
        v4l2_fmt_.fmt.pix.height = bestFormat.height;
        v4l2_fmt_.fmt.pix.pixelformat = bestFormat.pixelformat;
        v4l2_fmt_.fmt.pix.field = V4L2_FIELD_INTERLACED;

        if (ioctl(v4l2_fd_, VIDIOC_S_FMT, &v4l2_fmt_) < 0) {
            LOG_E("Failed to set V4L2 format: %s", strerror(errno));
            return false;
        }

        // 设置帧率
        v4l2_streamparm parm;
        memset(&parm, 0, sizeof(parm));
        parm.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        parm.parm.capture.timeperframe.numerator = bestFormat.numerator;
        parm.parm.capture.timeperframe.denominator = bestFormat.denominator;

        if (ioctl(v4l2_fd_, VIDIOC_S_PARM, &parm) < 0) {
            LOG_W("Failed to set framerate, using default");
        }

        // 初始化缓冲区
        return init_v4l2_buffers();
    }

    bool init_v4l2_buffers() {
        // 请求缓冲区
        v4l2_requestbuffers req;
        memset(&req, 0, sizeof(req));
        req.count = config_.buffer_count;
        req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;

        // 优先尝试DMA缓冲区
        if (config_.use_dma) {
            req.memory = V4L2_MEMORY_DMABUF;
            if (ioctl(v4l2_fd_, VIDIOC_REQBUFS, &req) == 0) {
                use_dma_ = true;
                LOG_I("Using DMA buffers");
            } else {
                LOG_W("DMA buffers not supported, falling back to MMAP");
            }
        }

        // 回退到MMAP
        if (!use_dma_) {
            req.memory = V4L2_MEMORY_MMAP;
            if (ioctl(v4l2_fd_, VIDIOC_REQBUFS, &req) < 0) {
                LOG_E("Failed to request V4L2 buffers: %s", strerror(errno));
                return false;
            }
        }

        // 映射缓冲区
        v4l2_buffers_.resize(req.count);
        for (uint32_t i = 0; i < req.count; ++i) {
            v4l2_buffer buf;
            memset(&buf, 0, sizeof(buf));
            buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            buf.memory = req.memory;
            buf.index = i;

            if (ioctl(v4l2_fd_, VIDIOC_QUERYBUF, &buf) < 0) {
                LOG_E("Failed to query buffer %d: %s", i, strerror(errno));
                return false;
            }

            if (use_dma_) {
                v4l2_buffers_[i].dma_fd = buf.m.fd;
                v4l2_buffers_[i].length = buf.length;
                v4l2_buffers_[i].start = nullptr;
            } else {
                v4l2_buffers_[i].start = mmap(nullptr, buf.length,
                                            PROT_READ | PROT_WRITE, MAP_SHARED,
                                            v4l2_fd_, buf.m.offset);
                if (v4l2_buffers_[i].start == MAP_FAILED) {
                    LOG_E("Failed to mmap buffer %d: %s", i, strerror(errno));
                    return false;
                }
                v4l2_buffers_[i].length = buf.length;
                v4l2_buffers_[i].dma_fd = -1;
            }

            // 将缓冲区加入队列
            if (ioctl(v4l2_fd_, VIDIOC_QBUF, &buf) < 0) {
                LOG_E("Failed to enqueue buffer %d: %s", i, strerror(errno));
                return false;
            }
        }

        // 开始捕获
        enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        if (ioctl(v4l2_fd_, VIDIOC_STREAMON, &type) < 0) {
            LOG_E("Failed to start V4L2 streaming: %s", strerror(errno));
            return false;
        }

        LOG_I("V4L2 initialized: %dx%d, %d buffers, DMA=%s",
              config_.width, config_.height, req.count, use_dma_ ? "yes" : "no");
        return true;
    }

    bool init_rtsp() {
        rtsp_client_ = std::make_unique<RTSPClient>();
        if (!rtsp_client_->init(config_.url, config_.use_tcp, config_.timeout_us)) {
            LOG_E("Failed to initialize RTSP client");
            return false;
        }

        LOG_I("RTSP client initialized: %s", config_.url.c_str());
        return true;
    }

    Frame capture_frame() {
        if (config_.source_type == V4L2_SOURCE) {
            return capture_v4l2_frame();
        } else if (config_.source_type == RTSP_SOURCE) {
            return capture_rtsp_frame();
        } else {
            return Frame(); //无效帧
        }
    }

    Frame capture_v4l2_frame() {
        Frame frame;

        // 等待帧就绪
        fd_set fds;
        FD_ZERO(&fds);
        FD_SET(v4l2_fd_, &fds);

        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 100000;  // 100ms超时

        int ret = select(v4l2_fd_ + 1, &fds, nullptr, nullptr, &tv);
        if (ret <= 0) {
            if (ret < 0 && errno != EINTR) {
                LOG_E("V4L2 select error: %s", strerror(errno));
            }
            return frame;  // 超时或错误
        }

        // 出队缓冲区
        v4l2_buffer buf;
        memset(&buf, 0, sizeof(buf));
        buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        buf.memory = use_dma_ ? V4L2_MEMORY_DMABUF : V4L2_MEMORY_MMAP;

        if (ioctl(v4l2_fd_, VIDIOC_DQBUF, &buf) < 0) {
            if (errno != EAGAIN) {
                LOG_E("Failed to dequeue V4L2 buffer: %s", strerror(errno));
            }
            return frame;
        }

        // 填充帧信息
        frame.frame_id = frame_id_counter_.fetch_add(1);
        frame.timestamp = get_current_us();
        frame.width = v4l2_fmt_.fmt.pix.width;
        frame.height = v4l2_fmt_.fmt.pix.height;
        frame.format = config_.format;
        frame.source_type = V4L2_SOURCE;  // V4L2
        frame.is_keyframe = true;  // V4L2帧都是关键帧
        frame.valid = true;

        if (use_dma_) {
            frame.dma_fd = dup(v4l2_buffers_[buf.index].dma_fd);  // 复制文件描述符
            frame.data_length = buf.bytesused;
        } else {
            // 复制数据到软件缓冲区
            frame.data.resize(buf.bytesused);
            memcpy(frame.data.data(), v4l2_buffers_[buf.index].start, buf.bytesused);
        }

        // 重新入队缓冲区
        if (ioctl(v4l2_fd_, VIDIOC_QBUF, &buf) < 0) {
            LOG_E("Failed to requeue V4L2 buffer: %s", strerror(errno));
        }

        return frame;
    }

    Frame capture_rtsp_frame() {
        Frame frame;

        if (!rtsp_client_ || !rtsp_client_->is_connected()) {
            return frame;
        }

        try {
            frame = rtsp_client_->get_frame();
            if (frame.valid) {
                frame.frame_id = frame_id_counter_.fetch_add(1);
                frame.source_type = RTSP_SOURCE;  // RTSP
            }
        } catch (const std::exception& e) {
            LOG_E("RTSP capture exception: %s", e.what());
        }

        return frame;
    }
    
    void adjust_capture_rate() {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            now - last_adjust_time_).count();

        // 每5秒调整一次
        if (elapsed >= 5) {
            float cpu_usage = cpu_monitor_.get_usage();

            if (cpu_usage > 80.0f) {
                // CPU负载高，降低帧率
                current_frame_interval_ms_ = std::min(current_frame_interval_ms_ + 10, 100);
                LOG_I("High CPU usage (%.1f%%), reducing framerate to %d ms interval",
                      cpu_usage, current_frame_interval_ms_);
            } else if (cpu_usage < 50.0f && current_frame_interval_ms_ > 33) {
                // CPU负载低，提高帧率
                current_frame_interval_ms_ = std::max(current_frame_interval_ms_ - 10, 33);
                LOG_I("Low CPU usage (%.1f%%), increasing framerate to %d ms interval",
                      cpu_usage, current_frame_interval_ms_);
            }

            last_adjust_time_ = now;
        }

        // 控制帧率
        std::this_thread::sleep_for(std::chrono::milliseconds(current_frame_interval_ms_));
    }

    void cleanup() {
        // 停止V4L2流
        if (v4l2_fd_ >= 0) {
            enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            ioctl(v4l2_fd_, VIDIOC_STREAMOFF, &type);

            // 释放缓冲区
            for (auto& buffer : v4l2_buffers_) {
                if (buffer.start && buffer.start != MAP_FAILED) {
                    munmap(buffer.start, buffer.length);
                }
                if (buffer.dma_fd >= 0) {
                    close(buffer.dma_fd);
                }
            }
            v4l2_buffers_.clear();

            close(v4l2_fd_);
            v4l2_fd_ = -1;
        }

        // 清理RTSP客户端
        rtsp_client_.reset();

        LOG_I("Video capture cleanup completed");
    }
};

// RTSPClient FFmpeg实现 (仅获取编码包)
bool RTSPClient::init(const std::string& url, bool use_tcp, int timeout_us) {
    url_ = url;
    use_tcp_ = use_tcp;

    // 初始化FFmpeg
    avformat_network_init();

    // 分配格式上下文
    format_ctx_ = avformat_alloc_context();
    if (!format_ctx_) {
        LOG_E("Failed to allocate format context");
        return false;
    }

    // 设置选项
    AVDictionary* options = nullptr;
    if (!use_tcp) {
        av_dict_set(&options, "rtsp_transport", "udp", 1);
        av_dict_set_int(&options, "buffer_size", 102400, 0);
    }

    // 设置实时参数 - 优化NALU获取实时性
    av_dict_set(&options, "fflags", "nobuffer+flush_packets+discardcorrupt", 0);
    av_dict_set(&options, "flags", "low_delay", 0);
    av_dict_set(&options, "avioflags", "direct", 0);
    av_dict_set_int(&options, "analyzeduration", 50000, 0);      // 减少分析时间 (50ms)
    av_dict_set_int(&options, "stimeout", timeout_us, 0);
    av_dict_set_int(&options, "probesize", 2048, 0);             // 减少探测大小
    av_dict_set_int(&options, "max_delay", 0, 0);                // 最小延迟
    av_dict_set_int(&options, "reorder_queue_size", 0, 0);       // 禁用重排序队列
    av_dict_set(&options, "tune", "zerolatency", 0);             // 零延迟调优

    // RTSP传输协议选择 - UDP优先，TCP备选
    if (use_tcp_) {
        av_dict_set(&options, "rtsp_transport", "tcp", 0);       // 使用TCP传输(可靠但延迟稍高)
        av_dict_set(&options, "rtsp_flags", "prefer_tcp", 0);    // 优先TCP
        av_dict_set_int(&options, "buffer_size", 1024*64, 0);    // TCP: 64KB缓冲区
        LOG_I("RTSP using TCP transport for reliability");
    } else {
        av_dict_set(&options, "rtsp_transport", "udp", 0);       // 使用UDP传输(低延迟首选)
        av_dict_set(&options, "rtsp_flags", "prefer_udp", 0);    // 优先UDP
        av_dict_set_int(&options, "buffer_size", 1024*32, 0);    // UDP: 32KB缓冲区(更小)

        // UDP特定优化参数
        av_dict_set_int(&options, "fifo_size", 1024*1024, 0);    // UDP: 1MB FIFO缓冲
        av_dict_set_int(&options, "overrun_nonfatal", 1, 0);     // UDP丢包不致命
        av_dict_set(&options, "fflags", "nobuffer+flush_packets+discardcorrupt+genpts", 0);

        LOG_I("RTSP using UDP transport for low latency");
    }
    av_dict_set(&options, "max_interleave_delta", "0", 0);       // 禁用交错

    // 网络优化参数
    av_dict_set_int(&options, "recv_buffer_size", 1024*1024, 0); // 1MB接收缓冲
    av_dict_set(&options, "user_agent", "VideoService/1.0", 0);  // 自定义User-Agent


    // 打开输入
    if (avformat_open_input(&format_ctx_, url.c_str(), nullptr, &options) < 0) {
        LOG_E("Failed to open RTSP stream: %s", url.c_str());
        av_dict_free(&options);
        return false;
    }
    av_dict_free(&options);

    // 设置format context的实时参数 - 进一步优化实时性
    format_ctx_->flags |= AVFMT_FLAG_NOBUFFER | AVFMT_FLAG_FLUSH_PACKETS | AVFMT_FLAG_DISCARD_CORRUPT;
    format_ctx_->max_analyze_duration = 50000;    // 最大分析时间50ms
    format_ctx_->max_interleave_delta = 0;        // 禁用交错延迟
    format_ctx_->max_ts_probe = 1;                // 最小时间戳探测
    format_ctx_->fps_probe_size = 1;              // 最小FPS探测
    format_ctx_->max_streams = 2;                 // 限制流数量(视频+音频)

    // 查找流信息
    if (avformat_find_stream_info(format_ctx_, nullptr) < 0) {
        LOG_E("Failed to find stream info");
        return false;
    }

    // 查找视频流
    video_stream_index_ = -1;
    for (unsigned int i = 0; i < format_ctx_->nb_streams; i++) {
        if (format_ctx_->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            video_stream_index_ = i;
            break;
        }
    }

    if (video_stream_index_ == -1) {
        LOG_E("No video stream found");
        return false;
    }

    // 获取流信息
    codec_params_ = format_ctx_->streams[video_stream_index_]->codecpar;
    width_ = codec_params_->width;
    height_ = codec_params_->height;
    time_base_ = format_ctx_->streams[video_stream_index_]->time_base;

    // 分配包
    packet_ = av_packet_alloc();
    if (!packet_) {
        LOG_E("Failed to allocate packet");
        return false;
    }

    connected_ = true;

    LOG_I("RTSP client initialized: %s, codec: %s, size: %dx%d, TCP=%s",
          url.c_str(), avcodec_get_name(codec_params_->codec_id),
          width_, height_, use_tcp ? "yes" : "no");

    return true;
}



Frame RTSPClient::get_frame() {
    Frame frame;

    if (!connected_) {
        return frame;
    }

    // 直接读取packet，无缓冲，低延迟
    int ret = av_read_frame(format_ctx_, packet_);
    if (ret < 0) {
        if (ret == AVERROR_EOF) {
            LOG_W("RTSP stream ended");
            connected_ = false;
        } else if (ret == AVERROR(EAGAIN)) {
            // 暂时没有数据，返回无效帧
            return frame;
        } else if (ret == AVERROR(ETIMEDOUT)) {
            LOG_W("RTSP read timeout, continuing...");
            return frame;
        } else {
            LOG_E("Failed to read RTSP frame: %d", ret);
            connected_ = false;
        }
        return frame;
    }

    // 实时性优化：检查包的时间戳，丢弃过时的帧
    if (packet_->pts != AV_NOPTS_VALUE) {
        int64_t current_time = get_current_us();
        int64_t packet_time = av_rescale_q(packet_->pts,
                                         format_ctx_->streams[packet_->stream_index]->time_base,
                                         AV_TIME_BASE_Q);

        // 如果包的时间戳比当前时间早超过100ms，丢弃该包
        if (current_time - packet_time > 100000) { // 100ms
            av_packet_unref(packet_);
            return frame; // 返回无效帧，继续读取下一个
        }
    }

    // 检查是否是视频包
    if (packet_->stream_index != video_stream_index_) {
        av_packet_unref(packet_);
        return frame; // 返回无效帧，让调用者继续尝试
    }

    // 创建Frame对象，包含编码数据
    frame.timestamp = get_current_us();
    frame.width = width_;
    frame.height = height_;

    // 根据编码类型设置格式
    if (codec_params_->codec_id == AV_CODEC_ID_H264) {
        frame.format = V4L2_PIX_FMT_H264;
    } else if (codec_params_->codec_id == AV_CODEC_ID_HEVC) {
        frame.format = V4L2_PIX_FMT_H265;
    } else {
        frame.format = V4L2_PIX_FMT_H264; // 默认
    }

    frame.is_keyframe = (packet_->flags & AV_PKT_FLAG_KEY) != 0;
    frame.valid = true;

    // 复制编码数据 (NALU)
    frame.data.resize(packet_->size);
    memcpy(frame.data.data(), packet_->data, packet_->size);

    // 释放包引用
    av_packet_unref(packet_);

    return frame;
}

void RTSPClient::cleanup() {
    connected_ = false;

    // 释放FFmpeg资源
    if (packet_) {
        av_packet_free(&packet_);
        packet_ = nullptr;
    }

    if (format_ctx_) {
        avformat_close_input(&format_ctx_);
        format_ctx_ = nullptr;
    }

    // 重置状态
    video_stream_index_ = -1;
    codec_params_ = nullptr;
    width_ = 0;
    height_ = 0;

    LOG_I("RTSP client cleanup completed");
}

#endif // VIDEO_CAPTURE_H

