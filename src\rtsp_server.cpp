#include "common.h"
#include "rtsp_server.h"
#include "capture_config.h"
#include <sstream>
#include <iomanip>
#include <thread>

// 静态辅助函数：V4L2格式到GStreamer格式转换
static std::string v4l2_to_gst_format(int v4l2_format) {
    switch (v4l2_format) {
        case V4L2_PIX_FMT_YUYV:
            return "YUY2";
        case V4L2_PIX_FMT_UYVY:
            return "UYVY";
        case V4L2_PIX_FMT_YUV420:
            return "I420";
        case V4L2_PIX_FMT_YVU420:
            return "YV12";
        case V4L2_PIX_FMT_NV12:
            return "NV12";
        case V4L2_PIX_FMT_NV21:
            return "NV21";
        case V4L2_PIX_FMT_RGB24:
            return "RGB";
        case V4L2_PIX_FMT_BGR24:
            return "BGR";
        case V4L2_PIX_FMT_RGB32:
            return "RGBx";
        case V4L2_PIX_FMT_BGR32:
            return "BGRx";
        case V4L2_PIX_FMT_MJPEG:
            return "MJPG";
        case V4L2_PIX_FMT_H264:
            return "H264";
        case V4L2_PIX_FMT_H265:
            return "H265";
        default:
            LOG_W("Unknown V4L2 format: 0x%08x, defaulting to I420", v4l2_format);
            return "I420";
    }
}

// VideoFormatConverter 实现
bool VideoFormatConverter::init(int input_width, int input_height, int input_format,
                               int output_width, int output_height, int output_fps,
                               bool use_hardware) {
    input_width_ = input_width;
    input_height_ = input_height;
    input_format_ = input_format;
    output_width_ = output_width;
    output_height_ = output_height;
    output_fps_ = output_fps;
    use_hardware_ = use_hardware;
    
    // 创建pipeline
    pipeline_ = gst_pipeline_new("converter-pipeline");
    if (!pipeline_) {
        LOG_E("Failed to create converter pipeline");
        return false;
    }
    
    // 创建元素
    appsrc_ = gst_element_factory_make("appsrc", "source");
    videoconvert_ = gst_element_factory_make("videoconvert", "convert");
    videoscale_ = gst_element_factory_make("videoscale", "scale");
    capsfilter_input_ = gst_element_factory_make("capsfilter", "input-caps");
    capsfilter_output_ = gst_element_factory_make("capsfilter", "output-caps");
    appsink_ = gst_element_factory_make("appsink", "sink");
    
    if (!appsrc_ || !videoconvert_ || !videoscale_ || !capsfilter_input_ || 
        !capsfilter_output_ || !appsink_) {
        LOG_E("Failed to create converter elements");
        cleanup();
        return false;
    }
    
    // 选择编码器 - 优先使用mpph264enc，回退到mpph265enc，最后使用软件编码器
    const char* encoder_name = nullptr;
    if (use_hardware_) {
        encoder_name = "mpph264enc";
        encoder_ = gst_element_factory_make(encoder_name, "encoder");
        if (!encoder_) {
            LOG_W("mpph264enc not available, trying mpph265enc");
            encoder_name = "mpph265enc";
            encoder_ = gst_element_factory_make(encoder_name, "encoder");
        }
        if (!encoder_) {
            LOG_W("Hardware encoders not available, falling back to software");
            encoder_name = "x264enc";
            encoder_ = gst_element_factory_make(encoder_name, "encoder");
            use_hardware_ = false;
        }
    } else {
        encoder_name = "x264enc";
        encoder_ = gst_element_factory_make(encoder_name, "encoder");
    }
    
    if (!encoder_) {
        LOG_E("Failed to create encoder");
        cleanup();
        return false;
    }
    
    // 获取正确的GStreamer格式
    std::string gst_format = v4l2_to_gst_format(input_format_);
    LOG_I("Converting V4L2 format 0x%08x to GStreamer format: %s", input_format_, gst_format.c_str());

    // 配置appsrc
    g_object_set(G_OBJECT(appsrc_),
                 "caps", gst_caps_new_simple("video/x-raw",
                                           "format", G_TYPE_STRING, gst_format.c_str(),
                                           "width", G_TYPE_INT, input_width_,
                                           "height", G_TYPE_INT, input_height_,
                                           "framerate", GST_TYPE_FRACTION, output_fps_, 1,
                                           NULL),
                 "stream-type", 0, // GST_APP_STREAM_TYPE_STREAM
                 "format", GST_FORMAT_TIME,
                 "is-live", TRUE,
                 "do-timestamp", TRUE,
                 NULL);
    
    // 配置编码器
    if (use_hardware_) {
        // 使用mpph264enc或mpph265enc硬件编码器
        g_object_set(G_OBJECT(encoder_),
                     "bps", 2000000,        // 2Mbps in bps
                     "bps-min", 1000000,    // 1Mbps minimum
                     "bps-max", 4000000,    // 4Mbps maximum
                     "profile", "baseline", // baseline profile
                     "gop", 15,
                     "rc-mode", 1,          // CBR
                     NULL);
    } else {
        g_object_set(G_OBJECT(encoder_),
                     "bitrate", 2000,
                     "tune", 0x00000004, // zerolatency
                     "speed-preset", 1,  // ultrafast
                     NULL);
    }
    
    // 配置输出caps
    GstCaps* output_caps = gst_caps_new_simple("video/x-raw",
                                              "format", G_TYPE_STRING, "I420",
                                              "width", G_TYPE_INT, output_width_,
                                              "height", G_TYPE_INT, output_height_,
                                              "framerate", GST_TYPE_FRACTION, output_fps_, 1,
                                              NULL);
    g_object_set(G_OBJECT(capsfilter_output_), "caps", output_caps, NULL);
    gst_caps_unref(output_caps);
    
    // 配置appsink
    g_object_set(G_OBJECT(appsink_),
                 "emit-signals", TRUE,
                 "sync", FALSE,
                 "max-buffers", 1,
                 "drop", TRUE,
                 NULL);
    
    // 添加元素到pipeline
    gst_bin_add_many(GST_BIN(pipeline_), appsrc_, videoconvert_, videoscale_,
                     capsfilter_output_, encoder_, appsink_, NULL);
    
    // 链接元素
    if (!gst_element_link_many(appsrc_, videoconvert_, videoscale_,
                              capsfilter_output_, encoder_, appsink_, NULL)) {
        LOG_E("Failed to link converter elements");
        cleanup();
        return false;
    }
    
    // 启动pipeline
    GstStateChangeReturn ret = gst_element_set_state(pipeline_, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        LOG_E("Failed to start converter pipeline");
        cleanup();
        return false;
    }
    
    LOG_I("VideoFormatConverter initialized: %dx%d -> %dx%d@%dfps, HW=%s",
          input_width_, input_height_, output_width_, output_height_, output_fps_,
          use_hardware_ ? "yes" : "no");
    
    return true;
}

bool VideoFormatConverter::convert_frame(const Frame& input_frame, GstBuffer** output_buffer) {
    LOG_D("convert_frame called: frame size=%zu, %dx%d, format=0x%08x",
          input_frame.data.size(), input_frame.width, input_frame.height, input_frame.format);

    if (!pipeline_ || !appsrc_ || !appsink_) {
        LOG_E("convert_frame: pipeline elements not initialized (pipeline=%p, appsrc=%p, appsink=%p)",
              pipeline_, appsrc_, appsink_);
        return false;
    }

    // 检查pipeline状态
    GstState state, pending;
    GstStateChangeReturn ret = gst_element_get_state(pipeline_, &state, &pending, 0);
    LOG_D("Pipeline state: %d, pending: %d, ret: %d", state, pending, ret);

    if (state != GST_STATE_PLAYING) {
        LOG_E("Pipeline is not in PLAYING state: %d", state);
        return false;
    }

    auto start_time = std::chrono::high_resolution_clock::now();

    // 创建输入buffer
    LOG_D("Creating input buffer of size %zu", input_frame.data.size());
    GstBuffer* input_buffer = gst_buffer_new_allocate(NULL, input_frame.data.size(), NULL);
    if (!input_buffer) {
        LOG_E("Failed to allocate input buffer of size %zu", input_frame.data.size());
        return false;
    }

    // 填充数据 - 零拷贝优化
    GstMapInfo map;
    if (!gst_buffer_map(input_buffer, &map, GST_MAP_WRITE)) {
        LOG_E("Failed to map input buffer");
        gst_buffer_unref(input_buffer);
        return false;
    }

    memcpy(map.data, input_frame.data.data(), input_frame.data.size());
    gst_buffer_unmap(input_buffer, &map);
    LOG_D("Input buffer filled with %zu bytes", input_frame.data.size());

    // 设置时间戳
    GST_BUFFER_PTS(input_buffer) = input_frame.timestamp * 1000; // us to ns
    GST_BUFFER_DTS(input_buffer) = GST_BUFFER_PTS(input_buffer);

    // 推送到appsrc
    LOG_D("Pushing buffer to appsrc...");
    GstFlowReturn flow_ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc_), input_buffer);
    if (flow_ret != GST_FLOW_OK) {
        LOG_E("Failed to push buffer to appsrc: %d", flow_ret);
        return false;
    }
    LOG_D("Buffer pushed to appsrc successfully");

    // 从appsink拉取转换后的buffer
    LOG_D("Trying to pull sample from appsink...");
    GstSample* sample = gst_app_sink_try_pull_sample(GST_APP_SINK(appsink_), GST_SECOND);
    if (!sample) {
        LOG_E("Failed to pull converted sample from appsink");
        return false;
    }
    LOG_D("Sample pulled from appsink successfully");

    *output_buffer = gst_buffer_ref(gst_sample_get_buffer(sample));
    gst_sample_unref(sample);

    // 更新统计信息
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);

    frames_converted_.fetch_add(1);
    conversion_time_us_.fetch_add(duration.count());

    LOG_D("convert_frame completed successfully in %ld us", duration.count());
    return true;
}

void VideoFormatConverter::cleanup() {
    if (pipeline_) {
        gst_element_set_state(pipeline_, GST_STATE_NULL);
        gst_object_unref(pipeline_);
        pipeline_ = nullptr;
    }
    
    // 元素会随pipeline一起释放
    appsrc_ = nullptr;
    appsink_ = nullptr;
    videoconvert_ = nullptr;
    videoscale_ = nullptr;
    encoder_ = nullptr;
    capsfilter_input_ = nullptr;
    capsfilter_output_ = nullptr;
}

// RTSPMediaFactory 实现
RTSPMediaFactory::RTSPMediaFactory(const RTSPServerConfig& config) : config_(config), factory_(nullptr) {
}

RTSPMediaFactory::~RTSPMediaFactory() {
    cleanup();
}

bool RTSPMediaFactory::init() {
    // 创建GStreamer媒体工厂
    factory_ = gst_rtsp_media_factory_new();
    if (!factory_) {
        LOG_E("Failed to create GStreamer media factory");
        return false;
    }

    // 设置为非共享的工厂 - 每个客户端独立媒体对象
    gst_rtsp_media_factory_set_shared(factory_, FALSE);
    gst_rtsp_media_factory_set_eos_shutdown(factory_, TRUE);

    LOG_I("Media factory configured: shared=FALSE, eos_shutdown=TRUE");

    // 设置支持UDP和TCP传输协议
    GstRTSPLowerTrans protocols = GstRTSPLowerTrans(
        GST_RTSP_LOWER_TRANS_UDP |
        GST_RTSP_LOWER_TRANS_UDP_MCAST |
        GST_RTSP_LOWER_TRANS_TCP
    );
    gst_rtsp_media_factory_set_protocols(factory_, protocols);

    LOG_I("Supported protocols: UDP, UDP_MCAST, TCP");

    // 初始化DDS读取器
    dds_reader_ = std::make_shared<DDSVideoReader>(config_.dds_topic, config_.buffer_size);
    if (!dds_reader_) {
        LOG_E("Failed to create DDS video reader for topic: %s", config_.dds_topic.c_str());
        return false;
    }

    // 等待第一帧数据来确定视频格式
    if (!wait_for_first_frame()) {
        LOG_E("Failed to receive first frame from DDS topic: %s", config_.dds_topic.c_str());
        return false;
    }

    // 初始化视频格式转换器
    converter_ = std::make_unique<VideoFormatConverter>();

    // 根据接收到的视频格式创建pipeline描述
    std::string pipeline_desc = create_pipeline_description();
    gst_rtsp_media_factory_set_launch(factory_, pipeline_desc.c_str());

    // 设置媒体配置回调
    g_signal_connect(factory_, "media-configure", G_CALLBACK(media_configure_callback), this);

    LOG_I("RTSPMediaFactory initialized for topic: %s", config_.dds_topic.c_str());
    LOG_I("Pipeline: %s", pipeline_desc.c_str());

    return true;
}

bool RTSPMediaFactory::wait_for_first_frame() {
    LOG_I("Waiting for first frame from DDS topic: %s", config_.dds_topic.c_str());

    Frame first_frame;
    int retry_count = 0;
    const int max_retries = 50; // 最多等待5秒 (50 * 100ms)

    while (retry_count < max_retries) {
        if (dds_reader_->read(first_frame, 100)) { // 100ms超时
            // 成功接收到第一帧，更新配置参数
            current_width_.store(first_frame.width);
            current_height_.store(first_frame.height);
            current_format_.store(first_frame.format);

            // 根据接收到的视频格式调整输出参数
            if (config_.output_width == 0 || config_.output_height == 0) {
                // 如果没有指定输出尺寸，使用输入尺寸
                config_.output_width = first_frame.width;
                config_.output_height = first_frame.height;
            }

            LOG_I("First frame received: %dx%d format=%d, output will be: %dx%d@%dfps",
                  first_frame.width, first_frame.height, first_frame.format,
                  config_.output_width, config_.output_height, config_.output_fps);

            return true;
        }

        retry_count++;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    LOG_E("Timeout waiting for first frame from DDS topic: %s", config_.dds_topic.c_str());
    return false;
}

std::string RTSPMediaFactory::create_pipeline_description() {
    std::ostringstream pipeline;

    // 使用从DDS接收到的实际视频参数
    int input_width = current_width_.load();
    int input_height = current_height_.load();
    int input_format = current_format_.load();

    // 检查是否为编码数据
    bool is_encoded = (input_format == V4L2_PIX_FMT_H264 || input_format == V4L2_PIX_FMT_H265);

    if (is_encoded) {
        // 对于编码数据，直接使用编码格式的caps
        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time ";

        if (input_format == V4L2_PIX_FMT_H264) {
            pipeline << "caps=\"video/x-h264,width=" << input_width;
            pipeline << ",height=" << input_height;
            pipeline << ",framerate=" << config_.output_fps << "/1,stream-format=byte-stream,alignment=au\" ";
            pipeline << "! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        } else if (input_format == V4L2_PIX_FMT_H265) {
            pipeline << "caps=\"video/x-h265,width=" << input_width;
            pipeline << ",height=" << input_height;
            pipeline << ",framerate=" << config_.output_fps << "/1,stream-format=byte-stream,alignment=au\" ";
            pipeline << "! h265parse ! rtph265pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        }

        return pipeline.str();
    }

    // 对于原始视频数据，使用video/x-raw格式
    std::string gst_format = v4l2_to_gst_format(input_format);

    // 使用appsrc作为数据源，caps基于实际接收到的视频格式
    pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time ";
    pipeline << "caps=\"video/x-raw,format=" << gst_format << ",width=" << input_width;
    pipeline << ",height=" << input_height;
    pipeline << ",framerate=" << config_.output_fps << "/1\" ";

    // 添加队列缓冲 - 增加缓冲区大小避免阻塞
    pipeline << "! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ";

    // 如果输入和输出尺寸不同，添加缩放
    if (input_width != config_.output_width || input_height != config_.output_height) {
        pipeline << "! videoscale ! video/x-raw,width=" << config_.output_width;
        pipeline << ",height=" << config_.output_height << " ";
    }

    // 编码器选择
    if (config_.use_hardware_encoder) {
        // 优先使用mpph264enc
        if (RTSPServerUtils::check_hardware_encoder_support("H264")) {
            pipeline << "! mpph264enc bps=" << config_.output_bitrate;
            pipeline << " bps-min=" << (config_.output_bitrate / 2);
            pipeline << " bps-max=" << (config_.output_bitrate * 2);
            pipeline << " profile=baseline gop=" << config_.gop_size;
            pipeline << " rc-mode=1";  // CBR
            pipeline << " ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        } else if (RTSPServerUtils::check_hardware_encoder_support("H265")) {
            // 回退到mpph265enc
            pipeline << "! mpph265enc bps=" << config_.output_bitrate;
            pipeline << " bps-min=" << (config_.output_bitrate / 2);
            pipeline << " bps-max=" << (config_.output_bitrate * 2);
            pipeline << " profile=baseline gop=" << config_.gop_size;
            pipeline << " rc-mode=1";  // CBR
            pipeline << " ! h265parse ! rtph265pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        } else {
            // 硬件编码器不可用，使用软件编码器
            pipeline << "! x264enc bitrate=" << (config_.output_bitrate / 1000);
            pipeline << " tune=zerolatency speed-preset=ultrafast";
            pipeline << " key-int-max=" << config_.gop_size;
            pipeline << " threads=2";
            pipeline << " ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        }
    } else {
        // 软件编码器
        pipeline << "! x264enc bitrate=" << (config_.output_bitrate / 1000);
        pipeline << " tune=zerolatency speed-preset=ultrafast";
        pipeline << " key-int-max=" << config_.gop_size;
        pipeline << " threads=4";
        pipeline << " ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
    }
    
    return pipeline.str();
}

void RTSPMediaFactory::cleanup() {
    converter_.reset();
    dds_reader_.reset();

    if (factory_) {
        g_object_unref(factory_);
        factory_ = nullptr;
    }
}

void RTSPMediaFactory::configure_media(GstRTSPMedia* media) {
    LOG_I("=== Media Configure Callback Triggered ===");

    // 获取媒体的pipeline
    GstElement* pipeline = gst_rtsp_media_get_element(media);
    if (!pipeline) {
        LOG_E("Failed to get media pipeline");
        return;
    }

    LOG_I("Got media pipeline: %p", pipeline);

    // 查找appsrc元素
    GstElement* appsrc = gst_bin_get_by_name(GST_BIN(pipeline), "source");
    if (!appsrc) {
        LOG_E("Failed to find appsrc element named 'source'");

        // 尝试调试：列出pipeline中的所有元素
        GstIterator* iter = gst_bin_iterate_elements(GST_BIN(pipeline));
        GValue value = G_VALUE_INIT;
        GstIteratorResult result;

        LOG_I("Pipeline elements:");
        while ((result = gst_iterator_next(iter, &value)) == GST_ITERATOR_OK) {
            GstElement* element = GST_ELEMENT(g_value_get_object(&value));
            gchar* name = gst_element_get_name(element);
            gchar* factory_name = gst_plugin_feature_get_name(GST_PLUGIN_FEATURE(gst_element_get_factory(element)));
            LOG_I("  - Element: %s (factory: %s)", name, factory_name);
            g_free(name);
            g_value_reset(&value);
        }
        g_value_unset(&value);
        gst_iterator_free(iter);

        gst_object_unref(pipeline);
        return;
    }

    LOG_I("Found appsrc element: %p", appsrc);

    // 设置appsrc回调
    g_signal_connect(appsrc, "need-data", G_CALLBACK(need_data_callback), this);
    g_signal_connect(appsrc, "enough-data", G_CALLBACK(enough_data_callback), this);

    LOG_I("Connected appsrc signals");

    // 配置appsrc属性 - 优化为持续数据流
    g_object_set(G_OBJECT(appsrc),
                 "is-live", TRUE,
                 "do-timestamp", FALSE,  // 手动设置时间戳避免时间戳问题
                 "format", GST_FORMAT_TIME,
                 "min-latency", G_GUINT64_CONSTANT(33333333),  // 33.33ms (1/30s)
                 "max-latency", G_GUINT64_CONSTANT(100000000), // 100ms
                 "stream-type", GST_APP_STREAM_TYPE_STREAM,  // 明确指定流类型
                 NULL);

    // 不在configure阶段推送数据，完全依赖need-data回调
    LOG_I("Appsrc configured, waiting for need-data callback to start data flow");
    gst_object_unref(appsrc);
    gst_object_unref(pipeline);

    LOG_I("Media configured successfully");
}

void RTSPMediaFactory::feed_data(GstElement* appsrc) {
    if (!dds_reader_) {
        LOG_E("DDS reader not initialized");
        return;
    }
    // 从DDS读取视频帧
    Frame input_frame;
    if (!dds_reader_->read(input_frame, 100)) {  // 100ms超时
        // 没有数据可读，发送EOS或等待
        LOG_W("No DDS data available within 100ms timeout");
        return;
    }

    // LOG_I("Successfully read DDS frame: %dx%d, format=0x%08x, size=%zu bytes",
        //   input_frame.width, input_frame.height, input_frame.format, input_frame.data.size());

    // 直接创建GstBuffer并推送原始数据
    GstBuffer* buffer = gst_buffer_new_allocate(NULL, input_frame.data.size(), NULL);
    if (!buffer) {
        LOG_E("Failed to allocate GstBuffer of size %zu", input_frame.data.size());
        return;
    }

    // 填充数据
    GstMapInfo map;
    if (!gst_buffer_map(buffer, &map, GST_MAP_WRITE)) {
        LOG_E("Failed to map GstBuffer");
        gst_buffer_unref(buffer);
        return;
    }

    memcpy(map.data, input_frame.data.data(), input_frame.data.size());
    gst_buffer_unmap(buffer, &map);

    // 设置连续时间戳 - 避免时间戳跳跃导致的阻塞
    uint64_t frame_num = frame_count_.fetch_add(1);
    uint64_t timestamp_ns = frame_num * GST_SECOND / config_.output_fps;  // 基于帧率的连续时间戳
    GST_BUFFER_PTS(buffer) = timestamp_ns;
    GST_BUFFER_DTS(buffer) = timestamp_ns;
    GST_BUFFER_DURATION(buffer) = GST_SECOND / config_.output_fps;

    // 推送到appsrc
    GstFlowReturn flow_ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc), buffer);
    if (flow_ret != GST_FLOW_OK) {
        LOG_E("Failed to push buffer to appsrc: %d", flow_ret);
        // buffer已经被appsrc接管，不需要手动释放
        return;
    }
    // 更新统计信息
    frames_served_.fetch_add(1);
}

bool RTSPMediaFactory::update_converter_if_needed(const Frame& frame) {
    bool format_changed = false;

    // 检查视频参数是否改变
    if (current_width_.load() != frame.width ||
        current_height_.load() != frame.height ||
        current_format_.load() != frame.format) {

        current_width_.store(frame.width);
        current_height_.store(frame.height);
        current_format_.store(frame.format);
        format_changed = true;
    }

    // 如果格式改变或转换器未初始化，重新初始化转换器
    if (format_changed || !converter_) {
        if (!converter_) {
            converter_ = std::make_unique<VideoFormatConverter>();
        }

        // 重新初始化转换器
        converter_->cleanup();

        LOG_I("Initializing converter: %dx%d (%d) -> %dx%d@%dfps, HW=%s",
              frame.width, frame.height, frame.format,
              config_.output_width, config_.output_height, config_.output_fps,
              config_.use_hardware_encoder ? "yes" : "no");

        if (!converter_->init(frame.width, frame.height, frame.format,
                             config_.output_width, config_.output_height,
                             config_.output_fps, config_.use_hardware_encoder)) {
            LOG_E("Failed to initialize converter: %dx%d (%d) -> %dx%d@%dfps",
                  frame.width, frame.height, frame.format,
                  config_.output_width, config_.output_height, config_.output_fps);
            return false;
        }

        LOG_I("Converter initialized successfully: %dx%d (%d) -> %dx%d@%dfps",
              frame.width, frame.height, frame.format,
              config_.output_width, config_.output_height, config_.output_fps);
    }

    return true;  // 返回初始化是否成功，而不是format_changed
}

// RTSPServerService 实现
bool RTSPServerService::init(const RTSPServerConfig& config) {
    config_ = config;

    // 设置GStreamer debug级别
    RTSPServerUtils::set_gstreamer_debug_level(config_.gst_debug_level);

    // 初始化GStreamer
    if (!RTSPServerUtils::init_gstreamer()) {
        LOG_E("Failed to initialize GStreamer");
        return false;
    }
    
    // 创建RTSP服务器
    server_ = gst_rtsp_server_new();
    if (!server_) {
        LOG_E("Failed to create RTSP server");
        return false;
    }
    
    // 配置服务器
    gst_rtsp_server_set_address(server_, config_.server_address.c_str());
    gst_rtsp_server_set_service(server_, std::to_string(config_.server_port).c_str());
    
    // 创建挂载点
    mounts_ = gst_rtsp_server_get_mount_points(server_);
    
    // 创建媒体工厂
    factory_ = std::make_unique<RTSPMediaFactory>(config_);
    if (!factory_->init()) {
        LOG_E("Failed to initialize media factory");
        return false;
    }
    
    // 挂载媒体工厂
    gst_rtsp_mount_points_add_factory(mounts_, config_.mount_point.c_str(),
                                     factory_->get_factory());
    
    // 配置传输参数
    RTSPServerUtils::configure_rtsp_transport_params(server_);
    RTSPServerUtils::set_rtsp_buffer_sizes(server_, config_.buffer_size);
    
    // 连接信号
    g_signal_connect(server_, "client-connected", 
                     G_CALLBACK(client_connected_callback), this);
    
    LOG_I("RTSP server initialized: %s:%d%s",
          config_.server_address.c_str(), config_.server_port, config_.mount_point.c_str());

    return true;
}

bool RTSPServerService::start() {
    if (running_.load()) {
        LOG_W("RTSP server already running");
        return true;
    }

    // 启动服务器
    guint server_id = gst_rtsp_server_attach(server_, NULL);
    if (server_id == 0) {
        LOG_E("Failed to attach RTSP server");
        return false;
    }

    stop_requested_.store(false);
    server_thread_ = std::thread(&RTSPServerService::run, this);
    running_.store(true);
    start_time_ = std::chrono::steady_clock::now();

    LOG_I("RTSP server started on %s:%d%s",
          config_.server_address.c_str(), config_.server_port, config_.mount_point.c_str());

    return true;
}

void RTSPServerService::stop() {
    if (!running_.load()) {
        return;
    }

    stop_requested_.store(true);

    if (server_thread_.joinable()) {
        server_thread_.join();
    }

    if (server_) {
        g_object_unref(server_);
        server_ = nullptr;
    }

    if (mounts_) {
        g_object_unref(mounts_);
        mounts_ = nullptr;
    }

    factory_.reset();
    running_.store(false);

    LOG_I("RTSP server stopped");
}

void RTSPServerService::run() {
    GMainLoop* loop = g_main_loop_new(NULL, FALSE);

    LOG_I("RTSP server main loop started");

    while (!stop_requested_.load()) {
        // 运行GLib主循环，处理RTSP连接
        g_main_context_iteration(g_main_loop_get_context(loop), FALSE);

        // 短暂休眠避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    g_main_loop_unref(loop);
    LOG_I("RTSP server main loop stopped");
}

RTSPServerService::ServerStats RTSPServerService::get_stats() const {
    ServerStats stats;
    stats.total_connections = total_connections_.load();
    stats.active_connections = active_connections_.load();
    stats.error_count = error_count_.load();

    if (factory_) {
        stats.frames_served = factory_->get_frames_served();
        stats.clients_connected = factory_->get_clients_connected();
        stats.avg_conversion_time_ms = 0.0; // TODO: 从converter获取
    }

    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_);
    stats.uptime_seconds = duration.count();

    {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(error_mutex_));
        stats.last_error = last_error_;
    }

    return stats;
}

void RTSPServerService::print_stats() const {
    auto stats = get_stats();

    LOG_I("=== RTSP Server Statistics ===");
    LOG_I("Uptime: %.1f seconds", stats.uptime_seconds);
    LOG_I("Total connections: %lu", stats.total_connections);
    LOG_I("Active connections: %lu", stats.active_connections);
    LOG_I("Frames served: %lu", stats.frames_served);
    LOG_I("Clients connected: %lu", stats.clients_connected);
    LOG_I("Error count: %lu", stats.error_count);
    if (!stats.last_error.empty()) {
        LOG_I("Last error: %s", stats.last_error.c_str());
    }
    if (stats.avg_conversion_time_ms > 0) {
        LOG_I("Avg conversion time: %.2f ms", stats.avg_conversion_time_ms);
    }
}

bool RTSPServerService::update_bitrate(int new_bitrate) {
    if (new_bitrate < config_.min_bitrate || new_bitrate > config_.max_bitrate) {
        LOG_W("Bitrate %d out of range [%d, %d]", new_bitrate,
              config_.min_bitrate, config_.max_bitrate);
        return false;
    }

    config_.output_bitrate = new_bitrate;
    LOG_I("Updated bitrate to %d bps", new_bitrate);
    return true;
}

bool RTSPServerService::update_quality(int new_gop_size) {
    if (new_gop_size < 1 || new_gop_size > 300) {
        LOG_W("GOP size %d out of range [1, 300]", new_gop_size);
        return false;
    }

    config_.gop_size = new_gop_size;
    LOG_I("Updated GOP size to %d", new_gop_size);
    return true;
}

void RTSPServerService::handle_client_connected() {
    total_connections_.fetch_add(1);
    active_connections_.fetch_add(1);
    LOG_I("=== CLIENT CONNECTED ===");
    LOG_I("Active connections: %lu, Total connections: %lu",
          active_connections_.load(), total_connections_.load());
}

void RTSPServerService::handle_client_disconnected() {
    active_connections_.fetch_sub(1);
    LOG_I("=== CLIENT DISCONNECTED ===");
    LOG_I("Active connections: %lu", active_connections_.load());
}

// RTSPMediaFactory静态回调函数
void RTSPMediaFactory::media_configure_callback(GstRTSPMediaFactory* factory,
                                               GstRTSPMedia* media,
                                               gpointer user_data) {
    LOG_I("factory: %p, media: %p, user_data: %p", factory, media, user_data);

    RTSPMediaFactory* media_factory = static_cast<RTSPMediaFactory*>(user_data);
    if (!media_factory) {
        LOG_E("Media factory is null in media_configure_callback");
        return;
    }
    media_factory->configure_media(media);
    LOG_I("configure_media call completed");
}

void RTSPMediaFactory::need_data_callback(GstElement* appsrc, guint unused, gpointer user_data) {

    RTSPMediaFactory* media_factory = static_cast<RTSPMediaFactory*>(user_data);
    if (!media_factory) {
        LOG_E("Media factory is null in need_data_callback");
        return;
    }
    media_factory->feed_data(appsrc);
}

void RTSPMediaFactory::enough_data_callback(GstElement* appsrc, gpointer user_data) {
    LOG_I("=== ENOUGH DATA CALLBACK TRIGGERED ===");
    LOG_I("appsrc: %p, user_data: %p", appsrc, user_data);
}

// RTSPServerService静态回调函数
void RTSPServerService::client_connected_callback(GstRTSPServer* server,
                                                 GstRTSPClient* client,
                                                 gpointer user_data) {
    RTSPServerService* service = static_cast<RTSPServerService*>(user_data);
    service->handle_client_connected();
}

void RTSPServerService::client_disconnected_callback(GstRTSPServer* server,
                                                    GstRTSPClient* client,
                                                    gpointer user_data) {
    RTSPServerService* service = static_cast<RTSPServerService*>(user_data);
    service->handle_client_disconnected();
}

// RTSPServerUtils 实现
namespace RTSPServerUtils {

void set_gstreamer_debug_level(int level) {
    // 设置GStreamer debug级别
    // 级别: 0=NONE, 1=ERROR, 2=WARNING, 3=FIXME, 4=INFO, 5=DEBUG, 6=LOG, 7=TRACE, 9=MEMDUMP
    const char* debug_levels[] = {
        "0",  // GST_LEVEL_NONE
        "1",  // GST_LEVEL_ERROR
        "2",  // GST_LEVEL_WARNING
        "3",  // GST_LEVEL_FIXME
        "4",  // GST_LEVEL_INFO
        "5",  // GST_LEVEL_DEBUG
        "6"   // GST_LEVEL_LOG
    };

    // 限制级别范围
    if (level < 0) level = 0;
    if (level > 6) level = 6;

    // 设置环境变量
    std::string debug_str = std::string("*:") + debug_levels[level];

    // 设置全局debug级别
    gst_debug_set_threshold_from_string(debug_str.c_str(), TRUE);

    LOG_I("GStreamer debug level set to: %d (%s)", level, debug_str.c_str());

    // 可以设置特定组件的debug级别
    if (level >= 4) {
        // 对于INFO及以上级别，启用RTSP相关组件的详细日志
        gst_debug_set_threshold_for_name("rtspserver", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("rtspmedia", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("rtspstream", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("appsrc", GST_LEVEL_DEBUG);
    }

    if (level >= 5) {
        // 对于DEBUG级别，启用编码器相关的详细日志
        gst_debug_set_threshold_for_name("mpph264enc", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("mpph265enc", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("x264enc", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("h264parse", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("rtph264pay", GST_LEVEL_DEBUG);
    }
}

bool init_gstreamer() {
    static bool initialized = false;
    if (initialized) {
        return true;
    }

    GError* error = nullptr;
    if (!gst_init_check(nullptr, nullptr, &error)) {
        if (error) {
            LOG_E("Failed to initialize GStreamer: %s", error->message);
            g_error_free(error);
        } else {
            LOG_E("Failed to initialize GStreamer: unknown error");
        }
        return false;
    }

    initialized = true;
    LOG_I("GStreamer initialized successfully");
    return true;
}

std::string create_encoder_pipeline(const RTSPServerConfig& config, bool use_hardware) {
    std::ostringstream pipeline;

    if (use_hardware && check_hardware_encoder_support(config.output_codec)) {
        if (config.output_codec == "H264") {
            pipeline << "mpph264enc bps=" << config.output_bitrate;
            pipeline << " bps-min=" << (config.output_bitrate / 2);
            pipeline << " bps-max=" << (config.output_bitrate * 2);
            pipeline << " profile=baseline rc-mode=1 gop=" << config.gop_size;
        } else if (config.output_codec == "H265") {
            pipeline << "mpph265enc bps=" << config.output_bitrate;
            pipeline << " bps-min=" << (config.output_bitrate / 2);
            pipeline << " bps-max=" << (config.output_bitrate * 2);
            pipeline << " profile=baseline rc-mode=1 gop=" << config.gop_size;
        }
    } else {
        if (config.output_codec == "H264") {
            pipeline << "x264enc bitrate=" << (config.output_bitrate / 1000);
            pipeline << " tune=zerolatency speed-preset=ultrafast";
            pipeline << " key-int-max=" << config.gop_size;
        } else if (config.output_codec == "H265") {
            pipeline << "x265enc bitrate=" << (config.output_bitrate / 1000);
            pipeline << " tune=zerolatency speed-preset=ultrafast";
            pipeline << " key-int-max=" << config.gop_size;
        }
    }

    pipeline << " key-int-max=" << config.gop_size;

    return pipeline.str();
}

void optimize_gst_pipeline_for_realtime(GstElement* pipeline) {
    // 设置pipeline为实时模式
    gst_pipeline_set_latency(GST_PIPELINE(pipeline), 0);

    // 禁用缓冲
    g_object_set(G_OBJECT(pipeline), "async-handling", TRUE, NULL);
}

bool check_hardware_encoder_support(const std::string& codec) {
    GstElementFactory* factory = nullptr;

    if (codec == "H264") {
        // 优先检查mpph264enc，回退到nvh264enc
        factory = gst_element_factory_find("mpph264enc");
        if (!factory) {
            factory = gst_element_factory_find("nvh264enc");
        }
    } else if (codec == "H265") {
        // 优先检查mpph265enc，回退到nvh265enc
        factory = gst_element_factory_find("mpph265enc");
        if (!factory) {
            factory = gst_element_factory_find("nvh265enc");
        }
    }

    if (factory) {
        gst_object_unref(factory);
        return true;
    }

    return false;
}

void configure_rtsp_transport_params(GstRTSPServer* server) {
    // 配置传输参数以优化实时性
    g_object_set(G_OBJECT(server),
                 "timeout", 30,  // 30秒会话超时
                 NULL);
}

void set_rtsp_buffer_sizes(GstRTSPServer* server, int buffer_size) {
    // 设置缓冲区大小
    // 注意：这些参数可能需要根据GStreamer版本调整
    g_object_set(G_OBJECT(server),
                 "backlog", buffer_size,
                 NULL);
}

} // namespace RTSPServerUtils