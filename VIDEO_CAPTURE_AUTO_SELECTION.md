# VideoCaptureService 智能参数选择功能

## 功能概述

为VideoCaptureService添加了智能参数选择功能，当视频来源为V4L2时：

1. **严格匹配模式** - 指定的参数必须被设备支持，否则程序退出
2. **自动选择模式** - 参数设为0时自动寻找设备支持的最合适参数
3. **混合模式** - 可以指定部分参数（严格匹配），其余参数自动选择
4. **参数优先级** - format > fps > size（从高到低）

## 使用方法

### 1. 配置文件方式

#### 完全自动选择
```json
{
  "video_capture": {
    "source_type": "v4l2",
    "device": "/dev/video0",
    "width": 0,        // 0表示自动选择
    "height": 0,       // 0表示自动选择
    "fps": 0,          // 0表示自动选择
    "format": 0        // 0表示自动选择
  }
}
```

#### 部分自动选择
```json
{
  "video_capture": {
    "source_type": "v4l2", 
    "device": "/dev/video0",
    "width": 1280,     // 指定宽度
    "height": 720,     // 指定高度
    "fps": 0,          // 自动选择帧率
    "format": 0        // 自动选择格式
  }
}
```

### 2. 命令行方式

#### 完全自动选择
```bash
./video_capture_main --auto
```

#### 部分自动选择
```bash
# 指定分辨率，自动选择帧率和格式
./video_capture_main --width 1280 --height 720 --fps 0 --format 0

# 只自动选择分辨率
./video_capture_main --width 0 --height 0

# 指定MJPEG格式，其余自动选择
./video_capture_main --format 1196444237 --width 0 --height 0 --fps 0
```

## 参数匹配逻辑

### 1. 参数优先级（严格匹配）
当指定参数时，按以下优先级严格匹配：
1. **Format（最高优先级）** - 必须完全匹配，不支持则退出
2. **FPS（中等优先级）** - 必须完全匹配，不支持则退出
3. **Size（最低优先级）** - 必须完全匹配，不支持则退出

### 2. 匹配算法
系统按以下步骤查找匹配的格式：
1. 枚举设备支持的所有像素格式
2. 如果指定了format，只考虑该格式；否则考虑所有格式
3. 对每个格式，枚举支持的分辨率
4. 如果指定了size，只考虑该分辨率；否则考虑所有分辨率
5. 对每个分辨率，枚举支持的帧率
6. 如果指定了fps，只考虑该帧率；否则考虑所有帧率
7. 找到第一个完全匹配的组合即返回成功

### 3. 严格匹配原则
- **指定参数必须被设备支持** - 不支持则程序退出，不会回退
- **自动选择参数寻找最佳匹配** - 只有设为0的参数才会自动选择
- **混合模式支持** - 可以指定部分参数，其余自动选择

## 常用像素格式参考

| 格式名称 | V4L2常量 | 数值 | 说明 |
|---------|----------|------|------|
| YUYV | V4L2_PIX_FMT_YUYV | 1448695129 | 最常用的YUV格式 |
| MJPEG | V4L2_PIX_FMT_MJPEG | 1196444237 | JPEG压缩格式 |
| H264 | V4L2_PIX_FMT_H264 | 875967048 | H264编码格式 |
| RGB24 | V4L2_PIX_FMT_RGB24 | 859981650 | 24位RGB格式 |
| YUV420 | V4L2_PIX_FMT_YUV420 | 842093913 | YUV420平面格式 |
| NV12 | V4L2_PIX_FMT_NV12 | 842094158 | NV12格式 |

## 日志输出示例

### 完全自动选择模式
```
[INFO] Auto-selecting all parameters
[INFO] Find best format width: 1280, height: 720, fps: 30, format: 1448695129
[INFO] Auto-selected format: 1280x720@30fps, format=1448695129
```

### 严格匹配模式（成功）
```
[INFO] Using specified parameters (format=1448695129, fps=30, size=1920x1080)
[INFO] Found matching format: 1920x1080@30fps, format=1448695129
[INFO] Final format: 1920x1080@30fps, format=1448695129
```

### 严格匹配模式（失败）
```
[INFO] Using specified parameters (format=1448695129, fps=60, size=4096x2160)
[ERROR] Device does not support the specified parameters
[ERROR] Required: format=1448695129, fps=60, size=4096x2160
[ERROR] Failed to init V4L2 device
```

### 混合模式
```
[INFO] Using specified parameters (format=1196444237, fps=0, size=0x0)
[INFO] Found matching format: 1280x720@30fps, format=1196444237
[INFO] Final format: 1280x720@30fps, format=1196444237
```

## 配置文件更新

### 主配置文件 (config/config.json)
- 添加了format字段
- 添加了自动选择说明注释
- 提供了常用格式数值参考

### 示例配置文件 (config/video_capture_auto.json)
- 提供了各种使用场景的配置示例
- 包含常用像素格式对照表
- 命令行使用示例

## 兼容性

### 向后兼容
- 现有配置文件无需修改即可正常工作
- 默认行为保持不变（使用指定参数）

### 新功能
- 设置参数为0启用自动选择
- 支持混合模式（部分自动，部分手动）
- 增强的错误处理和回退机制

## 使用建议

### 开发阶段
1. **设备探测** - 使用`--auto`快速了解设备能力
2. **参数测试** - 逐个指定参数测试设备支持情况
3. **性能测试** - 测试不同格式和分辨率的性能表现

### 生产环境
1. **严格配置** - 指定所有关键参数确保一致性
2. **错误处理** - 程序会在参数不支持时立即退出，便于问题定位
3. **监控日志** - 关注参数匹配日志，确保使用预期配置

### 参数选择策略
1. **Format优先** - 根据后续处理需求选择合适的像素格式
2. **FPS匹配** - 确保帧率满足实时性要求
3. **Size平衡** - 在画质和性能间找到平衡点

### 错误排查
- 程序退出时检查日志中的"Required"信息
- 使用`--auto`模式探测设备实际支持的参数
- 逐步放宽参数约束定位问题参数

这个功能提供了严格的参数控制，确保系统在指定配置下稳定运行，同时保持了自动适配的灵活性。
