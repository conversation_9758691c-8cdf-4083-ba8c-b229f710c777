# VideoCaptureService 自动参数选择功能

## 功能概述

为VideoCaptureService添加了智能参数选择功能，当视频来源为V4L2时：

1. **如果指定了参数** - 使用指定的width、height、format、fps参数
2. **如果未指定参数** - 自动寻找设备支持的最合适参数
3. **混合模式** - 可以指定部分参数，其余参数自动选择

## 使用方法

### 1. 配置文件方式

#### 完全自动选择
```json
{
  "video_capture": {
    "source_type": "v4l2",
    "device": "/dev/video0",
    "width": 0,        // 0表示自动选择
    "height": 0,       // 0表示自动选择
    "fps": 0,          // 0表示自动选择
    "format": 0        // 0表示自动选择
  }
}
```

#### 部分自动选择
```json
{
  "video_capture": {
    "source_type": "v4l2", 
    "device": "/dev/video0",
    "width": 1280,     // 指定宽度
    "height": 720,     // 指定高度
    "fps": 0,          // 自动选择帧率
    "format": 0        // 自动选择格式
  }
}
```

### 2. 命令行方式

#### 完全自动选择
```bash
./video_capture_main --auto
```

#### 部分自动选择
```bash
# 指定分辨率，自动选择帧率和格式
./video_capture_main --width 1280 --height 720 --fps 0 --format 0

# 只自动选择分辨率
./video_capture_main --width 0 --height 0

# 指定MJPEG格式，其余自动选择
./video_capture_main --format 1196444237 --width 0 --height 0 --fps 0
```

## 自动选择逻辑

### 1. 参数验证
- 检查指定的像素格式是否被设备支持
- 验证指定的分辨率是否可用
- 确认指定的帧率是否支持

### 2. 最佳格式查找
当需要自动选择时，系统会：
1. 枚举设备支持的所有格式
2. 优先选择常用格式：YUYV、NV12、RGB24、MJPEG、YUV420、BGR24
3. 查找合适的分辨率（避免过大或过小的分辨率）
4. 选择≥25fps的帧率
5. 返回找到的最佳组合

### 3. 回退机制
- 如果指定的参数不被设备支持，自动回退到自动选择模式
- 确保系统始终能找到可用的配置

## 常用像素格式参考

| 格式名称 | V4L2常量 | 数值 | 说明 |
|---------|----------|------|------|
| YUYV | V4L2_PIX_FMT_YUYV | 1448695129 | 最常用的YUV格式 |
| MJPEG | V4L2_PIX_FMT_MJPEG | 1196444237 | JPEG压缩格式 |
| H264 | V4L2_PIX_FMT_H264 | 875967048 | H264编码格式 |
| RGB24 | V4L2_PIX_FMT_RGB24 | 859981650 | 24位RGB格式 |
| YUV420 | V4L2_PIX_FMT_YUV420 | 842093913 | YUV420平面格式 |
| NV12 | V4L2_PIX_FMT_NV12 | 842094158 | NV12格式 |

## 日志输出示例

### 自动选择模式
```
[INFO] Auto-selecting best format (width=0, height=0, fps=0, format=0)
[INFO] Find best format width: 1280, height: 720, fps: 30, format: 1448695129
[INFO] Auto-selected format: 1280x720@30fps, format=1448695129
```

### 指定参数模式
```
[INFO] Using specified format: 1920x1080@30fps, format=1448695129
[INFO] Final format: 1920x1080, 30/1, 1448695129
```

### 回退模式
```
[WARN] Resolution 4096x2160 not supported for format 1448695129
[WARN] Specified format not supported, falling back to auto-selection
[INFO] Find best format width: 1280, height: 720, fps: 30, format: 1448695129
```

## 配置文件更新

### 主配置文件 (config/config.json)
- 添加了format字段
- 添加了自动选择说明注释
- 提供了常用格式数值参考

### 示例配置文件 (config/video_capture_auto.json)
- 提供了各种使用场景的配置示例
- 包含常用像素格式对照表
- 命令行使用示例

## 兼容性

### 向后兼容
- 现有配置文件无需修改即可正常工作
- 默认行为保持不变（使用指定参数）

### 新功能
- 设置参数为0启用自动选择
- 支持混合模式（部分自动，部分手动）
- 增强的错误处理和回退机制

## 使用建议

1. **开发测试阶段** - 使用`--auto`快速适配不同设备
2. **生产环境** - 指定具体参数确保一致性
3. **未知设备** - 先用自动选择探测能力，再固化配置
4. **性能优化** - 根据应用需求选择合适的格式和分辨率

这个功能大大提高了系统的灵活性和易用性，特别适合需要支持多种不同摄像头设备的场景。
