root@rk3576-buildroot:/# rtsp_server_pipe --gst-debug=4 -p 8556 -u /stream -i "( v4l2src device=/dev/video0 ! video/x-raw,format=YUY2,width=640,height=480,framerate=30/1 ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )"
0:00:00.000435568  1407   0x557bbbfdc0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.001010352  1407   0x557bbbfdc0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001243197  1407   0x557bbbfdc0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001339952  1407   0x557bbbfdc0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001360833  1407   0x557bbbfdc0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001413461  1407   0x557bbbfdc0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007203266  1407   0x557bbbfdc0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.005753 seconds
0:00:00.007264358  1407   0x557bbbfdc0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007822670  1407   0x557bbbfdc0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.007838812  1407   0x557bbbfdc0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.007851067  1407   0x557bbbfdc0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.007860623  1407   0x557bbbfdc0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.007868641  1407   0x557bbbfdc0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
0:00:00.008122504  1407   0x557bbbfdc0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x557bca50c0 for path /stream
stream ready at rtsp://0.0.0.0:8556/stream
0:00:14.647868553  1407   0x557bbbfdc0 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x557bcaf9d0 connected to server ip ************, ipv6 = 0
0:00:14.647895998  1407   0x557bbbfdc0 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x557bcaf9d0 ip ***********:59589
0:00:14.648172996  1407   0x557bbbfdc0 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x557bcaf9d0: attaching to context 0x557bcb01a0
0:00:14.648189573  1407   0x7f98000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x557bcaff80
0:00:14.657638798  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x557bcaf9d0: received a request OPTIONS rtsp://************:8556/stream 1.0
0:00:14.659478173  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x557bcaf9d0: received a request DESCRIBE rtsp://************:8556/stream 1.0
0:00:14.659525248  1407   0x7f98000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x557bca50c0 for path /stream
0:00:14.659548648  1407   0x7f98000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( v4l2src device=/dev/video0 ! video/x-raw,format=YUY2,width=640,height=480,framerate=30/1 ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:14.662571023  1407   0x7f98000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideo4linux2.so" loaded
0:00:14.665785448  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "v4l2src"
0:00:14.665844023  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f90010370> adding pad 'src'
0:00:14.667469348  1407   0x7f98000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:14.667697123  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:14.667770648  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f9001eac0> adding pad 'sink'
0:00:14.667810823  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f9001eac0> adding pad 'src'
0:00:14.668617048  1407   0x7f98000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoconvertscale.so" loaded
0:00:14.669163723  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:14.669220298  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f90027ce0> adding pad 'sink'
0:00:14.669252973  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f90027ce0> adding pad 'src'
0:00:14.675425773  1407   0x7f98000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:14.675860598  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:14.675929948  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f9002d9a0> adding pad 'sink'
0:00:14.675961898  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f9002d9a0> adding pad 'src'
0:00:14.677449898  1407   0x7f98000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:00:14.677688573  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:14.677736773  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f900358a0> adding pad 'sink'
0:00:14.677768048  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f900358a0> adding pad 'src'
0:00:14.677814823  1407   0x7f98000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f900358a0> PTS interpolation: no
0:00:14.677832073  1407   0x7f98000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f900358a0> TS inferring: no
0:00:14.679983123  1407   0x7f98000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:14.680321298  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:14.680389448  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f900452e0> adding pad 'src'
0:00:14.680420848  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f900452e0> adding pad 'sink'
0:00:14.680478548  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:14.680665148  1407   0x7f98000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstV4l2Src named v4l2src0 to some pad of GstQueue named queue0 (0/0) with caps "video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1"
0:00:14.680696598  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:14.680870698  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f90047b70> adding pad 'sink'
0:00:14.680902898  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f90047b70> adding pad 'src'
0:00:14.680927373  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:14.680962298  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:14.680985848  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.681011448  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element v4l2src0:(any) to element capsfilter0:sink
0:00:14.681028623  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:14.681045323  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: v4l2src0 and capsfilter0 in same bin, no need for ghost pads
0:00:14.681087523  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link v4l2src0:src and capsfilter0:sink
0:00:14.681119323  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:14.681186098  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked v4l2src0:src and capsfilter0:sink, successful
0:00:14.681219648  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.681234373  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<v4l2src0:src> Received event on flushing pad. Discarding
0:00:14.681263648  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element queue0:(any)
0:00:14.681281148  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:14.681301873  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and queue0:sink
0:00:14.681382748  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:14.681406998  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and queue0 in same bin, no need for ghost pads
0:00:14.681461523  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and queue0:sink
0:00:14.681507798  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:14.681532673  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and queue0:sink, successful
0:00:14.681546048  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.681559748  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:14.681594373  1407   0x7f98000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue0 to some pad of GstVideoScale named videoscale0 (0/0) with caps "(NULL)"
0:00:14.681616473  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue0:(any) to element videoscale0:(any)
0:00:14.681639548  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue0:src and videoscale0:sink
0:00:14.681689748  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:14.682441348  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue0 and videoscale0 in same bin, no need for ghost pads
0:00:14.682470998  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue0:src and videoscale0:sink
0:00:14.682546348  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:14.683221623  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue0:src and videoscale0:sink, successful
0:00:14.683237798  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.683252148  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue0:src> Received event on flushing pad. Discarding
0:00:14.683303348  1407   0x7f98000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:14.683348273  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:14.683418973  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f9004d940> adding pad 'sink'
0:00:14.683452448  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f9004d940> adding pad 'src'
0:00:14.683496498  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:14.683540123  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to NULL
0:00:14.683562773  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.683587123  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale0:(any) to element capsfilter1:sink
0:00:14.683620248  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:sink
0:00:14.683639498  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale0 and capsfilter1 in same bin, no need for ghost pads
0:00:14.683664398  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale0:src and capsfilter1:sink
0:00:14.683842848  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter1:src> pad has no peer
0:00:14.683877923  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale0:src and capsfilter1:sink, successful
0:00:14.683892273  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.683906023  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale0:src> Received event on flushing pad. Discarding
0:00:14.683931623  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter1:src to element mpph264enc0:(any)
0:00:14.683948598  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:src
0:00:14.683969273  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter1:src and mpph264enc0:sink
0:00:14.684162848  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:14.684239273  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter1 and mpph264enc0 in same bin, no need for ghost pads
0:00:14.684264998  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter1:src and mpph264enc0:sink
0:00:14.684435273  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:14.684503973  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter1:src and mpph264enc0:sink, successful
0:00:14.684519148  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.684533573  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter1:src> Received event on flushing pad. Discarding
0:00:14.684569923  1407   0x7f98000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:00:14.684604948  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:00:14.684642173  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:00:14.684669073  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:14.684715273  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:00:14.684740423  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:00:14.684761673  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:14.684807198  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:00:14.684821723  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.684835473  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:14.684869048  1407   0x7f98000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:14.684890123  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:00:14.684928748  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:00:14.684953448  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:14.684995773  1407   0x7f98000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:00:14.685019248  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:00:14.685039748  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:14.685078848  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:00:14.685092798  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.685106123  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:00:14.685300673  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:14.685329948  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f900452e0
0:00:14.685347123  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:14.685469048  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:14.685510723  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:14.685526398  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.685540773  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:14.685569423  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:14.685679498  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:14.685714098  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:14.685742398  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:14.685785173  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:14.685814323  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:14.685850048  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:14.686043373  1407   0x7f98000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f9004bda0 for url /stream
0:00:14.686137023  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f9004bda0
0:00:14.686211473  1407   0x7f98000d30 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f9004a0b0
0:00:14.687683573  1407   0x7f98000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:14.687714123  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:14.688594298  1407   0x7f98000d30 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f9004b620 joining bin as session 0
0:00:14.688644198  1407   0x7f98000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:14.689455048  1407   0x7f98000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:14.689620648  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f8c00ae70> adding pad 'sink'
0:00:14.689652548  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f8c00ae70> adding pad 'rtcp_sink'
0:00:14.689677273  1407   0x7f98000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:14.689801523  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f8c00c450> adding pad 'src'
0:00:14.689820648  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f8c00c450> adding pad 'sink'
0:00:14.689991623  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:14.690012173  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:14.690028873  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:14.690081298  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:14.690125298  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:14.690145673  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:14.690219998  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:14.690242298  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:14.690256448  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.690295523  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:14.690342073  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:14.690362098  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:14.690375348  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.690398698  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:14.690427023  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:14.690492223  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:14.690506648  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.690524673  1407   0x7f98000d30 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:14.690551048  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:14.690608973  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:14.690665823  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:14.690686298  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:14.690699748  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.690725648  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:14.690775048  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:14.690813348  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:14.690838098  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:14.690854423  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:14.690880373  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:14.690899823  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:14.690912523  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.690959098  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:14.690982923  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:14.690996373  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:14.691019473  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:14.691091248  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f9004bda0
0:00:14.691115873  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f9004bda0
0:00:14.691134248  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f9004bda0
0:00:14.691170648  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691215373  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691233923  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:14.691250998  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691286548  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:14.691313223  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691330498  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:14.691347148  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691369323  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:14.691389998  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691406723  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:14.691423073  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691444898  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:14.691464448  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:14.691481198  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691503073  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:14.691522873  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691573023  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691592398  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:14.691609573  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691632423  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:14.691653348  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691670823  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:14.691692148  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691717223  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:14.691742873  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691761098  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:14.691777423  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691799998  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:14.691821023  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current NULL pending VOID_PENDING, desired next READY
0:00:14.691837798  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to READY
0:00:14.691854048  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691876098  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter1' changed state to 2(READY) successfully
0:00:14.691897098  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691913823  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:14.691930348  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.691952598  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:14.691973848  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current NULL pending VOID_PENDING, desired next READY
0:00:14.691990548  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:14.692006973  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.692028798  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:14.692049798  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:14.692066123  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:14.692086073  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.692108973  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:14.692128498  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current NULL pending VOID_PENDING, desired next READY
0:00:14.873953873  1407   0x7f98000d30 INFO                    v4l2 v4l2_calls.c:592:gst_v4l2_open:<v4l2src0:src> Opened device 'FF Camera: FF Camera' (/dev/video0) successfully
0:00:14.874016873  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to READY
0:00:14.874037423  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.874077023  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 2(READY) successfully
0:00:14.874105598  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:14.874122648  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:14.874146823  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:14.874169923  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:14.874187298  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:14.874207223  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:14.874246648  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.874294923  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.874320123  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:14.874344923  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.874369923  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:14.874392148  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.874416148  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:14.874433073  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.874455298  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:14.874476773  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.874501223  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:14.874518398  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.874540573  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:14.874562473  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:14.874579323  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.874601073  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:14.874620248  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.874666873  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.874697198  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:14.874714648  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.874736898  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:14.874758248  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.875083498  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:14.875105048  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.875130623  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:14.875154073  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.875652898  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:14.875679273  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.875729648  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:14.875761573  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.875790348  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PAUSED
0:00:14.875808348  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.875830598  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter1' changed state to 3(PAUSED) successfully
0:00:14.875852348  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.875877373  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:14.875894548  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.875918873  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:14.875942898  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.875993173  1407   0x7f98000d30 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f9001cdf0 on task 0x7f8c026820
0:00:14.876012248  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue0:src> created task 0x7f8c026820
0:00:14.876119173  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:14.876140048  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.876183948  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:14.876208723  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.876234848  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:14.876252748  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.876275573  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:14.876296373  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current READY pending VOID_PENDING, desired next PAUSED
0:00:14.876342048  1407   0x7f98000d30 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<v4l2src0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:14.876380473  1407   0x7f98000d30 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f9001fbc0 on task 0x7f8c027120
0:00:14.876400098  1407   0x7f98000d30 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<v4l2src0:src> created task 0x7f8c027120
0:00:14.876505073  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to PAUSED
0:00:14.876524848  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.876556573  1407   0x7f98000f70 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "v4l2src0"
0:00:14.876558223  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 3(PAUSED) successfully without preroll
0:00:14.876628698  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:14.876646923  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.876672698  1407   0x7f98000f70 INFO                 v4l2src gstv4l2src.c:722:gst_v4l2src_query_preferred_size:<v4l2src0> Detect input 0 as `Input 1`
0:00:14.876674448  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:14.876717623  1407   0x7f98000d30 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:14.876730448  1407   0x7f98000f70 INFO                    v4l2 gstv4l2object.c:1307:gst_v4l2_object_fill_format_list:<v4l2src0:src> got 2 format(s):
0:00:14.876733448  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:14.876749673  1407   0x7f98000f70 INFO                    v4l2 gstv4l2object.c:1311:gst_v4l2_object_fill_format_list:<v4l2src0:src>   YUYV
0:00:14.876767548  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:14.876789823  1407   0x7f98000f70 INFO                    v4l2 gstv4l2object.c:1311:gst_v4l2_object_fill_format_list:<v4l2src0:src>   MJPG
0:00:14.876809823  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f9004bda0
0:00:14.876861548  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f9004bda0
0:00:14.876941173  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:14.876970373  1407   0x7f98000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:14.877007048  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877039548  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877056898  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:14.877073798  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877098648  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:14.877099648  1407   0x7f98000f70 INFO                    v4l2 gstv4l2object.c:4863:gst_v4l2_object_probe_caps:<v4l2src0:src> probed caps: video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 10/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 10/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)320, height=(int)240, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1, 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1, 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)640, height=(int)480, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)320, height=(int)240, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }
0:00:14.877122698  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877204973  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:14.877222273  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877246973  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:14.877271673  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877372723  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:14.877393973  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877455023  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:14.877478098  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:14.877496373  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877521623  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:14.877567023  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877585673  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:14.877602148  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877624523  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:14.877645498  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877662823  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:00:14.877679173  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877700898  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:14.877727898  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877746223  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:14.877762523  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877785048  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:14.877806148  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877822848  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PLAYING
0:00:14.877839273  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877861223  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:14.877881498  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877898298  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PLAYING
0:00:14.877916973  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.877939173  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:14.877959848  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.877977873  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PLAYING
0:00:14.878017323  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.878041748  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:14.878063798  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:14.878080473  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:14.878097048  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.878119098  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:14.878142748  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to PLAYING
0:00:14.878159973  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.878181823  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 4(PLAYING) successfully
0:00:14.878202698  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:14.878219548  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.878242773  1407   0x7f98000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:14.878262348  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:14.878278898  1407   0x7f98000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:14.878681648  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 2048 (new-clock)
0:00:14.881019123  1407   0x7f98000f70 INFO                 v4l2src gstv4l2src.c:868:gst_v4l2src_negotiate:<v4l2src0> fixated to: video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)1/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:16:1
0:00:14.881061498  1407   0x7f98000f70 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)1/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:16:1
0:00:14.881242848  1407   0x7f98000f70 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:14.881281298  1407   0x7f98000f70 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)1/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:16:1
0:00:14.882633698  1407   0x7f98000d90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4, interlace-mode=(string)progressive, colorimetry=(string)2:4:16:1
0:00:14.883015873  1407   0x7f98000d90 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter1> reuse caps
0:00:14.883053773  1407   0x7f98000d90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4, interlace-mode=(string)progressive, colorimetry=(string)2:4:16:1
0:00:14.883374248  1407   0x7f98000d90 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applying YUY2 1280x720 (2560x720)
0:00:14.885522458  1407   0x7f98000d90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:16:1
0:00:14.886012898  1407   0x7f98000f70 WARN                    v4l2 gstv4l2object.c:4518:gst_v4l2_object_set_crop:<v4l2src0:src> VIDIOC_S_CROP failed
0:00:14.888827623  1407   0x7f98000f70 INFO                    v4l2 gstv4l2object.c:4112:gst_v4l2_object_set_format_full:<v4l2src0:src> Set capture framerate to 30/1
0:00:14.888900248  1407   0x7f98000f70 WARN                    v4l2 gstv4l2object.c:3343:gst_v4l2_object_reset_compose_region:<v4l2src0:src> Failed to get default compose rectangle with VIDIOC_G_SELECTION: Invalid argument
0:00:14.888925723  1407   0x7f98000f70 INFO                    v4l2 gstv4l2object.c:3279:gst_v4l2_object_setup_pool:<v4l2src0:src> accessing buffers via mode 4
0:00:14.889156423  1407   0x7f98000f70 INFO          v4l2bufferpool gstv4l2bufferpool.c:586:gst_v4l2_buffer_pool_set_config:<v4l2src0:pool0:src> increasing minimum buffers to 2
0:00:14.889176948  1407   0x7f98000f70 INFO          v4l2bufferpool gstv4l2bufferpool.c:599:gst_v4l2_buffer_pool_set_config:<v4l2src0:pool0:src> reducing maximum buffers to 64
0:00:14.889253373  1407   0x7f98000f70 INFO          v4l2bufferpool gstv4l2bufferpool.c:599:gst_v4l2_buffer_pool_set_config:<v4l2src0:pool0:src> reducing maximum buffers to 64
0:00:15.142616723  1407   0x7f98000f70 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.142707998  1407   0x7f98000f70 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<v4l2src0> marking pending DISCONT
0:00:15.154499719  1407   0x7f98000d90 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f9002e230 on task 0x7f8400cf50
0:00:15.154526169  1407   0x7f98000d90 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f8400cf50
0:00:15.158024723  1407   0x7f98001150 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.159818523  1407   0x7f98001150 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse0> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:15.159912867  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:15.159940879  1407   0x7f98000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:15.160057223  1407   0x7f98001150 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:16:1, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:15.160378148  1407   0x7f98001150 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2070234816, timestamp-offset=(uint)480531583, seqnum-offset=(uint)16932, a-framerate=(string)30
0:00:15.160439973  1407   0x7f98001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:15.160482523  1407   0x7f98001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:15.160519848  1407   0x7f98001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:15.160697573  1407   0x7f98001150 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f9004b620: new sender source 0x7f7400fe50
0:00:15.160817498  1407   0x7f98001150 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2070234816, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)16932, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:15.160878148  1407   0x7f98001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:15.160938373  1407   0x7f98001150 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f9004b620 received caps 0x7f7400c650, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2070234816, timestamp-offset=(uint)480531583, seqnum-offset=(uint)16932, a-framerate=(string)30
0:00:15.161303623  1407   0x7f98001150 INFO               videometa gstvideometa.c:1100:gst_video_time_code_meta_api_get_type: registering
0:00:15.161443598  1407   0x7f98001150 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:00:15.161560599  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:15.161601848  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f9004bda0 is prerolled
0:00:15.161642873  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:15.161762973  1407   0x7f98000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:15.161868298  1407   0x7f98000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:15.161898348  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://************:8556/stream/
0:00:15.165787823  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x557bcaf9d0: received a request SETUP rtsp://************:8556/stream/stream=0 1.0
0:00:15.165834548  1407   0x7f98000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x557bca50c0 for path /stream/stream=0
0:00:15.165854373  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f9004bda0 for path /stream
0:00:15.165868848  1407   0x7f98000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:15.165884373  1407   0x7f98000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f9004bda0 was not prepared
0:00:15.166038523  1407   0x7f98000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f900595b0
0:00:15.166063923  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f900595b0
0:00:15.166123873  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=60652-60653
0:00:15.166206123  1407   0x7f98000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f9004bda0 in session 0x7f90059950
0:00:15.170625623  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x557bcaf9d0: received a request PLAY rtsp://************:8556/stream/ 1.0
0:00:15.170706073  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:15.170945923  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f9005ad80> adding pad 'src'
0:00:15.171008748  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:00:15.171047473  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:15.171113898  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:00:15.171129123  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.171144623  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:00:15.172231023  1407   0x7f98000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:00:15.172261748  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:15.172752673  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f9005d620> adding pad 'src'
0:00:15.172911298  1407   0x7f98000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:00:15.172981423  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:00:15.173021173  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.173057448  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:15.173122023  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:15.173168348  1407   0x7f98000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.173232998  1407   0x7f98000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f9005da50 on task 0x7f9005e440
0:00:15.173251823  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f9005e440
0:00:15.173428898  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:15.173458948  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:15.173471807  1407   0x7f98001330 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:00:15.173508748  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:15.173529973  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:00:15.173529710  1407   0x7f98001330 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:15.173581602  1407   0x7f98001330 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:15.173560498  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.173604633  1407   0x7f98001330 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:15.173766998  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:00:15.173802708  1407   0x7f98001330 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.173827623  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:00:15.173833042  1407   0x7f98001330 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:00:15.173907348  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:00:15.173932673  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.173971123  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:15.174017423  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f9005f1f0> adding pad 'src'
0:00:15.174145398  1407   0x7f98000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:00:15.174191648  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:00:15.174225623  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:15.174257048  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:15.174322698  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:15.174362573  1407   0x7f98000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:15.174397548  1407   0x7f98000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f9005f5f0 on task 0x7f9005fd60
0:00:15.174414298  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f9005fd60
0:00:15.174634398  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:15.174656348  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:15.174658914  1407   0x7f98001510 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:00:15.174730316  1407   0x7f98001510 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:15.174733398  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:00:15.174753076  1407   0x7f98001510 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:15.174771123  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:00:15.174818073  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.174825462  1407   0x7f98001510 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:15.174920773  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:00:15.174962423  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:00:15.175025073  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:00:15.175040698  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.175103523  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:15.175123523  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:15.175150348  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:00:15.175178573  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:15.175221848  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:15.175247748  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:00:15.175263423  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:00:15.175279873  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.175354623  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:15.175545098  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f90060a30> adding pad 'sink'
0:00:15.175636523  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:15.176093773  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f90062e40> adding pad 'sink'
0:00:15.176282698  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:00:15.176308473  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:00:15.176337798  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:00:15.176362648  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:00:15.176394473  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:00:15.176427823  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.176443373  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:00:15.176512123  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:15.176531973  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:15.176597473  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:00:15.176620798  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:15.176696573  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:15.176715948  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:00:15.176740523  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:00:15.176777573  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:15.176795823  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.176816973  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:00:15.176857623  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:00:15.176963573  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:00:15.176979298  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.177067698  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:15.177143548  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f900656b0> adding pad 'sink'
0:00:15.177215823  1407   0x7f98000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:15.177291448  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f90065e00> adding pad 'sink'
0:00:15.177407448  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:00:15.177439048  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:00:15.177480598  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:00:15.177501548  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:00:15.177544223  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:00:15.177559348  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.177587673  1407   0x7f98000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:00:15.177634048  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:15.177653298  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:15.177675123  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:00:15.177717148  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:15.177735798  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.177759923  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:15.177777823  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:15.177814673  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:00:15.177836723  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:15.177867298  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:15.177888848  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:00:15.177926398  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:15.177986573  1407   0x7f98000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:00:15.178001623  1407   0x7f98000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:15.178036248  1407   0x7f98000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:60652-60653
0:00:15.178392748  1407   0x7f98000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f9004bda0> Handle going back to 0 for none live not seekable streams.
0:00:15.178427073  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:15.178495923  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:15.178551173  1407   0x7f98000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:15.178580548  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:15.178776298  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f9004bda0, target state PAUSED
0:00:15.178833248  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f9004bda0 do_state 1
0:00:15.178862673  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f9004bda0
0:00:15.178882673  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f9004bda0
0:00:15.178900948  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f9004bda0
0:00:15.178916848  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:15.178942448  1407   0x7f98001150 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:15.178949162  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.178971373  1407   0x7f98001150 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:15.179017058  1407   0x7f98001150 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:15.179208073  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:15.179329673  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:15.179342123  1407   0x7f980016f0 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:15.179456623  1407   0x7f980016f0 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:15.179509973  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.179531723  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:00:15.179549623  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.179589723  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:15.179612873  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.179632048  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:15.179650248  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:15.179676673  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.179695798  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:00:15.179714273  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.179741723  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:15.179768873  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:15.179790648  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:00:15.179808048  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.179840098  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:15.179869873  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.179910523  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.179931473  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:15.179950698  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:15.179972598  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.179989023  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:15.180005648  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:15.180026123  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180042048  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:15.180059198  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:15.180078898  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:15.180097023  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:15.180116573  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180156023  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180172923  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:15.180189848  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:15.180210448  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180226298  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:15.180242773  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:15.180263123  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180278998  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:15.180295648  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:15.180316648  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180332248  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:15.180348748  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:15.180369073  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180384798  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:15.180401523  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:15.180421648  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180437148  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:15.180454123  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:15.180474173  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180490173  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:15.180506823  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:15.180525173  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180540773  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<v4l2src0> skipping transition from PLAYING to  PLAYING
0:00:15.180557248  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 4(PLAYING) successfully
0:00:15.180576948  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:15.180607048  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:15.180630398  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:15.180646348  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:15.180662898  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:15.180682048  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:15.180708823  1407   0x7f980016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:15.180729423  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:15.180746598  1407   0x7f980016f0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:15.180874523  1407   0x7f98000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:15.208905843  1407   0x7f98000d90 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter1> reuse caps
0:00:15.209733950  1407   0x7f98000f70 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:15.209783899  1407   0x7f98000f70 INFO                 v4l2src gstv4l2src.c:722:gst_v4l2src_query_preferred_size:<v4l2src0> Detect input 0 as `Input 1`
0:00:15.210360656  1407   0x7f98000f70 INFO                 v4l2src gstv4l2src.c:868:gst_v4l2src_negotiate:<v4l2src0> fixated to: video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)1/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:16:1
0:00:15.212705648  1407   0x7f98001150 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, seqnum-offset=(uint)16932, timestamp-offset=(uint)480531583, ssrc=(uint)2070234816, a-framerate=(string)30
0:00:15.212943223  1407   0x7f98001150 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f9004b620 received caps 0x7f7400ea40, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, seqnum-offset=(uint)16932, timestamp-offset=(uint)480531583, ssrc=(uint)2070234816, a-framerate=(string)30
0:00:15.222211624  1407   0x7f98000f70 INFO              bufferpool gstbufferpool.c:726:gst_buffer_pool_set_config:<v4l2src0:pool0:src> can't change config, we are active
0:00:15.458412198  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.491961723  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.525553173  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.559058348  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.592630398  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.626135198  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.661278648  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.693212123  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.726778723  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.760293823  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.793845473  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.827409348  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.860963798  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.894476148  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.928005594  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:15.961559139  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.028656959  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.129259567  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.165123097  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.229845190  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.296918223  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.363996845  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.431086514  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.498159700  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.598829360  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.668992750  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.732927682  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.800035992  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.867084693  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:16.934154711  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.001218239  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.101848529  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.171984041  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.235984818  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.303058872  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.403656442  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.504277564  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.547900429  1407   0x7f8c000d00 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:17.547986987  1407   0x7f8c000d00 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:17.548149097  1407   0x7f8c000d00 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2070234816, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)16932, clock-rate=(int)90000, octets-sent=(guint64)563914, packets-sent=(guint64)453, octets-received=(guint64)563914, packets-received=(guint64)453, bytes-received=(guint64)582034, bitrate=(guint64)1686200, packets-lost=(int)-2, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400319614108981780, sr-rtptime=(uint)480767163, sr-octet-count=(uint)563914, sr-packet-count=(uint)453;
0:00:17.548155116  1407   0x7f98000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:17.548247610  1407   0x7f98000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:17.604923962  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.674930805  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.772624392  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.873187434  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:17.973822521  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.006839267  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f9004b620: new source 0x7f780139e0
0:00:18.006968311  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2259045954, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:60653, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:18.007014332  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:60653 in 1 transports
0:00:18.007027707  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f9004b620: found transport 0x7f90059a90 for source  0x7f780139e0
0:00:18.007043410  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f9004b620: source 0x7f780139e0 for transport 0x7f90059a90
0:00:18.007067695  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f9004b620: source 0x7f780139e0 in transport 0x7f90059a90 is active
0:00:18.007079354  1407   0x7f98001330 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f900595b0 alive
0:00:18.007131185  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f9004b620: new SDES 0x7f780139e0
0:00:18.074479173  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.178062698  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.275678957  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.376260184  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.476952180  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.611053458  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.680764393  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.812294397  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:18.946449927  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:19.080603022  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:19.184137266  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:19.315374056  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:19.449488485  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:19.583630390  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:19.687299445  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:19.852003736  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:20.019665693  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:20.190029452  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:20.355056077  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:20.522707718  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:20.693400416  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:20.891657304  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:21.092918413  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:21.196126812  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:21.394731992  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:21.596014434  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:21.699521225  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:21.897846813  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9004bda0: got message type 16 (tag)
0:00:21.981566612  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f9004b620: source 0x7f780139e0 in transport 0x7f90059a90 is active
0:00:21.981595483  1407   0x7f98001330 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f900595b0 alive
0:00:21.981617270  1407   0x7f98001330 INFO              rtspstream rtsp-stream.c:2488:on_bye_ssrc: 0x7f9004b620: source 0x7f780139e0 bye
0:00:21.981912849  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x557bcaf9d0: received a request TEARDOWN rtsp://************:8556/stream/ 1.0
0:00:21.981997574  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f9004bda0, target state PLAYING
0:00:21.982020149  1407   0x7f98000b70 INFO              rtspstream rtsp-stream.c:4774:update_transport: removing ***********:60652-60653
0:00:21.982105649  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f9004bda0 do_state 1
0:00:21.982123174  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f9004bda0
0:00:21.982140449  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f9004bda0
0:00:21.982162224  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to NULL for media 0x7f9004bda0
0:00:21.982241474  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.982355599  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:21.982377849  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.982452274  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 3(PAUSED) successfully
0:00:21.982486899  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.982530649  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink0> completed state change to PAUSED
0:00:21.982548474  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.982591849  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 3(PAUSED) successfully
0:00:21.982642874  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.982661299  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:21.982678049  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.982710524  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 3(PAUSED) successfully
0:00:21.982740799  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.982757824  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:21.982774224  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.982795474  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 3(PAUSED) successfully
0:00:21.982826649  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.982860749  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.982877924  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:21.982894474  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.982929074  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:21.982981449  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983011924  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:21.983038099  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983063499  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:21.983157649  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983197974  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:21.983226224  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983253199  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:21.983283224  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:21.983300274  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983323774  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:21.983368274  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983423399  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983442424  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:21.983458674  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983491074  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:21.983511849  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983528824  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:21.983572625  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983595750  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:21.983625550  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983643700  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:21.983659775  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983681775  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:21.983718400  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983735350  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PAUSED
0:00:21.983751275  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983775500  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter1' changed state to 3(PAUSED) successfully
0:00:21.983807350  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983841350  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:21.983887425  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983911700  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:21.983933425  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.983959350  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:21.983975775  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.983997925  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:21.984019225  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.984035675  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:21.984051800  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.984073450  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:21.984112450  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.984131850  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to PAUSED
0:00:21.984147800  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.984169975  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 3(PAUSED) successfully without preroll
0:00:21.984205275  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:21.984222600  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.984254450  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:21.984279875  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PAUSED
0:00:21.984297200  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PAUSED
0:00:21.984321725  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PLAYING to PAUSED (VOID_PENDING pending)
0:00:21.984344250  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 3(PAUSED) successfully
0:00:21.984374700  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 3(PAUSED) successfully
0:00:21.984409500  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 3(PAUSED) successfully
0:00:21.984431175  1407   0x7f98000b70 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:21.984454975  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:21.984471725  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:21.984491900  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change PAUSED to READY, final NULL
0:00:21.984542825  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.984644625  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to READY
0:00:21.984664150  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.984687475  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 2(READY) successfully
0:00:21.984709425  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.984802875  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink0> completed state change to READY
0:00:21.984844000  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.984868625  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 2(READY) successfully
0:00:21.984891450  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.984925950  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to READY
0:00:21.984943625  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.984965625  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 2(READY) successfully
0:00:21.985003400  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.985035625  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to READY
0:00:21.985052975  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.985077800  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 2(READY) successfully
0:00:21.985101250  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.985146725  1407   0x7f98000b70 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f9004b620 received caps (nil), (NULL)
0:00:21.985196200  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.985222000  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:21.985239450  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.985315850  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:21.985340675  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.985387950  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:21.985405600  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.985428325  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:21.985449600  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.985532500  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:21.985551125  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.985575700  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:21.985638875  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:21.985657875  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.985684700  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:21.985741725  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.985792900  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:21.985815075  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.985838500  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:21.985859475  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.986064425  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:21.986084425  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.986110500  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:21.986135200  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.986169300  1407   0x7f98001150 INFO                  mppenc gstmppenc.c:1097:gst_mpp_enc_loop:<mpph264enc0> flushing
0:00:21.986191800  1407   0x7f98001150 INFO                    task gsttask.c:368:gst_task_func:<mpph264enc0:src> Task going to paused
0:00:21.986226900  1407   0x7f98001150 INFO                    task gsttask.c:370:gst_task_func:<mpph264enc0:src> Task resume from paused
0:00:21.986653399  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:21.986673492  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.986696513  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:21.986716001  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.986749440  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to READY
0:00:21.986762366  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.986778749  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter1' changed state to 2(READY) successfully
0:00:21.986794233  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.986940242  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:21.986955812  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.986991240  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:21.987008503  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.987072677  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:21.987087033  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.987103149  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:21.987119530  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next READY
0:00:21.987145999  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:21.987158140  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:21.987173725  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:21.987252175  1407   0x7f98000f70 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<v4l2src0> pausing after gst_base_src_get_range() = flushing
0:00:22.003275907  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to READY
0:00:22.003294902  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:22.003314610  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 2(READY) successfully
0:00:22.003334757  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:22.003347986  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:22.003363600  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:22.003381618  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PAUSED pending VOID_PENDING, desired next READY
0:00:22.003412654  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to READY
0:00:22.003424556  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to READY (VOID_PENDING pending)
0:00:22.003439471  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 2(READY) successfully
0:00:22.003453476  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 2(READY) successfully
0:00:22.003468993  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 2(READY) successfully
0:00:22.003487402  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from PAUSED to READY, pending NULL, next NULL
0:00:22.003499118  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to READY (NULL pending)
0:00:22.003513099  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to NULL, final NULL
0:00:22.003544403  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current READY pending VOID_PENDING, desired next NULL
0:00:22.003562074  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to NULL
0:00:22.003574210  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.003589379  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 1(NULL) successfully
0:00:22.003603835  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current READY pending VOID_PENDING, desired next NULL
0:00:22.003619014  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink0> completed state change to NULL
0:00:22.003630998  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.003645946  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 1(NULL) successfully
0:00:22.003659916  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current READY pending VOID_PENDING, desired next NULL
0:00:22.003674790  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to NULL
0:00:22.003686626  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.003702508  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 1(NULL) successfully
0:00:22.003716155  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current READY pending VOID_PENDING, desired next NULL
0:00:22.003730984  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to NULL
0:00:22.003742552  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.003785817  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 1(NULL) successfully
0:00:22.003803080  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next NULL
0:00:22.003829852  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next NULL
0:00:22.003845052  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:22.003856818  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.003873402  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 1(NULL) successfully
0:00:22.003888454  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next NULL
0:00:22.003903536  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:22.003915529  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.003930182  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 1(NULL) successfully
0:00:22.003944186  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next NULL
0:00:22.003960913  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:22.003972697  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.003987516  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 1(NULL) successfully
0:00:22.004004124  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to NULL
0:00:22.004015956  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004031076  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 1(NULL) successfully
0:00:22.004043799  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004074874  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004091596  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to NULL
0:00:22.004103847  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004119592  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 1(NULL) successfully
0:00:22.004133541  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004148591  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to NULL
0:00:22.004159912  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004174893  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 1(NULL) successfully
0:00:22.004188744  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004204500  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to NULL
0:00:22.004215862  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004231087  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 1(NULL) successfully
0:00:22.004245209  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current READY pending VOID_PENDING, desired next NULL
0:00:22.004260064  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to NULL
0:00:22.004271330  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004286385  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter1' changed state to 1(NULL) successfully
0:00:22.004300386  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004314787  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to NULL
0:00:22.004325891  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004356929  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 1(NULL) successfully
0:00:22.004372426  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004387313  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to NULL
0:00:22.004399202  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004413894  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 1(NULL) successfully
0:00:22.004427947  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004442636  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:22.004454086  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004468771  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 1(NULL) successfully
0:00:22.004481511  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004634321  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to NULL
0:00:22.004649596  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004666226  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 1(NULL) successfully
0:00:22.004683613  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to NULL
0:00:22.004695747  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004711833  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 1(NULL) successfully
0:00:22.004728487  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current READY pending VOID_PENDING, desired next NULL
0:00:22.004743863  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to NULL
0:00:22.004755276  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004770661  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 1(NULL) successfully
0:00:22.004784078  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 1(NULL) successfully
0:00:22.004798112  1407   0x7f98000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 1(NULL) successfully
0:00:22.004888796  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to NULL
0:00:22.004901588  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.004918607  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:4035:finish_unprepare: Removing elements of stream 0 from pipeline
0:00:22.004930767  1407   0x7f98000b70 INFO              rtspstream rtsp-stream.c:4153:gst_rtsp_stream_leave_bin: stream 0x7f9004b620 leaving bin
0:00:22.004944284  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking bin0:src_0(0x7f9004c1b0) and rtpbin0:send_rtp_sink_0(0x7f8c010790)
0:00:22.004962354  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:22.004983756  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtp_src_0'
0:00:22.004997443  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpbin0:send_rtp_src_0(0x7f8c00f450) and tee0:sink(0x7f90060cd0)
0:00:22.005013095  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpbin0:send_rtp_src_0 and tee0:sink
0:00:22.005031057  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:send_rtp_src(0x7f8c00ee30) and send_rtp_src_0:proxypad1(0x7f8c00f7d0)
0:00:22.005046916  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:22.005064309  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtp_sink'
0:00:22.005077345  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking send_rtp_sink_0:proxypad2(0x7f8c010b10) and rtpsession0:send_rtp_sink(0x7f8c00ea50)
0:00:22.005089245  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:22.005100533  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtp_src'
0:00:22.005123432  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtp_sink_0'
0:00:22.005143990  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink0> completed state change to NULL
0:00:22.005163323  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking tee0:src_0(0x7f90063f90) and multiudpsink0:sink(0x7f90063220)
0:00:22.005178151  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked tee0:src_0 and multiudpsink0:sink
0:00:22.005192509  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "multiudpsink0"
0:00:22.005215324  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<multiudpsink0> 0x7f90062e40 dispose
0:00:22.005225479  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<multiudpsink0> removing pad 'sink'
0:00:22.005239636  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<multiudpsink0> 0x7f90062e40 parent class dispose
0:00:22.005279188  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<multiudpsink0> 0x7f90062e40 finalize
0:00:22.005290477  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<multiudpsink0> 0x7f90062e40 finalize parent
0:00:22.005302722  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to NULL
0:00:22.005316812  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "tee0"
0:00:22.005333649  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<tee0> removing pad 'src_0'
0:00:22.005348723  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<tee0> 0x7f90060a30 dispose
0:00:22.005358546  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<tee0> removing pad 'sink'
0:00:22.005374524  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<tee0> 0x7f90060a30 parent class dispose
0:00:22.005385752  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<tee0> 0x7f90060a30 finalize
0:00:22.005395553  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<tee0> 0x7f90060a30 finalize parent
0:00:22.005410542  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:22.005422262  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:22.005436680  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to READY, final NULL
0:00:22.005487303  1407   0x7f98001330 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = flushing
0:00:22.005585204  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from PAUSED to READY, pending NULL, next NULL
0:00:22.005599786  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to READY (NULL pending)
0:00:22.005614074  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change READY to NULL, final NULL
0:00:22.005638349  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to NULL
0:00:22.005651119  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.005673121  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking udpsrc0:src(0x7f9005d9e0) and funnel0:funnelpad0(0x7f9005eef0)
0:00:22.005689167  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked udpsrc0:src and funnel0:funnelpad0
0:00:22.005702429  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "udpsrc0"
0:00:22.005721667  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<udpsrc0> 0x7f9005d620 dispose
0:00:22.005732086  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<udpsrc0> removing pad 'src'
0:00:22.005746577  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<udpsrc0> 0x7f9005d620 parent class dispose
0:00:22.005763427  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<udpsrc0> 0x7f9005d620 finalize
0:00:22.005774218  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<udpsrc0> 0x7f9005d620 finalize parent
0:00:22.005789657  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from PLAYING to PAUSED, pending NULL, next READY
0:00:22.005801295  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PLAYING to PAUSED (NULL pending)
0:00:22.005815737  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to READY, final NULL
0:00:22.005853924  1407   0x7f98001510 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = flushing
0:00:22.005935364  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from PAUSED to READY, pending NULL, next NULL
0:00:22.005949647  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to READY (NULL pending)
0:00:22.005964031  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change READY to NULL, final NULL
0:00:22.005986210  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to NULL
0:00:22.005999346  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to NULL (VOID_PENDING pending)
0:00:22.006020768  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking udpsrc1:src(0x7f9005f580) and funnel0:funnelpad1(0x7f9005ffa0)
0:00:22.006036661  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked udpsrc1:src and funnel0:funnelpad1
0:00:22.006049820  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "udpsrc1"
0:00:22.006066813  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<udpsrc1> 0x7f9005f1f0 dispose
0:00:22.006077050  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<udpsrc1> removing pad 'src'
0:00:22.006090926  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<udpsrc1> 0x7f9005f1f0 parent class dispose
0:00:22.006104682  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<udpsrc1> 0x7f9005f1f0 finalize
0:00:22.006114987  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<udpsrc1> 0x7f9005f1f0 finalize parent
0:00:22.006129170  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to NULL
0:00:22.006145780  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking tee1:src_0(0x7f90066cc0) and multiudpsink1:sink(0x7f900661c0)
0:00:22.006162106  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked tee1:src_0 and multiudpsink1:sink
0:00:22.006176170  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "multiudpsink1"
0:00:22.006192337  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to NULL
0:00:22.006208600  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpbin0:send_rtcp_src_0(0x7f8c012880) and tee1:sink(0x7f90065850)
0:00:22.006223826  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:22.006238323  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "tee1"
0:00:22.006254774  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<tee1> removing pad 'src_0'
0:00:22.006269973  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<tee1> 0x7f900656b0 dispose
0:00:22.006279696  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<tee1> removing pad 'sink'
0:00:22.006295451  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<tee1> 0x7f900656b0 parent class dispose
0:00:22.006306767  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<tee1> 0x7f900656b0 finalize
0:00:22.006316140  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<tee1> 0x7f900656b0 finalize parent
0:00:22.006328322  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to NULL
0:00:22.006344651  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking funnel0:src(0x7f9005b010) and rtpbin0:recv_rtcp_sink_0(0x7f8c013d60)
0:00:22.006360049  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:22.006374423  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "funnel0"
0:00:22.006390007  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<funnel0> removing pad 'funnelpad0'
0:00:22.006404489  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<funnel0> removing pad 'funnelpad1'
0:00:22.006418349  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<funnel0> 0x7f9005ad80 dispose
0:00:22.006428445  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<funnel0> removing pad 'src'
0:00:22.006446406  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<funnel0> 0x7f9005ad80 parent class dispose
0:00:22.006457188  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<funnel0> 0x7f9005ad80 finalize
0:00:22.006466722  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<funnel0> 0x7f9005ad80 finalize parent
0:00:22.006479843  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'recv_rtcp_sink_0'
0:00:22.006493743  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking recv_rtcp_sink_0:proxypad4(0x7f8c0142c0) and rtpsession0:recv_rtcp_sink(0x7f8c013500)
0:00:22.006506361  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:22.006519785  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'recv_rtcp_sink'
0:00:22.006531520  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'sync_src'
0:00:22.006544650  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:sync_src(0x7f8c0139f0) and rtpssrcdemux0:rtcp_sink(0x7f8c00b4c0)
0:00:22.006560231  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:22.006596788  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpbin0> removing pad 'send_rtcp_src_0'
0:00:22.006614655  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking rtpsession0:send_rtcp_src(0x7f8c012510) and send_rtcp_src_0:proxypad3(0x7f8c012c00)
0:00:22.006629978  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:22.006645962  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpsession0> removing pad 'send_rtcp_src'
0:00:22.006665407  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:22.006677548  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:22.006689262  1407   0x7f98000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:22.006702848  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpsession0"
0:00:22.006719032  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpsession0> 0x7f8c0040e0 dispose
0:00:22.006729895  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpsession0> 0x7f8c0040e0 parent class dispose
0:00:22.006753929  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpsession0> 0x7f8c0040e0 finalize
0:00:22.006763894  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpsession0> 0x7f8c0040e0 finalize parent
0:00:22.006777592  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpssrcdemux0"
0:00:22.006793226  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpssrcdemux0> 0x7f8c00ae70 dispose
0:00:22.006803569  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux0> removing pad 'sink'
0:00:22.006817487  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpssrcdemux0> removing pad 'rtcp_sink'
0:00:22.006831868  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpssrcdemux0> 0x7f8c00ae70 parent class dispose
0:00:22.006842823  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpssrcdemux0> 0x7f8c00ae70 finalize
0:00:22.006854567  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpssrcdemux0> 0x7f8c00ae70 finalize parent
0:00:22.006868750  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<rtpbin0> removed child "rtpstorage0"
0:00:22.006888115  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpstorage0> 0x7f8c00c450 dispose
0:00:22.006898665  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage0> removing pad 'src'
0:00:22.006913440  1407   0x7f98000b70 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<rtpstorage0> removing pad 'sink'
0:00:22.006928576  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpstorage0> 0x7f8c00c450 parent class dispose
0:00:22.006939364  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpstorage0> 0x7f8c00c450 finalize
0:00:22.006948697  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpstorage0> 0x7f8c00c450 finalize parent
0:00:22.006994126  1407   0x7f98000b70 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "rtpbin0"
0:00:22.007015365  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<rtpbin0> 0x7f900564b0 dispose
0:00:22.007026032  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<rtpbin0> 0x7f900564b0 parent class dispose
0:00:22.007038670  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<rtpbin0> 0x7f900564b0 finalize
0:00:22.007048875  1407   0x7f98000b70 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<rtpbin0> 0x7f900564b0 finalize parent
0:00:22.007084001  1407   0x7f98000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f90059950
0:00:22.007115344  1407   0x7f98000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f9004bda0 was not prepared
0:00:22.007131762  1407   0x7f98000b70 INFO               rtspmedia rtsp-media.c:4169:gst_rtsp_media_unprepare: media 0x7f9004bda0 was already unprepared
0:00:22.007119500  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<multiudpsink1> 0x7f90065e00 dispose
0:00:22.007156174  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x557bcaf9d0: sending close message
0:00:22.007171075  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<multiudpsink1> removing pad 'sink'
0:00:22.007219950  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<multiudpsink1> 0x7f90065e00 parent class dispose
0:00:22.007248095  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x557bcaf9d0: session 0x7f900595b0 removed
0:00:22.007262933  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x557bcaf9d0: unwatch session 0x7f900595b0
0:00:22.007265575  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<multiudpsink1> 0x7f90065e00 finalize
0:00:22.007288404  1407   0x7f98000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f900595b0
0:00:22.007299000  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<multiudpsink1> 0x7f90065e00 finalize parent
0:00:22.007349975  1407   0x7f98000d30 INFO               rtspmedia rtsp-media.c:530:gst_rtsp_media_finalize: finalize media 0x7f9004bda0
0:00:22.007352456  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x557bcaf9d0: connection closed
0:00:22.007386355  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x557bcaf9d0: watch destroyed
0:00:22.007414225  1407   0x7f98000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x557bcaf9d0
0:00:22.007475675  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<media-pipeline> removed child "bin0"
0:00:22.007480761  1407   0x7f98000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x557bcaff80
0:00:22.007535325  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<media-pipeline> 0x7f9004fe20 dispose
0:00:22.007553225  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<media-pipeline> 0x7f9004fe20 parent class dispose
0:00:22.007568650  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<media-pipeline> 0x7f9004fe20 finalize
0:00:22.007583175  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<media-pipeline> 0x7f9004fe20 finalize parent
0:00:22.007611000  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking videoscale0:src(0x7f90028370) and capsfilter1:sink(0x7f9004dbf0)
0:00:22.007641175  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked videoscale0:src and capsfilter1:sink
0:00:22.007669550  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking capsfilter1:src(0x7f9004de40) and mpph264enc0:sink(0x7f9002de20)
0:00:22.007692125  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked capsfilter1:src and mpph264enc0:sink
0:00:22.007710875  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "capsfilter1"
0:00:22.007734700  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<capsfilter1> 0x7f9004d940 dispose
0:00:22.007750100  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter1> removing pad 'sink'
0:00:22.007771000  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter1> removing pad 'src'
0:00:22.007791875  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<capsfilter1> 0x7f9004d940 parent class dispose
0:00:22.007807125  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<capsfilter1> 0x7f9004d940 finalize
0:00:22.007821375  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<capsfilter1> 0x7f9004d940 finalize parent
0:00:22.007845450  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking v4l2src0:src(0x7f9001fb50) and capsfilter0:sink(0x7f90047e20)
0:00:22.007868350  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked v4l2src0:src and capsfilter0:sink
0:00:22.007895425  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking capsfilter0:src(0x7f900481e0) and queue0:sink(0x7f90016ce0)
0:00:22.007917700  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked capsfilter0:src and queue0:sink
0:00:22.007935875  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "capsfilter0"
0:00:22.007959775  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<capsfilter0> 0x7f90047b70 dispose
0:00:22.007974425  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter0> removing pad 'sink'
0:00:22.007994175  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<capsfilter0> removing pad 'src'
0:00:22.008015025  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<capsfilter0> 0x7f90047b70 parent class dispose
0:00:22.008030225  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<capsfilter0> 0x7f90047b70 finalize
0:00:22.008044125  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<capsfilter0> 0x7f90047b70 finalize parent
0:00:22.008069250  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking pay0:src(0x7f900455c0) and src_0:proxypad0(0x7f9004c530)
0:00:22.008092225  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked pay0:src and src_0:proxypad0
0:00:22.008116675  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking h264parse0:src(0x7f90036a90) and pay0:sink(0x7f90045870)
0:00:22.008139225  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked h264parse0:src and pay0:sink
0:00:22.008159325  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "pay0"
0:00:22.008178725  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<pay0> 0x7f900452e0 dispose
0:00:22.008193275  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'src'
0:00:22.008213900  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<pay0> removing pad 'sink'
0:00:22.008234175  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<pay0> 0x7f900452e0 parent class dispose
0:00:22.008272175  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<pay0> 0x7f900452e0 finalize
0:00:22.008287600  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<pay0> 0x7f900452e0 finalize parent
0:00:22.008311375  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking mpph264enc0:src(0x7f9002e1c0) and h264parse0:sink(0x7f900366f0)
0:00:22.008333975  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked mpph264enc0:src and h264parse0:sink
0:00:22.008355300  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "h264parse0"
0:00:22.008375350  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<h264parse0> 0x7f900358a0 dispose
0:00:22.008390150  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<h264parse0> removing pad 'sink'
0:00:22.008410300  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<h264parse0> removing pad 'src'
0:00:22.008430950  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<h264parse0> 0x7f900358a0 parent class dispose
0:00:22.008466600  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<h264parse0> 0x7f900358a0 finalize
0:00:22.008481950  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<h264parse0> 0x7f900358a0 finalize parent
0:00:22.008501925  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "mpph264enc0"
0:00:22.008522400  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<mpph264enc0> 0x7f9002d9a0 dispose
0:00:22.008536700  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc0> removing pad 'sink'
0:00:22.008556975  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<mpph264enc0> removing pad 'src'
0:00:22.008590700  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<mpph264enc0> 0x7f9002d9a0 parent class dispose
0:00:22.008608775  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<mpph264enc0> 0x7f9002d9a0 finalize
0:00:22.008623425  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<mpph264enc0> 0x7f9002d9a0 finalize parent
0:00:22.008649925  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2164:gst_pad_unlink: unlinking queue0:src(0x7f9001cd80) and videoscale0:sink(0x7f900280c0)
0:00:22.008673925  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstpad.c:2219:gst_pad_unlink: unlinked queue0:src and videoscale0:sink
0:00:22.008695725  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "videoscale0"
0:00:22.008716475  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<videoscale0> 0x7f90027ce0 dispose
0:00:22.008730925  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<videoscale0> removing pad 'sink'
0:00:22.008751275  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<videoscale0> removing pad 'src'
0:00:22.008771775  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<videoscale0> 0x7f90027ce0 parent class dispose
0:00:22.008834025  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<videoscale0> 0x7f90027ce0 finalize
0:00:22.008849625  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<videoscale0> 0x7f90027ce0 finalize parent
0:00:22.008869250  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "queue0"
0:00:22.008888775  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<queue0> 0x7f9001eac0 dispose
0:00:22.008903225  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<queue0> removing pad 'sink'
0:00:22.008927025  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<queue0> removing pad 'src'
0:00:22.008950475  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<queue0> 0x7f9001eac0 parent class dispose
0:00:22.008967975  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<queue0> 0x7f9001eac0 finalize
0:00:22.008982300  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<queue0> 0x7f9001eac0 finalize parent
0:00:22.009005250  1407   0x7f98000d30 INFO           GST_PARENTAGE gstbin.c:1805:gst_bin_remove_func:<bin0> removed child "v4l2src0"
0:00:22.009025025  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<v4l2src0> 0x7f90010370 dispose
0:00:22.009039700  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<v4l2src0> removing pad 'src'
0:00:22.009060875  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<v4l2src0> 0x7f90010370 parent class dispose
0:00:22.009110300  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<v4l2src0> 0x7f90010370 finalize
0:00:22.009126150  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<v4l2src0> 0x7f90010370 finalize parent
0:00:22.009141325  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3392:gst_element_dispose:<bin0> 0x7f90046f30 dispose
0:00:22.009155175  1407   0x7f98000d30 INFO        GST_ELEMENT_PADS gstelement.c:875:gst_element_remove_pad:<bin0> removing pad 'src_0'
0:00:22.009182400  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3438:gst_element_dispose:<bin0> 0x7f90046f30 parent class dispose
0:00:22.009197350  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3470:gst_element_finalize:<bin0> 0x7f90046f30 finalize
0:00:22.009211300  1407   0x7f98000d30 INFO         GST_REFCOUNTING gstelement.c:3475:gst_element_finalize:<bin0> 0x7f90046f30 finalize parent
0:00:22.009273475  1407   0x7f98000d30 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f9004a0b0