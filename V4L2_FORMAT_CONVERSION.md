# V4L2 像素格式转换功能

## 功能概述

为VideoCaptureService添加了V4L2像素格式转换功能，遵循v4l2-ctl工具的格式转换规则，支持：

1. **四字符格式代码** - 如"YUYV"、"MJPG"、"H264"等
2. **整数值** - 如1448695129（YUYV）、1196444237（MJPG）等
3. **自动选择** - 设为0时自动选择最合适的格式
4. **格式帮助** - 使用`--format help`查看所有支持的格式

## 🎯 **支持的格式**

### YUV格式
| 四字符代码 | 整数值 | V4L2常量 | 描述 |
|-----------|--------|----------|------|
| YUYV | 1448695129 | V4L2_PIX_FMT_YUYV | YUV 4:2:2 (YUYV) |
| UYVY | 1498831189 | V4L2_PIX_FMT_UYVY | YUV 4:2:2 (UYVY) |
| YUV4 | 842093913 | V4L2_PIX_FMT_YUV420 | YUV 4:2:0 planar |
| I420 | 842093913 | V4L2_PIX_FMT_YUV420 | YUV 4:2:0 (alias) |
| YU12 | 842093913 | V4L2_PIX_FMT_YUV420 | YUV 4:2:0 (alias) |
| NV12 | 842094158 | V4L2_PIX_FMT_NV12 | YUV 4:2:0 (NV12) |
| NV21 | 825382478 | V4L2_PIX_FMT_NV21 | YUV 4:2:0 (NV21) |
| YV12 | 842094169 | V4L2_PIX_FMT_YVU420 | YVU 4:2:0 planar |

### RGB格式
| 四字符代码 | 整数值 | V4L2常量 | 描述 |
|-----------|--------|----------|------|
| RGB3 | 859981650 | V4L2_PIX_FMT_RGB24 | 24-bit RGB |
| BGR3 | 861030210 | V4L2_PIX_FMT_BGR24 | 24-bit BGR |
| RGB4 | 876758866 | V4L2_PIX_FMT_RGB32 | 32-bit RGB |
| BGR4 | 877807426 | V4L2_PIX_FMT_BGR32 | 32-bit BGR |
| RGBP | 1346520914 | V4L2_PIX_FMT_RGB565 | 16-bit RGB565 |
| BGRP | 1346454594 | V4L2_PIX_FMT_BGR565 | 16-bit BGR565 |

### 压缩格式
| 四字符代码 | 整数值 | V4L2常量 | 描述 |
|-----------|--------|----------|------|
| MJPG | 1196444237 | V4L2_PIX_FMT_MJPEG | Motion JPEG |
| JPEG | 1195724874 | V4L2_PIX_FMT_JPEG | JPEG |
| H264 | 875967048 | V4L2_PIX_FMT_H264 | H.264 |
| H265 | 1211250229 | V4L2_PIX_FMT_H265 | H.265/HEVC |
| HEVC | 1129727304 | V4L2_PIX_FMT_HEVC | HEVC (alias) |
| VP8  | 808996950 | V4L2_PIX_FMT_VP8 | VP8 |
| VP9  | 808997206 | V4L2_PIX_FMT_VP9 | VP9 |

### 其他格式
| 四字符代码 | 整数值 | V4L2常量 | 描述 |
|-----------|--------|----------|------|
| GREY | 1497715271 | V4L2_PIX_FMT_GREY | 8-bit Greyscale |
| Y16  | 540422489 | V4L2_PIX_FMT_Y16 | 16-bit Greyscale |
| BA81 | 825770306 | V4L2_PIX_FMT_SBGGR8 | Bayer BGGR 8-bit |
| GBRG | 1196573255 | V4L2_PIX_FMT_SGBRG8 | Bayer GBRG 8-bit |
| GRBG | 1111967575 | V4L2_PIX_FMT_SGRBG8 | Bayer GRBG 8-bit |
| RGGB | 1111967570 | V4L2_PIX_FMT_SRGGB8 | Bayer RGGB 8-bit |

## 🚀 **使用方法**

### 1. 命令行使用

#### 四字符格式代码
```bash
# 使用YUYV格式
./video_capture_main --format YUYV

# 使用MJPEG格式
./video_capture_main --format MJPG

# 使用H264格式
./video_capture_main --format H264

# 使用RGB24格式
./video_capture_main --format RGB3
```

#### 整数值
```bash
# 使用YUYV格式（整数值）
./video_capture_main --format 1448695129

# 使用MJPEG格式（整数值）
./video_capture_main --format 1196444237

# 使用H264格式（整数值）
./video_capture_main --format 875967048
```

#### 自动选择和帮助
```bash
# 自动选择格式
./video_capture_main --format 0

# 显示所有支持的格式
./video_capture_main --format help

# 显示所有支持的格式（别名）
./video_capture_main --format list
```

### 2. 配置文件使用

#### JSON配置示例
```json
{
  "video_capture": {
    "source_type": "v4l2",
    "device": "/dev/video0",
    "width": 1280,
    "height": 720,
    "fps": 30,
    "format": "YUYV"
  }
}
```

注意：配置文件中目前只支持整数值，四字符代码支持将在后续版本中添加。

## 🔧 **实现细节**

### 格式转换算法
```cpp
// 四字符字符串转换为32位整数（小端序）
uint32_t fourcc_to_pixelformat(const std::string& fourcc) {
    return (static_cast<uint32_t>(fourcc[0]) << 0) |
           (static_cast<uint32_t>(fourcc[1]) << 8) |
           (static_cast<uint32_t>(fourcc[2]) << 16) |
           (static_cast<uint32_t>(fourcc[3]) << 24);
}
```

### 格式映射表
使用V4L2标准常量定义格式映射：
```cpp
std::map<std::string, uint32_t> pixel_format_map = {
    {"YUYV", V4L2_PIX_FMT_YUYV},
    {"MJPG", V4L2_PIX_FMT_MJPEG},
    {"H264", V4L2_PIX_FMT_H264},
    // ... 更多格式
};
```

### 解析优先级
1. **特殊命令** - "help"或"list"显示格式帮助
2. **整数解析** - 尝试解析为整数值
3. **四字符解析** - 查找预定义映射表
4. **直接转换** - 按v4l2-ctl规则直接转换
5. **错误处理** - 解析失败时显示帮助信息

## 📋 **使用示例**

### 严格指定格式
```bash
# 必须使用MJPEG格式，其他参数自动选择
./video_capture_main --format MJPG

# 必须使用H264格式，指定分辨率
./video_capture_main --format H264 --width 1920 --height 1080

# 使用RGB24格式，指定帧率
./video_capture_main --format RGB3 --fps 60
```

### 混合配置
```bash
# MJPEG格式，自动选择分辨率和帧率
./video_capture_main --format MJPG --width 0 --height 0 --fps 0

# 指定分辨率，自动选择格式和帧率
./video_capture_main --width 1280 --height 720 --format 0 --fps 0
```

### 错误处理
```bash
# 无效格式会显示错误和帮助
./video_capture_main --format INVALID
# 输出：
# Invalid format: INVALID
# Use --format help to see all supported formats
```

## 🎉 **优势特点**

1. **兼容v4l2-ctl** - 遵循标准工具的转换规则
2. **多种输入方式** - 支持四字符代码和整数值
3. **内置帮助** - `--format help`显示所有支持格式
4. **严格验证** - 无效格式时提供清晰的错误信息
5. **扩展性强** - 易于添加新的格式支持

这个功能大大提高了格式指定的便利性，用户可以使用熟悉的四字符代码而不需要记住复杂的整数值。
