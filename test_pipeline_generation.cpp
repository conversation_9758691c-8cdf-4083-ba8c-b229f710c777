#include <iostream>
#include <sstream>
#include <linux/videodev2.h>

// 模拟RTSPServerConfig结构
struct RTSPServerConfig {
    int output_fps = 30;
    int output_bitrate = 2000000;  // 2Mbps
    int gop_size = 15;
    bool use_hardware_encoder = true;
};

// 模拟v4l2_to_gst_format函数
std::string v4l2_to_gst_format(int v4l2_format) {
    switch (v4l2_format) {
        case V4L2_PIX_FMT_YUYV: return "YUY2";
        case V4L2_PIX_FMT_UYVY: return "UYVY";
        case V4L2_PIX_FMT_YUV420: return "I420";
        case V4L2_PIX_FMT_NV12: return "NV12";
        case V4L2_PIX_FMT_RGB24: return "RGB";
        case V4L2_PIX_FMT_BGR24: return "BGR24";
        default: return "YUY2";
    }
}

// 模拟硬件编码器检查
bool check_hardware_encoder_support(const std::string& codec) {
    return true;  // 假设硬件编码器可用
}

// 测试pipeline生成函数
std::string create_test_pipeline(int input_format, int width, int height, const RTSPServerConfig& config) {
    std::ostringstream pipeline;
    
    std::cout << "=== Testing format: 0x" << std::hex << input_format << std::dec 
              << " (" << width << "x" << height << "@" << config.output_fps << "fps) ===" << std::endl;

    if (input_format == V4L2_PIX_FMT_H264) {
        // H264已编码数据
        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time "
                 << "caps=\"video/x-h264,width=" << width
                 << ",height=" << height
                 << ",framerate=" << config.output_fps << "/1"
                 << ",stream-format=byte-stream,alignment=au\" "
                 << "! h264parse "
                 << "! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        
    } else if (input_format == V4L2_PIX_FMT_H265) {
        // H265已编码数据
        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time "
                 << "caps=\"video/x-h265,width=" << width
                 << ",height=" << height
                 << ",framerate=" << config.output_fps << "/1"
                 << ",stream-format=byte-stream,alignment=au\" "
                 << "! h265parse "
                 << "! rtph265pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        
    } else if (input_format == V4L2_PIX_FMT_MJPEG) {
        // MJPEG数据
        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time "
                 << "caps=\"image/jpeg,format=MJPG,width=" << width
                 << ",height=" << height
                 << ",framerate=" << config.output_fps << "/1\" "
                 << "! queue max-size-buffers=10 leaky=downstream "
                 << "! mppjpegdec "
                 << "! mpph264enc bps=" << config.output_bitrate << " "
                 << "bps-min=" << (config.output_bitrate / 2) << " "
                 << "bps-max=" << (config.output_bitrate * 2) << " "
                 << "profile=baseline gop=" << config.gop_size << " rc-mode=1 "
                 << "! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        
    } else {
        // 原始视频数据
        std::string gst_format = v4l2_to_gst_format(input_format);
        
        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time "
                 << "caps=\"video/x-raw,format=" << gst_format
                 << ",width=" << width
                 << ",height=" << height
                 << ",framerate=" << config.output_fps << "/1\" "
                 << "! queue max-size-buffers=10 leaky=downstream ";
        
        // 检查是否需要格式转换
        bool need_convert = true;
        if (input_format == V4L2_PIX_FMT_YUV420 || input_format == V4L2_PIX_FMT_NV12) {
            need_convert = false;  // 硬件编码器原生支持
        }
        
        if (need_convert) {
            pipeline << "! videoconvert ";
        }
        
        pipeline << "! mpph264enc bps=" << config.output_bitrate << " "
                 << "bps-min=" << (config.output_bitrate / 2) << " "
                 << "bps-max=" << (config.output_bitrate * 2) << " "
                 << "profile=baseline gop=" << config.gop_size << " rc-mode=1 "
                 << "! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
    }
    
    return pipeline.str();
}

int main() {
    RTSPServerConfig config;
    
    // 测试不同格式
    std::vector<std::pair<int, std::string>> test_formats = {
        {V4L2_PIX_FMT_H264, "H264"},
        {V4L2_PIX_FMT_H265, "H265"},
        {V4L2_PIX_FMT_MJPEG, "MJPEG"},
        {V4L2_PIX_FMT_YUYV, "YUYV"},
        {V4L2_PIX_FMT_YUV420, "I420"},
        {V4L2_PIX_FMT_NV12, "NV12"}
    };
    
    for (const auto& format : test_formats) {
        std::string pipeline = create_test_pipeline(format.first, 1280, 720, config);
        std::cout << "Format: " << format.second << std::endl;
        std::cout << "Pipeline: " << pipeline << std::endl;
        std::cout << std::endl;
    }
    
    return 0;
}
