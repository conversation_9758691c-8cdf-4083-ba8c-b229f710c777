#!/bin/bash

# 简化的RTSP服务器编译脚本
# 专门用于测试convert_frame修复

set -e

echo "=== 编译RTSP服务器修复版本 ==="

# 项目目录
PROJECT_ROOT="$(pwd)"
BUILD_DIR="$PROJECT_ROOT/build"
SRC_DIR="$PROJECT_ROOT/src"
INCLUDE_DIR="$PROJECT_ROOT/include"
DDS_DIR="$PROJECT_ROOT/dds_video_frame"

# 创建构建目录
mkdir -p "$BUILD_DIR"

echo "编译DDS视频帧库..."
cd "$DDS_DIR"
if [ ! -f "libdds_video_frame.a" ]; then
    g++ -c -fPIC -I. *.cxx -std=c++17
    ar rcs libdds_video_frame.a *.o
    echo "DDS库编译完成"
else
    echo "DDS库已存在，跳过编译"
fi

cd "$PROJECT_ROOT"

echo "编译RTSP服务器..."

# 编译命令
g++ -o "$BUILD_DIR/rtsp_server_main" \
    "$SRC_DIR/rtsp_server_main.cpp" \
    "$SRC_DIR/rtsp_server.cpp" \
    -I"$INCLUDE_DIR" \
    -I"$DDS_DIR" \
    -L"$DDS_DIR" \
    -ldds_video_frame \
    -lfastdds \
    -lfastcdr \
    $(pkg-config --cflags --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-rtsp-server-1.0) \
    -lpthread \
    -std=c++17 \
    -O2 \
    -DLOG_LEVEL=0

if [ $? -eq 0 ]; then
    echo "✅ RTSP服务器编译成功！"
    echo "可执行文件: $BUILD_DIR/rtsp_server_main"
    
    # 显示文件信息
    ls -la "$BUILD_DIR/rtsp_server_main"
    
    echo ""
    echo "运行命令:"
    echo "  cd $PROJECT_ROOT"
    echo "  $BUILD_DIR/rtsp_server_main --hw-encoder --gst-debug 4"
else
    echo "❌ 编译失败"
    exit 1
fi
