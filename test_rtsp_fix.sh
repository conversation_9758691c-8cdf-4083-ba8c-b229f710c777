#!/bin/bash

# 测试RTSP服务器修复版本
set -e

echo "=== 测试RTSP服务器修复版本 ==="

PROJECT_ROOT="$(pwd)"
BUILD_DIR="$PROJECT_ROOT/build"

# 创建构建目录
mkdir -p "$BUILD_DIR"

echo "编译RTSP服务器..."

# 编译命令 - 简化版本，直接推送原始帧数据
g++ -o "$BUILD_DIR/rtsp_server_test" \
    "$PROJECT_ROOT/src/rtsp_server_main.cpp" \
    "$PROJECT_ROOT/src/rtsp_server.cpp" \
    -I"$PROJECT_ROOT/include" \
    -I"$PROJECT_ROOT/dds_video_frame" \
    -L"$PROJECT_ROOT/dds_video_frame" \
    -ldds_video_frame \
    -lfastdds \
    -lfastcdr \
    $(pkg-config --cflags --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-rtsp-server-1.0) \
    -lpthread \
    -std=c++17 \
    -O2 \
    -DLOG_LEVEL=0 \
    2>&1

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo "可执行文件: $BUILD_DIR/rtsp_server_test"
    
    echo ""
    echo "关键修复点："
    echo "1. 移除了复杂的VideoFormatConverter"
    echo "2. 直接推送原始YUY2帧数据到RTSP pipeline"
    echo "3. RTSP pipeline已配置好处理YUY2->H264转换"
    echo "4. 简化了数据流：DDS -> 原始帧 -> appsrc -> pipeline"
    
    echo ""
    echo "运行测试命令:"
    echo "  $BUILD_DIR/rtsp_server_test --hw-encoder --gst-debug 2"
    echo ""
    echo "RTSP URL: rtsp://localhost:8554/video"
    
else
    echo "❌ 编译失败"
    exit 1
fi
