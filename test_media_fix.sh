#!/bin/bash

# 测试媒体共享修复
set -e

echo "=== 测试RTSP媒体共享修复 ==="

PROJECT_ROOT="$(pwd)"
BUILD_DIR="$PROJECT_ROOT/build"

# 创建构建目录
mkdir -p "$BUILD_DIR"

echo "编译RTSP服务器（媒体共享修复版本）..."

# 编译命令
g++ -o "$BUILD_DIR/rtsp_server_media_fix" \
    "$PROJECT_ROOT/src/rtsp_server_main.cpp" \
    "$PROJECT_ROOT/src/rtsp_server.cpp" \
    -I"$PROJECT_ROOT/include" \
    -I"$PROJECT_ROOT/dds_video_frame" \
    -L"$PROJECT_ROOT/dds_video_frame" \
    -ldds_video_frame \
    -lfastdds \
    -lfastcdr \
    $(pkg-config --cflags --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-rtsp-server-1.0) \
    -lpthread \
    -std=c++17 \
    -O2 \
    -DLOG_LEVEL=0 \
    2>&1

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo "可执行文件: $BUILD_DIR/rtsp_server_media_fix"
    
    echo ""
    echo "关键修复："
    echo "1. ✅ 禁用媒体共享: gst_rtsp_media_factory_set_shared(factory_, FALSE)"
    echo "2. ✅ 添加TCP传输支持: GST_RTSP_LOWER_TRANS_TCP"
    echo "3. ✅ 支持UDP/TCP/UDP_MCAST多种传输协议"
    echo "4. ✅ 每个客户端独立媒体对象，避免状态冲突"
    
    echo ""
    echo "测试步骤："
    echo "1. 运行服务器: $BUILD_DIR/rtsp_server_media_fix --hw-encoder --gst-debug 2"
    echo "2. 连接客户端1: ffplay rtsp://localhost:8554/stream"
    echo "3. 断开客户端1"
    echo "4. 连接客户端2: ffplay rtsp://localhost:8554/stream"
    echo "5. 验证客户端2能正常接收视频流"
    
    echo ""
    echo "预期结果："
    echo "- VLC/ffplay客户端能正常连接（支持TCP传输）"
    echo "- 不再出现 'unsupported transport' 错误"
    echo "- 客户端能正常接收H264视频流"
    echo "- 支持多客户端并发连接"
    
else
    echo "❌ 编译失败"
    exit 1
fi
