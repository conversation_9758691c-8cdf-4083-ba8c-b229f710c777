root@rk3576-buildroot:/# rtsp_server --hw-encoder --gst-debug 4
[2021-01-01 17:23:06.590] [INFO] === RTSP Server Configuration ===
[2021-01-01 17:23:06.590] [INFO] DDS Topic: Video_Frames
[2021-01-01 17:23:06.590] [INFO] Server: 0.0.0.0:8554/stream
[2021-01-01 17:23:06.590] [INFO] Output: 1280x720@30fps, H264, 2000000 bps
[2021-01-01 17:23:06.590] [INFO] GOP Size: 30
[2021-01-01 17:23:06.590] [INFO] Hardware Encoder: yes
[2021-01-01 17:23:06.590] [INFO] Max Clients: 10
[2021-01-01 17:23:06.590] [INFO] Buffer Size: 5
[2021-01-01 17:23:06.590] [INFO] Zero Copy: yes
[2021-01-01 17:23:06.590] [INFO] GStreamer Debug Level: 4
[2021-01-01 17:23:06.590] [INFO] Adaptive Bitrate: 500000 - 5000000 bps
[2021-01-01 17:23:06.590] [INFO] GStreamer debug level set to: 4 (*:4)
0:00:00.000082653  1674   0x558efae8f0 INFO                GST_INIT gst.c:576:init_pre: Initializing GStreamer Core Library version 1.22.9
0:00:00.000099699  1674   0x558efae8f0 INFO                GST_INIT gst.c:577:init_pre: Using library installed in /lib
0:00:00.000112514  1674   0x558efae8f0 INFO                GST_INIT gst.c:595:init_pre: Linux rk3576-buildroot 6.1.99-rk3576 #1 SMP Mon Jun 30 10:03:13 CST 2025 aarch64
0:00:00.000346433  1674   0x558efae8f0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.000835832  1674   0x558efae8f0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001052711  1674   0x558efae8f0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001150981  1674   0x558efae8f0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001165164  1674   0x558efae8f0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001228751  1674   0x558efae8f0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007356492  1674   0x558efae8f0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.006080 seconds
0:00:00.007422701  1674   0x558efae8f0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.008056765  1674   0x558efae8f0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.008074502  1674   0x558efae8f0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.008088567  1674   0x558efae8f0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.008098095  1674   0x558efae8f0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.008105136  1674   0x558efae8f0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
[2021-01-01 17:23:06.598] [INFO] GStreamer initialized successfully
[2021-01-01 17:23:06.599] [INFO] Media factory configured: shared=FALSE, eos_shutdown=TRUE
[2021-01-01 17:23:06.599] [INFO] Supported protocols: UDP, UDP_MCAST, TCP
Start init DDS reader: Video_Frames
Create share memery qos success
Create participant success
Register type success
Create subscriber success
Create topic success
DDS Reader initialized for topic: Video_Frames[2021-01-01 17:23:06.605] [INFO] Waiting for first frame from DDS topic: Video_Frames
Subscriber matched
[2021-01-01 17:23:06.805] [INFO] First frame received: 640x480 format=1448695129, output will be: 1280x720@30fps
[2021-01-01 17:23:06.805] [INFO] RTSPMediaFactory initialized for topic: Video_Frames
[2021-01-01 17:23:06.805] [INFO] Pipeline: ( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=2 max-size-time=0 max-size-bytes=0 ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=30 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )
0:00:00.214908453  1674   0x558efae8f0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x558f08e280 for path /stream

(<unknown>:1674): GLib-GObject-CRITICAL **: 17:23:06.805: g_object_set_is_valid_property: object class 'GstRTSPServer' has no property named 'timeout'
[2021-01-01 17:23:06.805] [INFO] RTSP server initialized: 0.0.0.0:8554/stream
0:00:00.215031701  1674   0x558efae8f0 DEBUG             rtspserver rtsp-server.c:882:gst_rtsp_server_create_socket:<GstRTSPServer@0x558f08be70> getting address info of 0.0.0.0/8554
0:00:00.215461309  1674   0x558efae8f0 DEBUG             rtspserver rtsp-server.c:967:gst_rtsp_server_create_socket:<GstRTSPServer@0x558f08be70> opened sending server socket
0:00:00.215492974  1674   0x558efae8f0 DEBUG             rtspserver rtsp-server.c:994:gst_rtsp_server_create_socket:<GstRTSPServer@0x558f08be70> listening on server socket 0x558f1dde90 with queue of 5
[2021-01-01 17:23:06.806] [INFO] RTSP server started on 0.0.0.0:8554/stream
[2021-01-01 17:23:06.806] [INFO] RTSP server is running. Access stream at: rtsp://0.0.0.0:8554/stream
[2021-01-01 17:23:06.806] [INFO] Press Ctrl+C to stop the server
[2021-01-01 17:23:06.806] [INFO] RTSP server main loop started
[2021-01-01 17:23:16.806] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:23:16.807] [INFO] Uptime: 10.0 seconds
[2021-01-01 17:23:16.807] [INFO] Total connections: 0
[2021-01-01 17:23:16.807] [INFO] Active connections: 0
[2021-01-01 17:23:16.807] [INFO] Frames served: 0
[2021-01-01 17:23:16.807] [INFO] Clients connected: 0
[2021-01-01 17:23:16.807] [INFO] Error count: 0
0:00:13.829220663  1674   0x7f74000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f74006630 connected to server ip ************, ipv6 = 0
0:00:13.829253133  1674   0x7f74000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f74006630 ip ***********:58566
0:00:13.829271302  1674   0x7f74000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x558f08be70> manage client 0x7f74006630
[2021-01-01 17:23:20.419] [INFO] === CLIENT CONNECTED ===
[2021-01-01 17:23:20.420] [INFO] Active connections: 1, Total connections: 1
0:00:13.829668193  1674   0x7f74000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f74006630: attaching to context 0x7f74007440
0:00:13.829672271  1674   0x7f70000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f74006f10
0:00:13.829991221  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request OPTIONS rtsp://************:8554/stream 1.0
0:00:13.832827471  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request DESCRIBE rtsp://************:8554/stream 1.0
0:00:13.832882671  1674   0x7f70000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x558f08e280 for path /stream
0:00:13.832914771  1674   0x7f70000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=2 max-size-time=0 max-size-bytes=0 ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=30 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:13.833694471  1674   0x7f70000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstapp.so" loaded
0:00:13.834104021  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:13.834162996  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f64007810> adding pad 'src'
0:00:13.834193096  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 0
0:00:13.834237071  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:13.836025021  1674   0x7f70000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:13.836238521  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:13.836316071  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f6400d160> adding pad 'sink'
0:00:13.836362221  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f6400d160> adding pad 'src'
0:00:13.837365371  1674   0x7f70000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoconvertscale.so" loaded
0:00:13.837937496  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:13.837994696  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f640179c0> adding pad 'sink'
0:00:13.838035121  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f640179c0> adding pad 'src'
0:00:13.841347946  1674   0x7f70000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:13.841725571  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:13.841784146  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f6401c400> adding pad 'sink'
0:00:13.841820271  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f6401c400> adding pad 'src'
0:00:13.843982321  1674   0x7f70000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:00:13.844188071  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:13.844241721  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f64024c70> adding pad 'sink'
0:00:13.844276946  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f64024c70> adding pad 'src'
0:00:13.844328296  1674   0x7f70000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f64024c70> PTS interpolation: no
0:00:13.844352646  1674   0x7f70000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f64024c70> TS inferring: no
0:00:13.847087296  1674   0x7f70000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:13.847411496  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:13.847493921  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f64034a90> adding pad 'src'
0:00:13.847557121  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f64034a90> adding pad 'sink'
0:00:13.847625246  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:13.847778896  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue0 (0/0) with caps "(NULL)"
0:00:13.847806096  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue0:(any)
0:00:13.847833121  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue0:sink
0:00:13.847863271  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:13.847895271  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:13.847921196  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue0 in same bin, no need for ghost pads
0:00:13.847964771  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue0:sink
0:00:13.847984971  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:13.848005521  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:13.848031096  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue0:sink, successful
0:00:13.848045721  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.848062596  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:13.848109721  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue0 to some pad of GstVideoScale named videoscale0 (0/0) with caps "(NULL)"
0:00:13.848134646  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue0:(any) to element videoscale0:(any)
0:00:13.848158996  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue0:src and videoscale0:sink
0:00:13.848182546  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:13.848207446  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:13.848895971  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue0 and videoscale0 in same bin, no need for ghost pads
0:00:13.848931146  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue0:src and videoscale0:sink
0:00:13.848958096  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:13.848983271  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:13.849579221  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue0:src and videoscale0:sink, successful
0:00:13.849595996  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.849613946  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue0:src> Received event on flushing pad. Discarding
0:00:13.849666421  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:13.849696921  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:13.849841046  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f6403e3c0> adding pad 'sink'
0:00:13.849880946  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f6403e3c0> adding pad 'src'
0:00:13.849913446  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:13.849962396  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:13.849989846  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.850019746  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale0:(any) to element capsfilter0:sink
0:00:13.850038346  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:13.850057721  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale0 and capsfilter0 in same bin, no need for ghost pads
0:00:13.850084646  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale0:src and capsfilter0:sink
0:00:13.850115946  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:13.850771246  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:13.850844446  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale0:src and capsfilter0:sink, successful
0:00:13.850865346  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.850882396  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale0:src> Received event on flushing pad. Discarding
0:00:13.850919871  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mpph264enc0:(any)
0:00:13.850940821  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:13.850964246  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mpph264enc0:sink
0:00:13.850997996  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:13.851735521  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:13.851820671  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mpph264enc0 in same bin, no need for ghost pads
0:00:13.851855771  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mpph264enc0:sink
0:00:13.851891496  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:13.852624296  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:13.852738221  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mpph264enc0:sink, successful
0:00:13.852753996  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.852770946  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:13.852811821  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:00:13.852836996  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:00:13.852862921  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:00:13.852892371  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:13.852921346  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:00:13.852949146  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:00:13.852974546  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:13.853003921  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:00:13.853021746  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.853036446  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:13.853072096  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:13.853096996  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:00:13.853120921  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:00:13.853150721  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:13.853177871  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:00:13.853207396  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:00:13.853231046  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:13.853255471  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:00:13.853269571  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.853284721  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:00:13.853488246  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:13.853520546  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f64034a90
0:00:13.853541096  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:13.853592446  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f6403ffb0: creating stream with index 0 and payloader <pay0>
0:00:13.853785296  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:13.853810896  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:13.853827246  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.853843071  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:13.853875071  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:13.853941921  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f64041d70
0:00:13.853966871  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f64041d70> set retransmission time 0
0:00:13.853987596  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f64041d70> Enabling rate control
0:00:13.854027696  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f64041d70> set retransmission pt 97
0:00:13.854053246  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:13.854087521  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:13.854115721  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:13.854142596  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:13.854169396  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:13.854228246  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:13.854384671  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f64041d70> set retransmission time 0
[2021-01-01 17:23:20.445] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 17:23:20.445] [INFO] factory: 0x558f08e280, media: 0x7f6403ffb0, user_data: 0x558efc6e20
[2021-01-01 17:23:20.445] [INFO] Calling configure_media...
[2021-01-01 17:23:20.445] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 17:23:20.445] [INFO] Got media pipeline: 0x7f640366e0
0:00:13.854524221  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element source
[2021-01-01 17:23:20.445] [INFO] Found appsrc element: 0x7f64007810
[2021-01-01 17:23:20.445] [INFO] Connected appsrc signals
0:00:13.854611896  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 545460846592
0:00:13.854632696  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:13.854662671  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
[2021-01-01 17:23:20.445] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 17:23:20.445] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:13.855357071  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f6402f290
0:00:13.855411071  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:20.446] [INFO] Initial frame push result: 0
[2021-01-01 17:23:20.446] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 17:23:20.446] [INFO] Configured appsrc properties
[2021-01-01 17:23:20.446] [INFO] Media configured successfully
[2021-01-01 17:23:20.446] [INFO] configure_media call completed
0:00:13.855578146  1674   0x7f70000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f6403ffb0 for url /stream
0:00:13.855731896  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f6403ffb0
0:00:13.855751146  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:13.855764157  1674   0x7f70000d80 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f64039a30
0:00:13.857923046  1674   0x7f70000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:13.857962021  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:13.858819996  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:13.858842371  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:13.858864795  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:13.858895752  1674   0x7f70000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:13.858946005  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:13.858967308  1674   0x7f70000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:13.859000349  1674   0x7f70000d80 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f64041d70 joining bin as session 0
0:00:13.859029808  1674   0x7f70000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:13.859469288  1674   0x7f70000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:13.859579604  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f6800b3b0> adding pad 'sink'
0:00:13.859603704  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f6800b3b0> adding pad 'rtcp_sink'
0:00:13.859621580  1674   0x7f70000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:13.859710269  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f6800c830> adding pad 'src'
0:00:13.859724325  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f6800c830> adding pad 'sink'
0:00:13.859835511  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:13.859852391  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:13.859865748  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:13.859920508  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:13.859957679  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:13.859972164  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:13.860022669  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:13.860038348  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:13.860049005  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.860082601  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:13.860113721  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:13.860130341  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:13.860140514  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.860159716  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:13.860179221  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:13.860221061  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:13.860232144  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.860246336  1674   0x7f70000d80 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:13.860265494  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:13.860428182  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:13.860478357  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:13.860492634  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:13.860503545  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.860532345  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:13.860576565  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:13.860603505  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:13.860623347  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:13.860636348  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:13.860652512  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:13.860665220  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:13.860674582  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.860718038  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:13.860731657  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:13.860741028  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.860759128  1674   0x7f70000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:13.860800736  1674   0x7f70000d80 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f64041d70> successfully joined bin
0:00:13.860820999  1674   0x7f70000d80 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f6403ffb0
0:00:13.860833037  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6403ffb0 set blocked 1
0:00:13.860844796  1674   0x7f70000d80 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f64041d70> blocked: 1
0:00:13.860861153  1674   0x7f70000d80 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f6403ffb0
0:00:13.860874409  1674   0x7f70000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f6403ffb0
0:00:13.860901099  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:13.860934871  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:13.860948734  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:13.860961683  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.860988143  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:13.861003751  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861017071  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:13.861028670  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861045116  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:13.861059832  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861073037  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:13.861084680  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861107228  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:13.861121953  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:13.861134640  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861150551  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:13.861164081  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861200812  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861214831  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:13.861226714  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861243493  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:13.861257743  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861271205  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:13.861282806  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861298396  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:13.861313048  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861327654  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:13.861339388  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861355748  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:13.861370083  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861382978  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:13.861394220  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861409682  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:13.861424368  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861437330  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:13.861449234  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861469493  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:13.861485118  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current NULL pending VOID_PENDING, desired next READY
0:00:13.861498013  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:13.861508900  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861523917  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:13.861536706  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:13.861549341  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:13.861560146  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861575538  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:13.861588747  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:13.861600256  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.861615273  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:13.861629608  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:13.861641732  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:13.861654964  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:13.861673417  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.861700559  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.861718416  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:13.861729894  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.861750052  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:13.861765363  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.861782128  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:13.861793651  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.861808573  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:13.861822172  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.861838397  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:13.861849646  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.861864984  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:13.861880225  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:13.861891850  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.861906958  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:13.861919642  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.861945896  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.861967384  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:13.861979321  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.861994941  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:13.862008910  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.862271373  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:13.862288775  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.862308365  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:13.862325430  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.862716542  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:13.862745077  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.862785810  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:13.862807131  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.862831114  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:13.862843951  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.862861821  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:13.862877890  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.862895790  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:13.862907363  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.862923441  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:13.862939078  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.862976250  1674   0x7f70000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f6400d970 on task 0x7f6805ea00
0:00:13.862989497  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue0:src> created task 0x7f6805ea00
0:00:13.863163747  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:13.863179161  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.863232449  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:13.863249411  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:13.863267377  1674   0x7f70000d80 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:13.863295579  1674   0x7f70000d80 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:13.863322142  1674   0x7f70000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f64007b80 on task 0x7f6805efc0
0:00:13.863333653  1674   0x7f70000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f6805efc0
0:00:13.863451586  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:13.863466080  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.863485229  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:13.863498335  1674   0x7f70000ff0 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:13.863505407  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:13.863529308  1674   0x7f70000ff0 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:13.863534557  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.863571208  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:13.863590645  1674   0x7f70000d80 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:13.863602270  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:13.863615961  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.863634215  1674   0x7f70000d80 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f6403ffb0
0:00:13.863660540  1674   0x7f70000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6403ffb0
0:00:13.863714710  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:13.863735280  1674   0x7f70000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:13.863759264  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.863782890  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.863796251  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:13.863807976  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.863825647  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:13.863840963  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.863854003  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:13.863865706  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.863881857  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:13.863896793  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.863964906  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:13.863978979  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864011962  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:13.864028064  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:13.864040314  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864057915  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:13.864087993  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.864102488  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:13.864114371  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864131293  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:13.864146113  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.864159673  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:00:13.864171482  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864187028  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:13.864201965  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.864215361  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:13.864226949  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864243072  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:13.864257439  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.864270258  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:13.864281738  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864297365  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:13.864312433  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.864324872  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PLAYING
0:00:13.864336235  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864353073  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:13.864367434  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.864380046  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PLAYING
0:00:13.864401142  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864418288  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:13.864437630  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:13.864451325  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864468480  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:13.864474692  1674   0x7f70000ff0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:13.864503529  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:13.864517206  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864536151  1674   0x7f70000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:13.864550456  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:13.864563651  1674   0x7f70000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.864584445  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:13.864621900  1674   0x7f70000ff0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:13.864650184  1674   0x7f70000ff0 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 17:23:20.455] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:23:20.455] [INFO] appsrc: 0x7f64007810, unused: 4096, user_data: 0x558efc6e20
[2021-01-01 17:23:20.455] [INFO] Calling feed_data...
[2021-01-01 17:23:20.455] [INFO] === FEED DATA CALLED ===
0:00:13.864741618  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6403ffb0: went from NULL to READY (pending PAUSED)
[2021-01-01 17:23:20.455] [INFO] appsrc: 0x7f64007810
[2021-01-01 17:23:20.455] [INFO] DDS reader is initialized
[2021-01-01 17:23:20.455] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:23:20.455] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:23:20.455] [INFO] Creating GstBuffer for raw frame data...
0:00:13.864899018  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6403ffb0: went from READY to PAUSED (pending VOID_PENDING)
0:00:13.864927563  1674   0x7f70000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f6403ffb0: got message type 2048 (new-clock)
0:00:13.865059482  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6403ffb0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 17:23:20.455] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:23:20.456] [INFO] Pushing raw buffer directly to appsrc...
0:00:13.865330518  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f60001f70
0:00:13.865351020  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:20.456] [INFO] Raw frame fed to appsrc successfully, total frames served: 2
[2021-01-01 17:23:20.456] [INFO] feed_data call completed
0:00:13.865397715  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:23:20.456] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:23:20.456] [INFO] appsrc: 0x7f64007810, unused: 4096, user_data: 0x558efc6e20
[2021-01-01 17:23:20.456] [INFO] Calling feed_data...
[2021-01-01 17:23:20.456] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:23:20.456] [INFO] appsrc: 0x7f64007810
[2021-01-01 17:23:20.456] [INFO] DDS reader is initialized
[2021-01-01 17:23:20.456] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:23:20.456] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:23:20.456] [INFO] Creating GstBuffer for raw frame data...
0:00:13.865849096  1674   0x7f70000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 17:23:20.456] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:23:20.456] [INFO] Pushing raw buffer directly to appsrc...
0:00:13.866065659  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f6012e800
0:00:13.866086306  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:20.456] [INFO] Raw frame fed to appsrc successfully, total frames served: 3
[2021-01-01 17:23:20.456] [INFO] feed_data call completed
0:00:13.866129393  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:13.866289196  1674   0x7f70000de0 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:13.866333921  1674   0x7f70000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:13.866690271  1674   0x7f70000de0 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applying YUY2 1280x720 (2560x720)
0:00:13.868856021  1674   0x7f70000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
[2021-01-01 17:23:20.460] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:23:20.460] [INFO] appsrc: 0x7f64007810, unused: 4096, user_data: 0x558efc6e20
[2021-01-01 17:23:20.460] [INFO] Calling feed_data...
[2021-01-01 17:23:20.460] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:23:20.460] [INFO] appsrc: 0x7f64007810
[2021-01-01 17:23:20.460] [INFO] DDS reader is initialized
[2021-01-01 17:23:20.460] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:23:20.460] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:23:20.460] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:23:20.461] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:23:20.461] [INFO] Pushing raw buffer directly to appsrc...
0:00:13.870404223  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f601c4c00
0:00:13.870427118  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:20.461] [INFO] Raw frame fed to appsrc successfully, total frames served: 4
[2021-01-01 17:23:20.461] [INFO] feed_data call completed
0:00:13.870471057  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:13.898643845  1674   0x7f70000de0 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f64021130 on task 0x7f5c035b70
0:00:13.898673716  1674   0x7f70000de0 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f5c035b70
[2021-01-01 17:23:20.489] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:23:20.489] [INFO] appsrc: 0x7f64007810, unused: 4096, user_data: 0x558efc6e20
[2021-01-01 17:23:20.489] [INFO] Calling feed_data...
[2021-01-01 17:23:20.489] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:23:20.489] [INFO] appsrc: 0x7f64007810
[2021-01-01 17:23:20.489] [INFO] DDS reader is initialized
[2021-01-01 17:23:20.489] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:23:20.489] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:23:20.489] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:23:20.490] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:23:20.490] [INFO] Pushing raw buffer directly to appsrc...
0:00:13.899943961  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f6025afb0
0:00:13.899967455  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:20.490] [INFO] Raw frame fed to appsrc successfully, total frames served: 5
[2021-01-01 17:23:20.490] [INFO] feed_data call completed
0:00:13.900293426  1674   0x7f70000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:13.902485404  1674   0x7f70001200 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:13.903059383  1674   0x7f70001200 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse0> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:13.903149162  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:13.903177782  1674   0x7f70000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:13.903224300  1674   0x7f70001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:13.903457723  1674   0x7f70001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2656434352, timestamp-offset=(uint)235388104, seqnum-offset=(uint)14874, a-framerate=(string)30
0:00:13.903500196  1674   0x7f70001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:13.903531528  1674   0x7f70001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:13.903558865  1674   0x7f70001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:13.903660806  1674   0x7f70001200 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f64041d70: new sender source 0x7f50010dc0
0:00:13.903723880  1674   0x7f70001200 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:13.903766589  1674   0x7f70001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:13.903802973  1674   0x7f70001200 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f64041d70 received caps 0x7f5000c9a0, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2656434352, timestamp-offset=(uint)235388104, seqnum-offset=(uint)14874, a-framerate=(string)30
0:00:13.904047381  1674   0x7f70001200 INFO               videometa gstvideometa.c:1100:gst_video_time_code_meta_api_get_type: registering
0:00:13.904154654  1674   0x7f70001200 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:00:13.904208659  1674   0x7f70001200 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin0:send_rtp_src_0> Now blocking
0:00:13.904227190  1674   0x7f70001200 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f64041d70> position: 447089:23:20.257199000
0:00:13.904286147  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f6403ffb0> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:13.904300268  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:13.904310609  1674   0x7f70000d80 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:13.904320198  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:13.904344604  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:13.904361310  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f6403ffb0 is prerolled
0:00:13.904387548  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:13.904435874  1674   0x7f70000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:13.904501298  1674   0x7f70000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:13.904514393  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f6403ffb0 no suspend
0:00:13.904524835  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:13.904542623  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://************:8554/stream/
0:00:13.924256652  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request SETUP rtsp://************:8554/stream/stream=0 1.0
0:00:13.924297361  1674   0x7f70000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x558f08e280 for path /stream/stream=0
0:00:13.924313024  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f6403ffb0 for path /stream
0:00:13.924324637  1674   0x7f70000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:13.924336115  1674   0x7f70000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f6403ffb0 was not prepared
0:00:13.924422821  1674   0x7f70000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f64042c00
0:00:13.924440370  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f64042c00
0:00:13.924471811  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=51552-51553
0:00:13.924484744  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:13.924493249  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:13.924521440  1674   0x7f70000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f6403ffb0 in session 0x7f64042320
0:00:13.924535358  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:1866:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f64041d70> GST_RTSP_LOWER_TRANS_UDP, ipv4
0:00:13.924670138  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f64041d70> allocated address: 0.0.0.0 and ports: 58532, 58533
0:00:13.924685711  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:1880:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f64041d70> GST_RTSP_LOWER_TRANS_UDP, ipv6
0:00:13.924749330  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f64041d70> allocated address: :: and ports: 48560, 48561
0:00:13.927299473  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request PLAY rtsp://************:8554/stream/ 1.0
0:00:13.927334867  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f6403ffb0> complete pipeline
0:00:13.927348148  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f64041d70> complete stream
0:00:13.927358696  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f64041d70> create receiver part
0:00:13.927382042  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f64041d70> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:13.927397316  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:13.927479661  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f6404d400> adding pad 'src'
0:00:13.927516242  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:00:13.927540463  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:13.927580288  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:00:13.927591545  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.927603112  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:00:13.927620086  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3778:create_receiver_part:<GstRTSPStream@0x7f64041d70> udp IPv4, create and configure udpsources
0:00:13.928389877  1674   0x7f70000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:00:13.928418174  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:13.928619564  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f64050150> adding pad 'src'
0:00:13.928694821  1674   0x7f70000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:00:13.928722375  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:00:13.928736819  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.928761180  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:13.928793175  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:13.928824536  1674   0x7f70000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:13.928849497  1674   0x7f70000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f64050550 on task 0x7f64050dc0
0:00:13.928861953  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f64050dc0
0:00:13.929084270  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:13.929098918  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:13.929119220  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:13.929133756  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:00:13.929146530  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.929189596  1674   0x7f70001410 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:00:13.929222609  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:00:13.929246461  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:00:13.929286971  1674   0x7f70001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:13.929313288  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:00:13.929328740  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.929319221  1674   0x7f70001410 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:13.929353635  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3802:create_receiver_part:<GstRTSPStream@0x7f64041d70> udp IPv6, create and configure udpsources
0:00:13.929370384  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:13.929398112  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f64051c40> adding pad 'src'
0:00:13.929464274  1674   0x7f70000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:00:13.929492875  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:00:13.929507053  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:13.929528763  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:13.929556316  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:13.929588696  1674   0x7f70000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:13.929597496  1674   0x7f70001410 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:13.929618088  1674   0x7f70000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f64052040 on task 0x7f64052490
0:00:13.929659471  1674   0x7f70001410 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:00:13.929662105  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f64052490
0:00:13.929887925  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:13.929902246  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:13.929923080  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:00:13.929938044  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:00:13.929950423  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.929986171  1674   0x7f70001620 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:00:13.929991454  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:00:13.930034152  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:00:13.930073721  1674   0x7f70001620 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:13.930096880  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:00:13.930111825  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.930104371  1674   0x7f70001620 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:13.930141236  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:13.930158114  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:13.930182040  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:00:13.930202903  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:13.930216949  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:13.930236951  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:00:13.930248785  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:00:13.930260515  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.930280886  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f64041d70> create sender part
0:00:13.930295915  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f64041d70> tcp: 0, udp: 1, mcast: 0 (ttl: 0)
0:00:13.930318141  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:13.930414168  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f64053300> adding pad 'sink'
0:00:13.930453086  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:13.930627225  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f64055a40> adding pad 'sink'
0:00:13.930666314  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f64041d70> udp IPv4, configure udpsinks
0:00:13.930680722  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f64041d70> udp IPv6, configure udpsinks
0:00:13.930695730  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f64041d70> plug udp sink
0:00:13.930717021  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f64041d70> creating first stream
0:00:13.930767336  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:00:13.930783193  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:00:13.930802505  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:00:13.930818613  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:00:13.930840136  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:00:13.930850959  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.930861478  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:00:13.930901941  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:13.930916387  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:13.930937715  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:00:13.930954119  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:13.930984082  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:13.930991023  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6403ffb0: went from PAUSED to PAUSED (pending PAUSED)
0:00:13.931016586  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:00:13.931036524  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:00:13.931053262  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:13.931065257  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.931080701  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:00:13.931098396  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:00:13.931158891  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:00:13.931169832  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.931219466  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:13.931261869  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f64058250> adding pad 'sink'
0:00:13.931297563  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:13.931322234  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f64058a40> adding pad 'sink'
0:00:13.931360232  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f64041d70> udp IPv4, configure udpsinks
0:00:13.931374128  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f64041d70> udp IPv6, configure udpsinks
0:00:13.931387682  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f64041d70> plug udp sink
0:00:13.931406374  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f64041d70> creating first stream
0:00:13.931429739  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:00:13.931443138  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:00:13.931460229  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:00:13.931475321  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:00:13.931495626  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:00:13.931505507  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.931517053  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:00:13.931547896  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:13.931561317  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:13.931577499  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:00:13.931596336  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:13.931608927  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.931627088  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:13.931638870  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:13.931653106  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:00:13.931668098  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:13.931679685  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:13.931694064  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:00:13.931710740  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:13.931740092  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:00:13.931750756  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:13.931770975  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f64041d70> pipeline successfully updated
0:00:13.931784671  1674   0x7f70000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:51552-51553
0:00:13.931870180  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:13.931883218  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:13.931912496  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f64041d70> Enabling rate control
0:00:13.931942530  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:13.931978379  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:13.932021246  1674   0x7f70000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:13.932101375  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f6403ffb0> seekable:0
0:00:13.932116131  1674   0x7f70000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f6403ffb0> Handle going back to 0 for none live not seekable streams.
0:00:13.932128396  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:13.932140045  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:13.932149771  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:13.932176677  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:13.932257119  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f6403ffb0, target state PAUSED
0:00:13.932271261  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:13.932283248  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6403ffb0 set blocked 0
0:00:13.932294951  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f64041d70> blocked: 0
0:00:13.932315649  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f6403ffb0 do_state 1
0:00:13.932328750  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f6403ffb0
0:00:13.932338262  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f6403ffb0
0:00:13.932352412  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6403ffb0 set blocked 0
0:00:13.932368000  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f64041d70> blocked: 0
0:00:13.932379418  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6403ffb0
0:00:13.932392262  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:13.932415831  1674   0x7f70001200 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:13.932437520  1674   0x7f70001200 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:13.932426360  1674   0x7f70000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f6403ffb0: got message type 16 (tag)
0:00:13.932452538  1674   0x7f70001200 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:13.932500119  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6403ffb0: went from PAUSED to PAUSED (pending PLAYING)
0:00:13.932526682  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f6403ffb0> got async-done
0:00:13.932803598  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:13.932810996  1674   0x7f70001830 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:13.933069021  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:13.933201496  1674   0x7f70001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:13.933291196  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.933327721  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:00:13.933347196  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.933413221  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:13.933437421  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.933454246  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:13.933471071  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:13.933521596  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.933540871  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:00:13.933561671  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.933600671  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:13.933633496  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:13.933664496  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:00:13.933682071  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.933713871  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:13.933738396  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.933801521  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.933821146  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:13.933864921  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:13.933897296  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.933914971  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:13.933932096  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:13.933976996  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.933993121  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:13.934018646  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:13.934038471  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:13.934057396  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:13.934101996  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934149296  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934181421  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:13.934198621  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:13.934219621  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934243046  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:13.934281946  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:13.934304521  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934320571  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:13.934336996  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:13.934366821  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934382821  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:13.934422196  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:13.934444896  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934461746  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:13.934478521  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:13.934521896  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934537696  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:13.934554271  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:13.934572796  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934589171  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:13.934625771  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:13.934645721  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:13.934674846  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:13.934698121  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:13.934713421  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:13.934729596  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:13.934787096  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:13.934808271  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:13.934838871  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:13.934857721  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:13.934976209  1674   0x7f70000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:13.935052381  1674   0x7f70000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6403ffb0: went from PAUSED to PLAYING (pending VOID_PENDING)
0:00:16.613556020  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f64041d70: new source 0x7f44013a40
0:00:16.613775895  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1476469126, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:51553, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:16.613841370  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:51553 in 1 transports
0:00:16.613865595  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f64041d70: found transport 0x7f6404c730 for source  0x7f44013a40
0:00:16.613889070  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f64041d70: source 0x7f44013a40 for transport 0x7f6404c730
0:00:16.613922170  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f64041d70: source 0x7f44013a40 in transport 0x7f6404c730 is active
0:00:16.613939970  1674   0x7f70001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f64042c00 alive
0:00:16.614020970  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f64041d70: new SDES 0x7f44013a40
0:00:16.800660442  1674   0x7f6805f8c0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:16.800749376  1674   0x7f6805f8c0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:16.800930615  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378160994568133, sr-rtptime=(uint)835683375, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:16.800930615  1674   0x7f70000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:16.801041234  1674   0x7f70000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
[2021-01-01 17:23:26.807] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:23:26.807] [INFO] Uptime: 20.0 seconds
[2021-01-01 17:23:26.807] [INFO] Total connections: 1
[2021-01-01 17:23:26.807] [INFO] Active connections: 1
[2021-01-01 17:23:26.807] [INFO] Frames served: 5
[2021-01-01 17:23:26.807] [INFO] Clients connected: 0
[2021-01-01 17:23:26.807] [INFO] Error count: 0
0:00:21.310384820  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f64041d70: source 0x7f44013a40 in transport 0x7f6404c730 is active
0:00:21.310456820  1674   0x7f70001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f64042c00 alive
0:00:21.441296461  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:24.514595270  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f64041d70: source 0x7f44013a40 in transport 0x7f6404c730 is active
0:00:24.514646170  1674   0x7f70001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f64042c00 alive
0:00:24.514667302  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request TEARDOWN rtsp://************:8554/stream/ 1.0
0:00:24.514680345  1674   0x7f70001410 INFO              rtspstream rtsp-stream.c:2488:on_bye_ssrc: 0x7f64041d70: source 0x7f44013a40 bye
0:00:24.514720536  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f6403ffb0, target state PLAYING
0:00:24.514737313  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:24.514772262  1674   0x7f70000b70 INFO              rtspstream rtsp-stream.c:4774:update_transport: removing ***********:51552-51553
0:00:24.514818311  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f6403ffb0 do_state 1
0:00:24.514831332  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f6403ffb0
0:00:24.514843035  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f6403ffb0
0:00:24.514855647  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:24.514866210  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:24.514878240  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6403ffb0
0:00:24.514995816  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:24.515062242  1674   0x7f70000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:24.515098750  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515111593  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink1> skipping transition from PLAYING to  PLAYING
0:00:24.515124230  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:24.515139290  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515150899  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:24.515162954  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:24.515177096  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515188220  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee1> skipping transition from PLAYING to  PLAYING
0:00:24.515199617  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:24.515213780  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515226172  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee0> skipping transition from PLAYING to  PLAYING
0:00:24.515238167  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:24.515253831  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515278499  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515289892  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:24.515302013  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:24.515317782  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515329834  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:24.515341725  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:24.515355742  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515366637  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:24.515378111  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:24.515392429  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:24.515405576  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:24.515419104  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515443564  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515455715  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:24.515467659  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:24.515482437  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515493563  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:24.515505107  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:24.515518574  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515529569  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:24.515541160  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:24.515554836  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515565895  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:24.515577008  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:24.515590793  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515601796  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:24.515613065  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:24.515626763  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515639039  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:24.515650811  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:24.515663556  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515674167  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:24.515685571  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:24.515697962  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:24.515710088  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:24.515725349  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.515737346  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:24.515748928  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:24.515762526  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:24.515776602  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:24.515790839  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:24.515801469  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:24.515870920  1674   0x7f70001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = flushing
0:00:24.515907370  1674   0x7f70001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:24.515954795  1674   0x7f70001620 INFO                    task gsttask.c:370:gst_task_func:<udpsrc1:src> Task resume from paused
0:00:24.515976645  1674   0x7f70001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = flushing
0:00:24.516004945  1674   0x7f70001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:24.516035395  1674   0x7f70001410 INFO                    task gsttask.c:370:gst_task_func:<udpsrc0:src> Task resume from paused
0:00:24.516048737  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f6402bff0, time 99:99:99.999999999, seq-num 181, (NULL)
0:00:24.516078920  1674   0x7f70001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = eos
0:00:24.516081349  1674   0x7f70000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f64042320
0:00:24.516119920  1674   0x7f70001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:24.516120802  1674   0x7f70000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f6403ffb0 was not prepared
0:00:24.516154440  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f6403ffb0 is already unpreparing
0:00:24.516176732  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f74006630: sending close message
0:00:24.516210120  1674   0x7f70001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = eos
0:00:24.516247965  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f74006630: session 0x7f64042c00 removed
0:00:24.516262389  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f74006630: unwatch session 0x7f64042c00
0:00:24.516281620  1674   0x7f70000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f64042c00
0:00:24.516330818  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f74006630: connection closed
0:00:24.516346843  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f74006630: watch destroyed
0:00:24.516362845  1674   0x7f70001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:24.516363683  1674   0x7f70000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x558f08be70> unmanage client 0x7f74006630
0:00:24.516412868  1674   0x7f70000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f74006cb0
0:00:24.516428861  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f74006630
0:00:24.516496919  1674   0x7f70000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f74006f10
0:00:24.519838970  1674   0x7f74000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f74006630 connected to server ip ************, ipv6 = 0
0:00:24.519868795  1674   0x7f74000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f74006630 ip ***********:58575
0:00:24.519888520  1674   0x7f74000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x558f08be70> manage client 0x7f74006630
[2021-01-01 17:23:31.110] [INFO] === CLIENT CONNECTED ===
[2021-01-01 17:23:31.110] [INFO] Active connections: 2, Total connections: 2
0:00:24.520035506  1674   0x7f70000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f74008020
0:00:24.520051320  1674   0x7f74000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f74006630: attaching to context 0x7f74008140
0:00:24.529688461  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request OPTIONS rtsp://************:8554/stream 1.0
0:00:24.531737326  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request DESCRIBE rtsp://************:8554/stream 1.0
0:00:24.531768647  1674   0x7f70000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x558f08e280 for path /stream
0:00:24.531787487  1674   0x7f70000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=2 max-size-time=0 max-size-bytes=0 ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=30 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:24.531875127  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:24.531919787  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f6405c9d0> adding pad 'src'
0:00:24.531943229  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 0
0:00:24.531972237  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:24.532006216  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:24.532043751  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f6405cf80> adding pad 'sink'
0:00:24.532071638  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f6405cf80> adding pad 'src'
0:00:24.532104519  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:24.532129764  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f6405f530> adding pad 'sink'
0:00:24.532150367  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f6405f530> adding pad 'src'
0:00:24.532204748  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:24.532232526  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f64060090> adding pad 'sink'
0:00:24.532253776  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f64060090> adding pad 'src'
0:00:24.532283766  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:24.532309581  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f640610a0> adding pad 'sink'
0:00:24.532330429  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f640610a0> adding pad 'src'
0:00:24.532351125  1674   0x7f70000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f640610a0> PTS interpolation: no
0:00:24.532363116  1674   0x7f70000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f640610a0> TS inferring: no
0:00:24.532393604  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:24.532418214  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f64064cc0> adding pad 'src'
0:00:24.532439113  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f64064cc0> adding pad 'sink'
0:00:24.532465419  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:24.532541150  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue1 (0/0) with caps "(NULL)"
0:00:24.532558751  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue1:(any)
0:00:24.532574114  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue1:sink
0:00:24.532592647  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:24.532612276  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue1:src> pad has no peer
0:00:24.532630124  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue1 in same bin, no need for ghost pads
0:00:24.532650987  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue1:sink
0:00:24.532664615  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:24.532679718  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue1:src> pad has no peer
0:00:24.532697994  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue1:sink, successful
0:00:24.532708750  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.532720148  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:24.532747329  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue1 to some pad of GstVideoScale named videoscale1 (0/0) with caps "(NULL)"
0:00:24.532760556  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue1:(any) to element videoscale1:(any)
0:00:24.532775639  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue1:src and videoscale1:sink
0:00:24.532791782  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:24.532809097  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale1:src> pad has no peer
0:00:24.533159022  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue1 and videoscale1 in same bin, no need for ghost pads
0:00:24.533176719  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue1:src and videoscale1:sink
0:00:24.533192733  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:24.533209054  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale1:src> pad has no peer
0:00:24.533545505  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue1:src and videoscale1:sink, successful
0:00:24.533556178  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.533567403  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue1:src> Received event on flushing pad. Discarding
0:00:24.533597193  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale1 to some pad of GstMppH264Enc named mpph264enc1 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:24.533613966  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:24.533644024  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f64067300> adding pad 'sink'
0:00:24.533665190  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f64067300> adding pad 'src'
0:00:24.533682282  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin1> getting state
0:00:24.533710109  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to NULL
0:00:24.533728982  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.533746836  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale1:(any) to element capsfilter1:sink
0:00:24.533759834  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:sink
0:00:24.533771666  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale1 and capsfilter1 in same bin, no need for ghost pads
0:00:24.533790210  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale1:src and capsfilter1:sink
0:00:24.533813679  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:24.534143028  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter1:src> pad has no peer
0:00:24.534183751  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale1:src and capsfilter1:sink, successful
0:00:24.534194333  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.534205940  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale1:src> Received event on flushing pad. Discarding
0:00:24.534224759  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter1:src to element mpph264enc1:(any)
0:00:24.534237032  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:src
0:00:24.534250783  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter1:src and mpph264enc1:sink
0:00:24.534268349  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:24.534671965  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:24.534721028  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter1 and mpph264enc1 in same bin, no need for ghost pads
0:00:24.534738710  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter1:src and mpph264enc1:sink
0:00:24.534768931  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:24.535182554  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:24.535246690  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter1:src and mpph264enc1:sink, successful
0:00:24.535257879  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.535267932  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter1:src> Received event on flushing pad. Discarding
0:00:24.535291662  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc1 to some pad of GstH264Parse named h264parse1 (0/0) with caps "(NULL)"
0:00:24.535304822  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc1:(any) to element h264parse1:(any)
0:00:24.535319549  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc1:src and h264parse1:sink
0:00:24.535337577  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:00:24.535356834  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc1 and h264parse1 in same bin, no need for ghost pads
0:00:24.535372992  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc1:src and h264parse1:sink
0:00:24.535388055  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:00:24.535407083  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc1:src and h264parse1:sink, successful
0:00:24.535416383  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.535426162  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc1:src> Received event on flushing pad. Discarding
0:00:24.535447230  1674   0x7f70000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse1 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:24.535459689  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse1:(any) to element pay0:(any)
0:00:24.535473542  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse1:src and pay0:sink
0:00:24.535491161  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:24.535508620  1674   0x7f70000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse1 and pay0 in same bin, no need for ghost pads
0:00:24.535524623  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse1:src and pay0:sink
0:00:24.535539746  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:24.535555474  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse1:src and pay0:sink, successful
0:00:24.535565057  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.535574850  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse1:src> Received event on flushing pad. Discarding
0:00:24.535606726  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay0
0:00:24.535624207  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f64064cc0
0:00:24.535636334  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:24.535651766  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f6406b400: creating stream with index 0 and payloader <pay0>
0:00:24.535694924  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad5
0:00:24.535708595  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad5, successful
0:00:24.535718355  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.535731955  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:24.535753067  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin1> adding pad 'src_0'
0:00:24.535768479  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f6406dae0
0:00:24.535784521  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f6406dae0> set retransmission time 0
0:00:24.535797405  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f6406dae0> Enabling rate control
0:00:24.535820497  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f6406dae0> set retransmission pt 97
0:00:24.535835511  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay0
0:00:24.535857667  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay0
0:00:24.535876526  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay1
0:00:24.535894998  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay1
0:00:24.535913225  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay1
0:00:24.535937125  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:24.536025488  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f6406dae0> set retransmission time 0
[2021-01-01 17:23:31.126] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 17:23:31.126] [INFO] factory: 0x558f08e280, media: 0x7f6406b400, user_data: 0x558efc6e20
[2021-01-01 17:23:31.126] [INFO] Calling configure_media...
[2021-01-01 17:23:31.126] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 17:23:31.126] [INFO] Got media pipeline: 0x7f64066930
0:00:24.536117145  1674   0x7f70000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element source
[2021-01-01 17:23:31.126] [INFO] Found appsrc element: 0x7f6405c9d0
[2021-01-01 17:23:31.126] [INFO] Connected appsrc signals
0:00:24.536178261  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 545460846592
0:00:24.536192915  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:24.536212283  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
[2021-01-01 17:23:31.126] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 17:23:31.126] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:24.536727718  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f64069090
0:00:24.536759592  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:31.127] [INFO] Initial frame push result: 0
[2021-01-01 17:23:31.127] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 17:23:31.127] [INFO] Configured appsrc properties
[2021-01-01 17:23:31.127] [INFO] Media configured successfully
[2021-01-01 17:23:31.127] [INFO] configure_media call completed
0:00:24.536868987  1674   0x7f70000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f6406b400 for url /stream
0:00:24.536907523  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f6406b400
0:00:24.536920073  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:24.536934520  1674   0x7f70001830 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f64067f40
0:00:24.536936971  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:24.537122835  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:24.537138916  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:24.537174020  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:24.537263320  1674   0x7f70001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:24.537366520  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:24.537396095  1674   0x7f70001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:24.537454120  1674   0x7f70001830 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f6406dae0 joining bin as session 0
0:00:24.537518395  1674   0x7f70001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:24.537669070  1674   0x7f70001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:24.537738920  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f3c005170> adding pad 'sink'
0:00:24.537795345  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f3c005170> adding pad 'rtcp_sink'
0:00:24.537820670  1674   0x7f70001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:24.537937720  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f3c005ab0> adding pad 'src'
0:00:24.537966795  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f3c005ab0> adding pad 'sink'
0:00:24.538204020  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:00:24.538249970  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:00:24.538288095  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:00:24.538358570  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_sink'
0:00:24.538408095  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_src'
0:00:24.538428670  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:send_rtp_src
0:00:24.538518795  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6
0:00:24.538541020  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6, successful
0:00:24.538555395  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.538606520  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_src_0'
0:00:24.538676895  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink
0:00:24.538697620  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink, successful
0:00:24.538711045  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.538735320  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_sink_0'
0:00:24.538825520  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin1:src_0 and rtpbin1:send_rtp_sink_0
0:00:24.538940345  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin1:src_0 and rtpbin1:send_rtp_sink_0, successful
0:00:24.538956395  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.538977670  1674   0x7f70001830 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:24.539017295  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin1:send_rtp_src_0
0:00:24.539100795  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtcp_src'
0:00:24.539199320  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8
0:00:24.539254345  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8, successful
0:00:24.539280395  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.539315070  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtcp_src_0'
0:00:24.539384970  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'recv_rtcp_sink'
0:00:24.539440720  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'sync_src'
0:00:24.539499870  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:sync_src
0:00:24.539521070  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux1:rtcp_sink
0:00:24.539553495  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink
0:00:24.539588470  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink, successful
0:00:24.539611095  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.539678595  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink
0:00:24.539723170  1674   0x7f70001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink, successful
0:00:24.539739470  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.539765095  1674   0x7f70001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'recv_rtcp_sink_0'
0:00:24.539831870  1674   0x7f70001830 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f6406dae0> successfully joined bin
0:00:24.539870645  1674   0x7f70001830 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f6406b400
0:00:24.539888470  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6406b400 set blocked 1
0:00:24.539904770  1674   0x7f70001830 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f6406dae0> blocked: 1
0:00:24.539933495  1674   0x7f70001830 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f6406b400
0:00:24.539951495  1674   0x7f70001830 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f6406b400
0:00:24.539998520  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540046570  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540066595  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to READY
0:00:24.540083895  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540149670  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 2(READY) successfully
0:00:24.540174445  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540193245  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to READY
0:00:24.540224570  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540261495  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 2(READY) successfully
0:00:24.540285995  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540304145  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to READY
0:00:24.540320670  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540343545  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 2(READY) successfully
0:00:24.540374070  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to READY
0:00:24.540393670  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540417920  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 2(READY) successfully
0:00:24.540439695  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540517095  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:24.540537045  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:24.540554245  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540578645  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 2(READY) successfully
0:00:24.540601070  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540627520  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to READY
0:00:24.540644070  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540667370  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 2(READY) successfully
0:00:24.540688845  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540715645  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to READY
0:00:24.540732845  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540787895  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 2(READY) successfully
0:00:24.540812020  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540838070  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to READY
0:00:24.540855495  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540903870  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 2(READY) successfully
0:00:24.540926870  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current NULL pending VOID_PENDING, desired next READY
0:00:24.540944545  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to READY
0:00:24.540970345  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.540994645  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 2(READY) successfully
0:00:24.541026270  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current NULL pending VOID_PENDING, desired next READY
0:00:24.541046645  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to READY
0:00:24.541080345  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.541114970  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 2(READY) successfully
0:00:24.541147070  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:24.541165595  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:24.541182295  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.541205295  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 2(READY) successfully
0:00:24.541232970  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to READY
0:00:24.541249870  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.541288920  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 2(READY) successfully
0:00:24.541329595  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:24.541348745  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:24.541378745  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:24.541430345  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.541474220  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.541511970  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PAUSED
0:00:24.541548420  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.541584070  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 3(PAUSED) successfully
0:00:24.541607420  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.541640445  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PAUSED
0:00:24.541665820  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.541689820  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 3(PAUSED) successfully
0:00:24.541711445  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.541761420  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PAUSED
0:00:24.541779595  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.541803795  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 3(PAUSED) successfully
0:00:24.541851995  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PAUSED
0:00:24.541869495  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.541902270  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 3(PAUSED) successfully
0:00:24.541944045  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.541989070  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.542027695  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:24.542046295  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.542094470  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 3(PAUSED) successfully
0:00:24.542118220  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.542506295  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PAUSED
0:00:24.542528845  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.542556645  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 3(PAUSED) successfully
0:00:24.542604820  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.543286945  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PAUSED
0:00:24.543323445  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.543393620  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 3(PAUSED) successfully
0:00:24.543426195  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.543475445  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PAUSED
0:00:24.543503520  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.543534645  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 3(PAUSED) successfully
0:00:24.543563870  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.543621795  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to PAUSED
0:00:24.543641920  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.543666070  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 3(PAUSED) successfully
0:00:24.543711845  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.543778695  1674   0x7f70001830 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f6405d620 on task 0x7f3c056ce0
0:00:24.543797670  1674   0x7f70001830 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue1:src> created task 0x7f3c056ce0
0:00:24.544028520  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to PAUSED
0:00:24.544070820  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.544123845  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 3(PAUSED) successfully
0:00:24.544157345  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:24.544185170  1674   0x7f70001830 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:24.544233945  1674   0x7f70001830 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.544284620  1674   0x7f70001830 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f6405cd40 on task 0x7f3c057400
0:00:24.544305695  1674   0x7f70001830 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f3c057400
0:00:24.544473470  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:24.544503295  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.544530070  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin1> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:24.544556999  1674   0x7f70001c50 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:24.544560895  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PAUSED
0:00:24.544587806  1674   0x7f70001c50 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:24.544601520  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.544647095  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 3(PAUSED) successfully without preroll
0:00:24.544681020  1674   0x7f70001830 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:24.544699970  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:24.544717570  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.544783320  1674   0x7f70001830 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f6406b400
0:00:24.544802295  1674   0x7f70001830 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6406b400
0:00:24.544951770  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:24.545002645  1674   0x7f70001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:24.545044670  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.545081870  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.545117345  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PLAYING
0:00:24.545161245  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.545191470  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:24.545239620  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.545259295  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PLAYING
0:00:24.545284620  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.545325395  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:24.545358820  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.545534945  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PLAYING
0:00:24.545555695  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.545587520  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:24.545637720  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:24.545667070  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.545750770  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:24.545805845  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.545831745  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:24.545877320  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.545908020  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:24.545949845  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.545980120  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PLAYING
0:00:24.546015020  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.546043170  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:24.546071195  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.546095495  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PLAYING
0:00:24.546123895  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.546165720  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:24.546188595  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.546220220  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PLAYING
0:00:24.546246945  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.546272295  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:24.546311070  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.546331245  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to PLAYING
0:00:24.546350620  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.546387170  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:24.546429445  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.546448420  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to PLAYING
0:00:24.546464670  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.546533120  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:24.546567595  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:24.546605395  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.546627880  1674   0x7f70001c50 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:24.546646670  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:24.546689595  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:24.546719245  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.546762562  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:24.546783345  1674   0x7f70001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:24.546791858  1674   0x7f70001c50 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.546838829  1674   0x7f70001c50 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
0:00:24.546817095  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
[2021-01-01 17:23:31.137] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:23:31.137] [INFO] appsrc: 0x7f6405c9d0, unused: 4096, user_data: 0x558efc6e20
0:00:24.546907870  1674   0x7f70001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pip[2021-01-01 17:23:31.137] [INFO] Calling feed_data...
eline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
[2021-01-01 17:23:31.137] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:23:31.137] [INFO] appsrc: 0x7f6405c9d0
[2021-01-01 17:23:31.137] [INFO] DDS reader is initialized
[2021-01-01 17:23:31.137] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:23:31.137] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:23:31.137] [INFO] Creating GstBuffer for raw frame data...
0:00:24.547210545  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6406b400: went from NULL to READY (pending PAUSED)
[2021-01-01 17:23:31.138] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:23:31.138] [INFO] Pushing raw buffer directly to appsrc...
0:00:24.547456860  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f34002690
0:00:24.547498158  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:31.138] [INFO] Raw frame fed to appsrc successfully, total frames served: 7
0:00:24.547556670  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6406b400: went from READY to PAUSED (pending VOID_PENDING)
0:00:24.547607895  1674   0x7f70001830 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f6406b400: got message type 2048 (new-clock)
[2021-01-01 17:23:31.138] [INFO] feed_data call completed
0:00:24.547695793  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:23:31.138] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:23:31.138] [INFO] appsrc: 0x7f6405c9d0, unused: 4096, user_data: 0x558efc6e20
[2021-01-01 17:23:31.138] [INFO] Calling feed_data...
[2021-01-01 17:23:31.138] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:23:31.138] [INFO] appsrc: 0x7f6405c9d0
[2021-01-01 17:23:31.138] [INFO] DDS reader is initialized
[2021-01-01 17:23:31.138] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:23:31.138] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:23:31.138] [INFO] Creating GstBuffer for raw frame data...
0:00:24.547894096  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6406b400: went from PAUSED to PLAYING (pending VOID_PENDING)
0:00:24.547931671  1674   0x7f70001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 17:23:31.138] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:23:31.139] [INFO] Pushing raw buffer directly to appsrc...
0:00:24.548315030  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f3412f740
0:00:24.548335321  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:31.139] [INFO] Raw frame fed to appsrc successfully, total frames served: 8
[2021-01-01 17:23:31.139] [INFO] feed_data call completed
0:00:24.548376396  1674   0x7f70001a40 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter1> reuse caps
0:00:24.548399887  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:24.548419571  1674   0x7f70001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:24.548746421  1674   0x7f70001a40 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc1> applying YUY2 1280x720 (2560x720)
0:00:24.549197646  1674   0x7f70001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
[2021-01-01 17:23:31.140] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:23:31.140] [INFO] appsrc: 0x7f6405c9d0, unused: 4096, user_data: 0x558efc6e20
[2021-01-01 17:23:31.140] [INFO] Calling feed_data...
[2021-01-01 17:23:31.140] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:23:31.140] [INFO] appsrc: 0x7f6405c9d0
[2021-01-01 17:23:31.140] [INFO] DDS reader is initialized
[2021-01-01 17:23:31.140] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:23:31.140] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:23:31.140] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:23:31.141] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:23:31.141] [INFO] Pushing raw buffer directly to appsrc...
0:00:24.550552169  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f341c6490
0:00:24.550579411  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:31.141] [INFO] Raw frame fed to appsrc successfully, total frames served: 9
[2021-01-01 17:23:31.141] [INFO] feed_data call completed
0:00:24.550648989  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:24.578974956  1674   0x7f70001a40 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f640609e0 on task 0x7f40035cc0
0:00:24.579003664  1674   0x7f70001a40 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc1:src> created task 0x7f40035cc0
[2021-01-01 17:23:31.169] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:23:31.170] [INFO] appsrc: 0x7f6405c9d0, unused: 4096, user_data: 0x558efc6e20
[2021-01-01 17:23:31.170] [INFO] Calling feed_data...
[2021-01-01 17:23:31.170] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:23:31.170] [INFO] appsrc: 0x7f6405c9d0
[2021-01-01 17:23:31.170] [INFO] DDS reader is initialized
[2021-01-01 17:23:31.170] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:23:31.170] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:23:31.170] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:23:31.170] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:23:31.170] [INFO] Pushing raw buffer directly to appsrc...
0:00:24.580084740  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f3425d0c0
0:00:24.580111328  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:23:31.170] [INFO] Raw frame fed to appsrc successfully, total frames served: 10
[2021-01-01 17:23:31.170] [INFO] feed_data call completed
0:00:24.580292729  1674   0x7f70001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:24.582859596  1674   0x7f70001e60 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.584727546  1674   0x7f70001e60 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse1> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:24.584859421  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:24.584900471  1674   0x7f70001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:24.584989621  1674   0x7f70001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:24.585333571  1674   0x7f70001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)3253555652, timestamp-offset=(uint)2590259037, seqnum-offset=(uint)11614, a-framerate=(string)30
0:00:24.585399046  1674   0x7f70001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:24.585447546  1674   0x7f70001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:24.585488946  1674   0x7f70001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:24.585560521  1674   0x7f70001e60 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f6406dae0: new sender source 0x7f24010860
0:00:24.585661296  1674   0x7f70001e60 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:24.585725696  1674   0x7f70001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:24.585780346  1674   0x7f70001e60 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f6406dae0 received caps 0x7f2400c880, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)3253555652, timestamp-offset=(uint)2590259037, seqnum-offset=(uint)11614, a-framerate=(string)30
0:00:24.586216921  1674   0x7f70001e60 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession1> Can't determine running time for this packet without knowing configured latency
0:00:24.586278796  1674   0x7f70001e60 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin1:send_rtp_src_0> Now blocking
0:00:24.586306171  1674   0x7f70001e60 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f6406dae0> position: 447089:23:30.942533000
0:00:24.586399271  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f6406b400> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:24.586424746  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:24.586438546  1674   0x7f70001830 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:24.586452046  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:24.586482623  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:24.586501882  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f6406b400 is prerolled
0:00:24.586531456  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:24.586574921  1674   0x7f70000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:24.586627763  1674   0x7f70000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:24.586641853  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f6406b400 no suspend
0:00:24.586652729  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:24.586671656  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://************:8554/stream/
0:00:24.589781658  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request SETUP rtsp://************:8554/stream/stream=0 1.0
0:00:24.589813095  1674   0x7f70000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x558f08e280 for path /stream/stream=0
0:00:24.589827980  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f6406b400 for path /stream
0:00:24.589838842  1674   0x7f70000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:24.589850569  1674   0x7f70000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f6406b400 was not prepared
0:00:24.589881028  1674   0x7f70000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f640696d0
0:00:24.589898100  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f640696d0
0:00:24.589930387  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP/TCP;unicast;interleaved=0-1
0:00:24.589942951  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:24.589951489  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:24.589964892  1674   0x7f70000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f6406b400 in session 0x7f6406a580
0:00:24.594398462  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request PLAY rtsp://************:8554/stream/ 1.0
0:00:24.594449119  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f6406b400> complete pipeline
0:00:24.594463265  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f6406dae0> complete stream
0:00:24.594473945  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f6406dae0> create receiver part
0:00:24.594497998  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f6406dae0> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:24.594513540  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:24.594565616  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f6406bcf0> adding pad 'src'
0:00:24.594603549  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel1:src
0:00:24.594627888  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel1:src and rtpbin1:recv_rtcp_sink_0
0:00:24.594666642  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel1:src and rtpbin1:recv_rtcp_sink_0, successful
0:00:24.594677476  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.594688796  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel1:src> Received event on flushing pad. Discarding
0:00:24.594706252  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:24.594737848  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f6406c3e0> adding pad 'src'
0:00:24.594785269  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsrc0:src
0:00:24.594805464  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsrc0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:24.594818212  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:24.594844151  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsrc0> continue state change READY to PAUSED, final PLAYING
0:00:24.594858472  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<appsrc0> starting
0:00:24.594887215  1674   0x7f70000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<appsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.594915155  1674   0x7f70000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f6406c750 on task 0x7f6406ce70
0:00:24.594927303  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<appsrc0:src> created task 0x7f6406ce70
0:00:24.595090293  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:24.595106667  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:24.595129638  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:24.595143787  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsrc0> completed state change to PLAYING
0:00:24.595156116  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.595209163  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel1> adding pad 'funnelpad2'
0:00:24.595223649  1674   0x7f70002070 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "appsrc0"
0:00:24.595266878  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link appsrc0:src and funnel1:funnelpad2
0:00:24.595271649  1674   0x7f70002070 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<appsrc0:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:24.595289776  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<appsrc0> caps: (NULL)
0:00:24.595349342  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked appsrc0:src and funnel1:funnelpad2, successful
0:00:24.595362521  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.595390837  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel1> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:24.595404147  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed NULL to READY (PLAYING pending)
0:00:24.595427122  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel1> continue state change READY to PAUSED, final PLAYING
0:00:24.595451384  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:24.595464893  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:24.595482756  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel1> continue state change PAUSED to PLAYING, final PLAYING
0:00:24.595495103  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel1> completed state change to PLAYING
0:00:24.595508200  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.595529404  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f6406dae0> create sender part
0:00:24.595544753  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f6406dae0> tcp: 1, udp: 0, mcast: 0 (ttl: 0)
0:00:24.595569249  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:24.595611182  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f6406ba90> adding pad 'sink'
0:00:24.595648058  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsink"
0:00:24.595807768  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f6406f950> adding pad 'sink'
0:00:24.595838750  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3472:plug_tcp_sink:<GstRTSPStream@0x7f6406dae0> plug tcp sink
0:00:24.595875886  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee2> adding pad 'src_0'
0:00:24.595890828  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsink0:sink
0:00:24.595910083  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee2:src_0 and appsink0:sink
0:00:24.595926825  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee2:sink> pad has no peer
0:00:24.595948521  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee2:src_0 and appsink0:sink, successful
0:00:24.595959241  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.595969466  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee2:src_0> Received event on flushing pad. Discarding
0:00:24.595992175  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:24.596004829  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:24.596026252  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsink0> continue state change READY to PAUSED, final PLAYING
0:00:24.596044513  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:24.596072989  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee2> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:24.596080905  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6406b400: went from PAUSED to PAUSED (pending PAUSED)
0:00:24.596087896  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed NULL to READY (PAUSED pending)
0:00:24.596124474  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee2> continue state change READY to PAUSED, final PAUSED
0:00:24.596141614  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee2> completed state change to PAUSED
0:00:24.596154119  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.596169540  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee2:sink
0:00:24.596187072  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin1:send_rtp_src_0 and tee2:sink
0:00:24.596247957  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin1:send_rtp_src_0 and tee2:sink, successful
0:00:24.596260215  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.596307489  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:24.596350538  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f64071080> adding pad 'sink'
0:00:24.596386409  1674   0x7f70000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsink"
0:00:24.596410113  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f64071880> adding pad 'sink'
0:00:24.596436988  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:3472:plug_tcp_sink:<GstRTSPStream@0x7f6406dae0> plug tcp sink
0:00:24.596467405  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee3> adding pad 'src_0'
0:00:24.596481031  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsink1:sink
0:00:24.596498041  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee3:src_0 and appsink1:sink
0:00:24.596513621  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee3:sink> pad has no peer
0:00:24.596534095  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee3:src_0 and appsink1:sink, successful
0:00:24.596544234  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.596553677  1674   0x7f70000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee3:src_0> Received event on flushing pad. Discarding
0:00:24.596575226  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:24.596587334  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:24.596603072  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsink1> continue state change READY to PAUSED, final PAUSED
0:00:24.596621049  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsink1> completed state change to PAUSED
0:00:24.596633415  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.596657479  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee3> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:24.596669947  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed NULL to READY (PAUSED pending)
0:00:24.596686769  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee3> continue state change READY to PAUSED, final PAUSED
0:00:24.596703063  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee3> completed state change to PAUSED
0:00:24.596716054  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.596731312  1674   0x7f70000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee3:sink
0:00:24.596748861  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin1:send_rtcp_src_0 and tee3:sink
0:00:24.596780151  1674   0x7f70000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin1:send_rtcp_src_0 and tee3:sink, successful
0:00:24.596790729  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.596811739  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f6406dae0> pipeline successfully updated
0:00:24.596824558  1674   0x7f70000b70 INFO              rtspstream rtsp-stream.c:4783:update_transport: adding TCP ***********
0:00:24.596875160  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:24.596887738  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:24.596910615  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f6406dae0> Enabling rate control
0:00:24.596940967  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:24.596972052  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:24.597010143  1674   0x7f70001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:24.597085624  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f6406b400> seekable:0
0:00:24.597100497  1674   0x7f70000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f6406b400> Handle going back to 0 for none live not seekable streams.
0:00:24.597113230  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:24.597124922  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:24.597134882  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:24.597160827  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:24.597241827  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f6406b400, target state PAUSED
0:00:24.597256366  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:24.597268523  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6406b400 set blocked 0
0:00:24.597280814  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f6406dae0> blocked: 0
0:00:24.597299133  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f6406b400 do_state 1
0:00:24.597310523  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f6406b400
0:00:24.597322059  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f6406b400
0:00:24.597333957  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6406b400 set blocked 0
0:00:24.597344977  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f6406dae0> blocked: 0
0:00:24.597356496  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6406b400
0:00:24.597369337  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:24.597417950  1674   0x7f70001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)3253555652, timestamp-offset=(uint)2590259037, seqnum-offset=(uint)11614, a-framerate=(string)30
0:00:24.597525107  1674   0x7f70001830 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f6406b400: got message type 16 (tag)
0:00:24.597555000  1674   0x7f70001e60 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:24.597577950  1674   0x7f70001e60 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:24.597596425  1674   0x7f70001e60 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:24.597634724  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6406b400: went from PAUSED to PAUSED (pending PLAYING)
0:00:24.597662794  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f6406b400> got async-done
0:00:24.597898505  1674   0x7f70002280 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:24.598007756  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:24.598014099  1674   0x7f70002280 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:24.598099292  1674   0x7f70002280 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:24.598139702  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.598156136  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsink1> completed state change to PLAYING
0:00:24.598168534  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.598194431  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink1' changed state to 4(PLAYING) successfully
0:00:24.598211422  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598223202  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink0> skipping transition from PLAYING to  PLAYING
0:00:24.598234617  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink0' changed state to 4(PLAYING) successfully
0:00:24.598249520  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee3> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.598262468  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee3> completed state change to PLAYING
0:00:24.598273672  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.598290827  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee3' changed state to 4(PLAYING) successfully
0:00:24.598306492  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee2> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.598319043  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee2> completed state change to PLAYING
0:00:24.598321888  1674   0x7f70001a40 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter1> reuse caps
0:00:24.598333116  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.598369346  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee2' changed state to 4(PLAYING) successfully
0:00:24.598385985  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598410536  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598422326  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage1> skipping transition from PLAYING to  PLAYING
0:00:24.598433830  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:24.598448605  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598459954  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux1> skipping transition from PLAYING to  PLAYING
0:00:24.598471670  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:24.598485815  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598496910  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession1> skipping transition from PLAYING to  PLAYING
0:00:24.598508516  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:24.598521711  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:24.598534413  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:24.598547411  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598571876  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598583367  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:24.598594540  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:24.598608044  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598618365  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse1> skipping transition from PLAYING to  PLAYING
0:00:24.598629371  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:24.598642587  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598653199  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc1> skipping transition from PLAYING to  PLAYING
0:00:24.598664064  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:24.598677121  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598687663  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:24.598698553  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:24.598712027  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598722353  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale1> skipping transition from PLAYING to  PLAYING
0:00:24.598733062  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:24.598758597  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598771141  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue1> skipping transition from PLAYING to  PLAYING
0:00:24.598782949  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:24.598794836  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598805036  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:24.598817220  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:24.598831369  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:24.598844710  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:24.598859657  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.598870198  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel1> skipping transition from PLAYING to  PLAYING
0:00:24.598881450  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel1' changed state to 4(PLAYING) successfully
0:00:24.598894892  1674   0x7f70002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsrc0' changed state to 4(PLAYING) successfully
0:00:24.598910092  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:24.598921951  1674   0x7f70002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.599013085  1674   0x7f70001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:24.599090497  1674   0x7f70001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6406b400: went from PAUSED to PLAYING (pending VOID_PENDING)
0:00:26.277192836  1674   0x7f3c000df0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:26.277267742  1674   0x7f3c000df0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:26.277302240  1674   0x7f3c000df0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:26.277555378  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378201695928792, sr-rtptime=(uint)3191407571, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:26.277580575  1674   0x7f70001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:26.277588364  1674   0x7f2c001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f6406dae0> message send complete
0:00:26.277649527  1674   0x7f2c001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f640696d0 alive
0:00:26.277721025  1674   0x7f70001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:26.287084900  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:27.295713000  1674   0x7f70000b70 DEBUG             rtspstream rtsp-stream.c:4664:gst_rtsp_stream_recv_rtcp: stream 0x7f6406dae0: first buffer at time 5:10:24.496946106, base 5:10:21.746074700
0:00:27.295743600  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<appsrc0> queueing buffer 0x7f640708a0
0:00:27.295761975  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<appsrc0> Currently queued: 36 bytes, 1 buffers, 0:00:00.000000000
0:00:27.295792908  1674   0x7f70002070 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<appsrc0> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:27.295827637  1674   0x7f70002070 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:27.295860352  1674   0x7f70002070 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<appsrc0> marking pending DISCONT
0:00:27.295912266  1674   0x7f70002070 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:27.296009011  1674   0x7f70002070 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f6406dae0: new source 0x7f28002220
0:00:27.296112361  1674   0x7f70002070 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1695397038, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:58575, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:27.296162145  1674   0x7f70002070 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:58575 in 1 transports
0:00:27.296175739  1674   0x7f70002070 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f6406dae0: found transport 0x7f6406a770 for source  0x7f28002220
0:00:27.296190861  1674   0x7f70002070 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f6406dae0: source 0x7f28002220 for transport 0x7f6406a770
0:00:27.296210688  1674   0x7f70002070 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f6406dae0: source 0x7f28002220 in transport 0x7f6406a770 is active
0:00:27.296222496  1674   0x7f70002070 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f640696d0 alive
0:00:27.296272704  1674   0x7f70002070 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f6406dae0: new SDES 0x7f28002220
[2021-01-01 17:23:36.808] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:23:36.808] [INFO] Uptime: 30.0 seconds
[2021-01-01 17:23:36.808] [INFO] Total connections: 2
[2021-01-01 17:23:36.808] [INFO] Active connections: 2
[2021-01-01 17:23:36.808] [INFO] Frames served: 10
[2021-01-01 17:23:36.808] [INFO] Clients connected: 0
[2021-01-01 17:23:36.808] [INFO] Error count: 0
0:00:30.863079799  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2496:on_bye_timeout: 0x7f64041d70: source 0x7f44013a40 bye timeout
0:00:30.863434574  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:31.090196271  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:31.090201446  1674   0x7f2c001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f6406dae0> message send complete
0:00:31.090261608  1674   0x7f2c001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f640696d0 alive
0:00:33.021492999  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<appsrc0> queueing buffer 0x7f64052c30
0:00:33.021526830  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<appsrc0> Currently queued: 36 bytes, 1 buffers, 0:00:00.000000000
0:00:33.021558935  1674   0x7f70002070 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<appsrc0> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:33.021629861  1674   0x7f70002070 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f6406dae0: source 0x7f28002220 in transport 0x7f6406a770 is active
0:00:33.021646567  1674   0x7f70002070 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f640696d0 alive
0:00:34.912450499  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:35.120578205  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f74006630: received a request TEARDOWN rtsp://************:8554/stream/ 1.0
0:00:35.120639440  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f6406b400, target state PLAYING
0:00:35.120654445  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:35.120667625  1674   0x7f70000b70 INFO              rtspstream rtsp-stream.c:4787:update_transport: removing TCP ***********
0:00:35.120681771  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f6406b400 do_state 1
0:00:35.120692763  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f6406b400
0:00:35.120704582  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f6406b400
0:00:35.120716780  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:35.120727922  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:35.120740067  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6406b400
0:00:35.120859919  1674   0x7f70000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:35.120922229  1674   0x7f70000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:35.120957667  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.120970364  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink1> skipping transition from PLAYING to  PLAYING
0:00:35.120982351  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink1' changed state to 4(PLAYING) successfully
0:00:35.120997064  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121008302  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink0> skipping transition from PLAYING to  PLAYING
0:00:35.121021907  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink0' changed state to 4(PLAYING) successfully
0:00:35.121035846  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee3> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121046771  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee3> skipping transition from PLAYING to  PLAYING
0:00:35.121058279  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee3' changed state to 4(PLAYING) successfully
0:00:35.121072109  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee2> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121084037  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee2> skipping transition from PLAYING to  PLAYING
0:00:35.121095916  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee2' changed state to 4(PLAYING) successfully
0:00:35.121110793  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121134310  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121145359  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage1> skipping transition from PLAYING to  PLAYING
0:00:35.121156642  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:35.121170517  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121181738  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux1> skipping transition from PLAYING to  PLAYING
0:00:35.121193652  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:35.121206748  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121217219  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession1> skipping transition from PLAYING to  PLAYING
0:00:35.121228123  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:35.121241254  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:35.121253563  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:35.121265993  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121288890  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121300465  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:35.121311862  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:35.121325164  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121335698  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse1> skipping transition from PLAYING to  PLAYING
0:00:35.121346373  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:35.121359602  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121369928  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc1> skipping transition from PLAYING to  PLAYING
0:00:35.121380855  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:35.121394135  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121405230  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:35.121416532  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:35.121429647  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121440083  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale1> skipping transition from PLAYING to  PLAYING
0:00:35.121450822  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:35.121463611  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121474052  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue1> skipping transition from PLAYING to  PLAYING
0:00:35.121484680  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:35.121497150  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121507438  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:35.121518012  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:35.121530391  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:35.121542255  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:35.121555500  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:35.121566234  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel1> skipping transition from PLAYING to  PLAYING
0:00:35.121576856  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel1' changed state to 4(PLAYING) successfully
0:00:35.121589395  1674   0x7f70000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsrc0' changed state to 4(PLAYING) successfully
0:00:35.121602577  1674   0x7f70000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:35.121612890  1674   0x7f70000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:35.121634535  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<appsrc0> queue event: eos event: 0x7f6405b370, time 99:99:99.999999999, seq-num 352, (NULL)
0:00:35.121664222  1674   0x7f70000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f6405b370, time 99:99:99.999999999, seq-num 352, (NULL)
0:00:35.121665644  1674   0x7f70002070 DEBUG                 appsrc gstappsrc.c:1683:gst_app_src_create:<appsrc0> pop event eos event: 0x7f6405b370, time 99:99:99.999999999, seq-num 352, (NULL)
0:00:35.121690131  1674   0x7f70000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f6406a580
0:00:35.121704719  1674   0x7f70000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f6406b400 was not prepared
0:00:35.121715866  1674   0x7f70000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f6406b400 is already unpreparing
0:00:35.121736000  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f74006630: sending close message
0:00:35.121800758  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f74006630: session 0x7f640696d0 removed
0:00:35.121814400  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f74006630: unwatch session 0x7f640696d0
0:00:35.121832772  1674   0x7f70000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f640696d0
0:00:35.121902976  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f74006630: connection closed
0:00:35.121919901  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f74006630: watch destroyed
0:00:35.121937277  1674   0x7f70000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x558f08be70> unmanage client 0x7f74006630
0:00:35.121959575  1674   0x7f70000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f74007550
0:00:35.121974645  1674   0x7f70000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f74006630
0:00:35.122053072  1674   0x7f70000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f74008020
0:00:36.980461023  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:39.670165049  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 17:23:46.809] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:23:46.809] [INFO] Uptime: 40.0 seconds
[2021-01-01 17:23:46.809] [INFO] Total connections: 2
[2021-01-01 17:23:46.809] [INFO] Active connections: 2
[2021-01-01 17:23:46.809] [INFO] Frames served: 10
[2021-01-01 17:23:46.809] [INFO] Clients connected: 0
[2021-01-01 17:23:46.809] [INFO] Error count: 0
0:00:43.099397159  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:45.215931173  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:48.081681282  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:50.148920198  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 17:23:56.809] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:23:56.810] [INFO] Uptime: 50.0 seconds
[2021-01-01 17:23:56.810] [INFO] Total connections: 2
[2021-01-01 17:23:56.810] [INFO] Active connections: 2
[2021-01-01 17:23:56.810] [INFO] Frames served: 10
[2021-01-01 17:23:56.810] [INFO] Clients connected: 0
[2021-01-01 17:23:56.810] [INFO] Error count: 0
0:00:51.049104114  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:54.987757673  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:56.966307637  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 17:24:06.810] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:24:06.810] [INFO] Uptime: 60.0 seconds
[2021-01-01 17:24:06.810] [INFO] Total connections: 2
[2021-01-01 17:24:06.810] [INFO] Active connections: 2
[2021-01-01 17:24:06.810] [INFO] Frames served: 10
[2021-01-01 17:24:06.810] [INFO] Clients connected: 0
[2021-01-01 17:24:06.810] [INFO] Error count: 0
0:01:01.011258347  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:01:01.118393010  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2509:on_timeout: 0x7f6406dae0: source 0x7f28002220 timeout
0:01:01.118590302  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:01:04.429326147  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:01:04.732754252  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:01:09.562044496  1674   0x7f6805f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2656434352, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)14874, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378180925582913, sr-rtptime=(uint)836101024, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:01:09.944316132  1674   0x7f3c000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3253555652, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)11614, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400378222367477539, sr-rtptime=(uint)3191840739, sr-octet-count=(uint)25, sr-packet-count=(uint)1;