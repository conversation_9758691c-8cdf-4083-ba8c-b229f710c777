[2021-01-01 12:11:08.302] [INFO] === RTSP Server Statistics ===
[2021-01-01 12:11:08.302] [INFO] Uptime: 140.0 seconds
[2021-01-01 12:11:08.302] [INFO] Total connections: 0
[2021-01-01 12:11:08.302] [INFO] Active connections: 0
[2021-01-01 12:11:08.302] [INFO] Frames served: 0
[2021-01-01 12:11:08.302] [INFO] Clients connected: 0
[2021-01-01 12:11:08.302] [INFO] Error count: 0
[2021-01-01 12:11:18.303] [INFO] === RTSP Server Statistics ===
[2021-01-01 12:11:18.303] [INFO] Uptime: 150.0 seconds
[2021-01-01 12:11:18.303] [INFO] Total connections: 0
[2021-01-01 12:11:18.303] [INFO] Active connections: 0
[2021-01-01 12:11:18.303] [INFO] Frames served: 0
[2021-01-01 12:11:18.303] [INFO] Clients connected: 0
[2021-01-01 12:11:18.303] [INFO] Error count: 0
0:02:35.996632317  1187   0x7f6c000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f6c006630 connected to server ip ************, ipv6 = 0
0:02:35.996661464  1187   0x7f6c000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f6c006630 ip ***********:49677
0:02:35.996681081  1187   0x7f6c000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x5589ccdc20> manage client 0x7f6c006630
[2021-01-01 12:11:24.036] [INFO] === CLIENT CONNECTED ===
[2021-01-01 12:11:24.036] [INFO] Active connections: 1, Total connections: 1
0:02:35.997073103  1187   0x7f64000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f6c006f10
0:02:35.997074294  1187   0x7f6c000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f6c006630: attaching to context 0x7f6c007440
0:02:35.997370987  1187   0x7f64000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f6c006630: received a request OPTIONS rtsp://************:8554/stream 1.0
0:02:36.000037470  1187   0x7f64000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f6c006630: received a request DESCRIBE rtsp://************:8554/stream 1.0
0:02:36.000070226  1187   0x7f64000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x5589ccbbe0 for path /stream
0:02:36.000087829  1187   0x7f64000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:02:36.001052524  1187   0x7f64000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstapp.so" loaded
0:02:36.001323315  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:02:36.001378025  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f58007680> adding pad 'src'
0:02:36.001398927  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 0
0:02:36.001430555  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:02:36.004878021  1187   0x7f64000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:02:36.005027157  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:02:36.005073534  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f5800d010> adding pad 'sink'
0:02:36.005101578  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f5800d010> adding pad 'src'
0:02:36.006369519  1187   0x7f64000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoconvertscale.so" loaded
0:02:36.007228554  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:02:36.007266056  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f58017830> adding pad 'sink'
0:02:36.007292902  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f58017830> adding pad 'src'
0:02:36.014245890  1187   0x7f64000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:02:36.014478453  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:02:36.014513834  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f5801bc60> adding pad 'sink'
0:02:36.014535168  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f5801bc60> adding pad 'src'
0:02:36.019825146  1187   0x7f64000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:02:36.019957080  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:02:36.019990920  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f58024710> adding pad 'sink'
0:02:36.020012708  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f58024710> adding pad 'src'
0:02:36.020043985  1187   0x7f64000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f58024710> PTS interpolation: no
0:02:36.020056595  1187   0x7f64000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f58024710> TS inferring: no
0:02:36.026309251  1187   0x7f64000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:02:36.026660088  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:02:36.026747078  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f58034580> adding pad 'src'
0:02:36.026789731  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f58034580> adding pad 'sink'
0:02:36.026846327  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:02:36.026935845  1187   0x7f64000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue0 (0/0) with caps "(NULL)"
0:02:36.026951774  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue0:(any)
0:02:36.026976231  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue0:sink
0:02:36.026994770  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:02:36.027014169  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:02:36.027031060  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue0 in same bin, no need for ghost pads
0:02:36.027055446  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue0:sink
0:02:36.027068674  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:02:36.027090885  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:02:36.027108161  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue0:sink, successful
0:02:36.027117875  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.027128519  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:02:36.027159103  1187   0x7f64000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue0 to some pad of GstVideoScale named videoscale0 (0/0) with caps "(NULL)"
0:02:36.027172278  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue0:(any) to element videoscale0:(any)
0:02:36.027185904  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue0:src and videoscale0:sink
0:02:36.027200181  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:02:36.027217035  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:02:36.027594456  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue0 and videoscale0 in same bin, no need for ghost pads
0:02:36.027613137  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue0:src and videoscale0:sink
0:02:36.027627983  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:02:36.027643624  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:02:36.027958135  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue0:src and videoscale0:sink, successful
0:02:36.027968559  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.027978479  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue0:src> Received event on flushing pad. Discarding
0:02:36.028007394  1187   0x7f64000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:02:36.028025070  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:02:36.028103539  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f5803dfc0> adding pad 'sink'
0:02:36.028125692  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f5803dfc0> adding pad 'src'
0:02:36.028142351  1187   0x7f64000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:02:36.028168818  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:02:36.028184592  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.028201552  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale0:(any) to element capsfilter0:sink
0:02:36.028213370  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:02:36.028224931  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale0 and capsfilter0 in same bin, no need for ghost pads
0:02:36.028241629  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale0:src and capsfilter0:sink
0:02:36.028260421  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:02:36.028593553  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:02:36.028631686  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale0:src and capsfilter0:sink, successful
0:02:36.028641494  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.028651087  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale0:src> Received event on flushing pad. Discarding
0:02:36.028669479  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mpph264enc0:(any)
0:02:36.028680667  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:02:36.028693488  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mpph264enc0:sink
0:02:36.028710137  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:02:36.029091865  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:02:36.029139521  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mpph264enc0 in same bin, no need for ghost pads
0:02:36.029156986  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mpph264enc0:sink
0:02:36.029173418  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:02:36.029553816  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:02:36.029609860  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mpph264enc0:sink, successful
0:02:36.029619629  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.029628904  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:02:36.029651700  1187   0x7f64000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:02:36.029664531  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:02:36.029677889  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:02:36.029694882  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:02:36.029713040  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:02:36.029728507  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:02:36.029742530  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:02:36.029760751  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:02:36.029770220  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.029779405  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:02:36.029799644  1187   0x7f64000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:02:36.029811462  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:02:36.029824255  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:02:36.029841748  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:02:36.029857897  1187   0x7f64000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:02:36.029873124  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:02:36.029887909  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:02:36.029902580  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:02:36.029911380  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.029920549  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:02:36.030039452  1187   0x7f64000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:02:36.030058472  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f58034580
0:02:36.030069859  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:02:36.030099414  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f5803fbb0: creating stream with index 0 and payloader <pay0>
0:02:36.030221185  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:02:36.030235604  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:02:36.030244924  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.030254594  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:02:36.030274379  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:02:36.030311564  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f58041a60
0:02:36.030326931  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f58041a60> set retransmission time 0
0:02:36.030338560  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f58041a60> Enabling rate control
0:02:36.030359279  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f58041a60> set retransmission pt 97
0:02:36.030372579  1187   0x7f64000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:02:36.030392587  1187   0x7f64000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:02:36.030409749  1187   0x7f64000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:02:36.030425954  1187   0x7f64000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:02:36.030442336  1187   0x7f64000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:02:36.030463107  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:02:36.030560714  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f58041a60> set retransmission time 0
[2021-01-01 12:11:24.070] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 12:11:24.070] [INFO] factory: 0x5589ccbbe0, media: 0x7f5803fbb0, user_data: 0x5589b94e20
[2021-01-01 12:11:24.070] [INFO] Calling configure_media...
[2021-01-01 12:11:24.070] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 12:11:24.070] [INFO] Got media pipeline: 0x7f580361d0
0:02:36.030649412  1187   0x7f64000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element source
[2021-01-01 12:11:24.070] [INFO] Found appsrc element: 0x7f58007680
[2021-01-01 12:11:24.070] [INFO] Connected appsrc signals
0:02:36.030705053  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 545460846592
0:02:36.030731094  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:02:36.030750660  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:02:36.030765326  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 12:11:24.071] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 12:11:24.071] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:02:36.031256549  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f5802edb0
0:02:36.031288504  1187   0x7f64000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 12:11:24.071] [INFO] Initial frame push result: 0
[2021-01-01 12:11:24.071] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 12:11:24.071] [INFO] Configured appsrc properties
[2021-01-01 12:11:24.071] [INFO] Pipeline latency query result: live=0, min=0 ns, max=18446744073709551615 ns
[2021-01-01 12:11:24.071] [INFO] Setting manual latency: min=33333333 ns, max=100000000 ns
0:02:36.031396185  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.033333333
0:02:36.031426486  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
[2021-01-01 12:11:24.071] [INFO] Sent latency event to pipeline
[2021-01-01 12:11:24.071] [INFO] Media configured successfully
[2021-01-01 12:11:24.071] [INFO] configure_media call completed
0:02:36.031495440  1187   0x7f64000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f5803fbb0 for url /stream
0:02:36.031623136  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f5803fbb0
0:02:36.031635308  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:02:36.031698437  1187   0x7f64000d80 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f58039670
0:02:36.035398328  1187   0x7f64000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:02:36.035423473  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:02:36.035975423  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:02:36.035989497  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:02:36.036044337  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:02:36.036088662  1187   0x7f64000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:02:36.036157587  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:02:36.036186312  1187   0x7f64000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:02:36.036231537  1187   0x7f64000d80 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f58041a60 joining bin as session 0
0:02:36.036271237  1187   0x7f64000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:02:36.037317962  1187   0x7f64000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:02:36.037498312  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f5400b3b0> adding pad 'sink'
0:02:36.037538087  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f5400b3b0> adding pad 'rtcp_sink'
0:02:36.037568162  1187   0x7f64000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:02:36.037714337  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f5400c830> adding pad 'src'
0:02:36.037739712  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f5400c830> adding pad 'sink'
0:02:36.037926612  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:02:36.037953712  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:02:36.037975762  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:02:36.038046737  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:02:36.038101862  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:02:36.038127762  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:02:36.038202362  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:02:36.038234237  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:02:36.038253662  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.038301937  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:02:36.038350412  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:02:36.038374737  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:02:36.038393337  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.038417587  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:02:36.038446112  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:02:36.038515237  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:02:36.038535237  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.038554937  1187   0x7f64000d80 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:02:36.038585637  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:02:36.038855837  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:02:36.038951037  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:02:36.038977837  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:02:36.038996987  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.039041312  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:02:36.039121512  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:02:36.039166337  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:02:36.039197537  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:02:36.039218412  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:02:36.039245112  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:02:36.039267437  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:02:36.039286237  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.039349987  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:02:36.039374687  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:02:36.039392462  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.039418612  1187   0x7f64000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:02:36.039483512  1187   0x7f64000d80 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f58041a60> successfully joined bin
0:02:36.039518112  1187   0x7f64000d80 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f5803fbb0
0:02:36.039538462  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f5803fbb0 set blocked 1
0:02:36.039555087  1187   0x7f64000d80 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f58041a60> blocked: 1
0:02:36.039577137  1187   0x7f64000d80 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f5803fbb0
0:02:36.039595262  1187   0x7f64000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f5803fbb0
0:02:36.039636437  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:02:36.039689162  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:02:36.039714362  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:02:36.039734462  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.039774387  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:02:36.039803287  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:02:36.039826312  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:02:36.039846087  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.039873912  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:02:36.039899012  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:02:36.039921337  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:02:36.039940887  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.039972762  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:02:36.039997962  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:02:36.040014862  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040044087  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:02:36.040069687  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:02:36.040127162  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:02:36.040153062  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:02:36.040170412  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040198987  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:02:36.040221487  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:02:36.040242937  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:02:36.040262687  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040289412  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:02:36.040316412  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:02:36.040340662  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:02:36.040360237  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040388012  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:02:36.040414537  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:02:36.040436837  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:02:36.040456437  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040485062  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:02:36.040511212  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current NULL pending VOID_PENDING, desired next READY
0:02:36.040533287  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:02:36.040552587  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040586912  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:02:36.040613812  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current NULL pending VOID_PENDING, desired next READY
0:02:36.040636612  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:02:36.040656212  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040683812  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:02:36.040708437  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:02:36.040731412  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:02:36.040750762  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040779412  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:02:36.040803612  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:02:36.040823112  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.040850887  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:02:36.040877087  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:02:36.040900037  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:02:36.040924362  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:02:36.040958587  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.041006337  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.041037912  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:02:36.041059287  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.041092212  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:02:36.041119537  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.041150712  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:02:36.041173162  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.041202237  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:02:36.041228787  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.041256862  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:02:36.041277312  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.041304362  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:02:36.041332262  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:02:36.041352462  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.041380712  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:02:36.041404037  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.041447237  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.041483787  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:02:36.041504162  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.041532712  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:02:36.041559812  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.041948987  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:02:36.041974562  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.042010287  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:02:36.042038762  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.042623687  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:02:36.042658187  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.042697812  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:02:36.042743887  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.042781487  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:02:36.042803512  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.042834087  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:02:36.042862187  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.042894562  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:02:36.042914762  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.042943637  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:02:36.042971812  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.043028387  1187   0x7f64000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f5800d820 on task 0x7f5405ec80
0:02:36.043053237  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue0:src> created task 0x7f5405ec80
0:02:36.043228062  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:02:36.043254262  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.043287137  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:02:36.043313437  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:02:36.043341387  1187   0x7f64000d80 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:02:36.043404687  1187   0x7f64000d80 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:02:36.043445537  1187   0x7f64000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f580079f0 on task 0x7f5405f260
0:02:36.043465562  1187   0x7f64000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f5405f260
0:02:36.043556087  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:02:36.043578462  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.043610337  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:02:36.043638112  1187   0x7f64000ff0 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:02:36.043644337  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:02:36.043679237  1187   0x7f64000ff0 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:02:36.043690962  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.043758412  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:02:36.043788837  1187   0x7f64000d80 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:02:36.043810012  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:02:36.043854512  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.043882412  1187   0x7f64000d80 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f5803fbb0
0:02:36.043902537  1187   0x7f64000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f5803fbb0
0:02:36.043982587  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:02:36.044015612  1187   0x7f64000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:02:36.044059187  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.044099037  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.044122712  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:02:36.044142562  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.044172237  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:02:36.044200512  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.044223562  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:02:36.044244562  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.044273862  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:02:36.044300487  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.044408687  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:02:36.044476537  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.044511987  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:02:36.044537012  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:02:36.044557237  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.044588037  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:02:36.044638537  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.044664512  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:02:36.044684862  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.044714537  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:02:36.044742312  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.044765187  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:02:36.044784787  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.044812037  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:02:36.044838687  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.044862462  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:02:36.044881612  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.044909637  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:02:36.044936412  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.044959362  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:02:36.044979062  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.045010787  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:02:36.045038237  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.045061637  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PLAYING
0:02:36.045078737  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.045122487  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:02:36.045150512  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.045172637  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PLAYING
0:02:36.045192537  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.045221512  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:02:36.045253812  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:02:36.045276937  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.045305000  1187   0x7f64000ff0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:02:36.045308512  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:02:36.045358487  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:02:36.045378787  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.045407562  1187   0x7f64000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:02:36.045428589  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:02:36.045435287  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:02:36.045460634  1187   0x7f64000ff0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:02:36.045473512  1187   0x7f64000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.045500614  1187   0x7f64000ff0 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 12:11:24.085] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 12:11:24.085] [INFO] appsrc: 0x7f58007680, unused: 4096, user_data: 0x5589b94e20
[2021-01-01 12:11:24.085] [INFO] Calling feed_data...
[2021-01-01 12:11:24.085] [INFO] === FEED DATA CALLED ===
[2021-01-01 12:11:24.085] [INFO] appsrc: 0x7f58007680
[2021-01-01 12:11:24.085] [INFO] DDS reader is initialized
[2021-01-01 12:11:24.085] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 12:11:24.085] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 12:11:24.085] [INFO] Creating GstBuffer for raw frame data...
0:02:36.045701262  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5803fbb0: went from NULL to READY (pending PAUSED)
0:02:36.045915287  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5803fbb0: went from READY to PAUSED (pending VOID_PENDING)
0:02:36.045948612  1187   0x7f64000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f5803fbb0: got message type 2048 (new-clock)
[2021-01-01 12:11:24.086] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 12:11:24.086] [INFO] Pushing raw buffer directly to appsrc...
0:02:36.046111112  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5803fbb0: went from PAUSED to PLAYING (pending VOID_PENDING)
0:02:36.046121346  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f500025f0
0:02:36.046179478  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 12:11:24.086] [INFO] Raw frame fed to appsrc successfully, total frames served: 2
[2021-01-01 12:11:24.086] [INFO] feed_data call completed
0:02:36.046224930  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 12:11:24.086] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 12:11:24.086] [INFO] appsrc: 0x7f58007680, unused: 4096, user_data: 0x5589b94e20
[2021-01-01 12:11:24.086] [INFO] Calling feed_data...
[2021-01-01 12:11:24.086] [INFO] === FEED DATA CALLED ===
[2021-01-01 12:11:24.086] [INFO] appsrc: 0x7f58007680
[2021-01-01 12:11:24.086] [INFO] DDS reader is initialized
[2021-01-01 12:11:24.086] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 12:11:24.086] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 12:11:24.086] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 12:11:24.087] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 12:11:24.087] [INFO] Pushing raw buffer directly to appsrc...
0:02:36.046866333  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f5012f6f0
0:02:36.046885524  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 12:11:24.087] [INFO] Raw frame fed to appsrc successfully, total frames served: 3
[2021-01-01 12:11:24.087] [INFO] feed_data call completed
0:02:36.046946696  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 12:11:24.087] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 12:11:24.087] [INFO] appsrc: 0x7f58007680, unused: 4096, user_data: 0x5589b94e20
[2021-01-01 12:11:24.087] [INFO] Calling feed_data...
[2021-01-01 12:11:24.087] [INFO] === FEED DATA CALLED ===
[2021-01-01 12:11:24.087] [INFO] appsrc: 0x7f58007680
[2021-01-01 12:11:24.087] [INFO] DDS reader is initialized
[2021-01-01 12:11:24.087] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 12:11:24.087] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 12:11:24.087] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 12:11:24.087] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 12:11:24.087] [INFO] Pushing raw buffer directly to appsrc...
0:02:36.047568034  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f501c6440
0:02:36.047586736  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 12:11:24.087] [INFO] Raw frame fed to appsrc successfully, total frames served: 4
0:02:36.047608514  1187   0x7f64000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:02:36.047863661  1187   0x7f64000de0 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:02:36.047893473  1187   0x7f64000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video[2021-01-01 12:11:24.088] [INFO] feed_data call completed
/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:02:36.047917629  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 12:11:24.088] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 12:11:24.088] [INFO] appsrc: 0x7f58007680, unused: 4096, user_data: 0x5589b94e20
[2021-01-01 12:11:24.088] [INFO] Calling feed_data...
[2021-01-01 12:11:24.088] [INFO] === FEED DATA CALLED ===
[2021-01-01 12:11:24.088] [INFO] appsrc: 0x7f58007680
[2021-01-01 12:11:24.088] [INFO] DDS reader is initialized
[2021-01-01 12:11:24.088] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 12:11:24.088] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 12:11:24.088] [INFO] Creating GstBuffer for raw frame data...
0:02:36.048164236  1187   0x7f64000de0 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applying YUY2 1280x720 (2560x720)
[2021-01-01 12:11:24.088] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 12:11:24.088] [INFO] Pushing raw buffer directly to appsrc...
0:02:36.048640775  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f5025d070
0:02:36.048660479  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 12:11:24.088] [INFO] Raw frame fed to appsrc successfully, total frames served: 5
[2021-01-01 12:11:24.088] [INFO] feed_data call completed
0:02:36.048707518  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 12:11:24.089] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 12:11:24.089] [INFO] appsrc: 0x7f58007680, unused: 4096, user_data: 0x5589b94e20
[2021-01-01 12:11:24.089] [INFO] Calling feed_data...
[2021-01-01 12:11:24.089] [INFO] === FEED DATA CALLED ===
[2021-01-01 12:11:24.089] [INFO] appsrc: 0x7f58007680
[2021-01-01 12:11:24.089] [INFO] DDS reader is initialized
[2021-01-01 12:11:24.089] [INFO] Attempting to read DDS frame with 100ms timeout...
0:02:36.050331684  1187   0x7f64000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
0:02:36.069023793  1187   0x7f64000de0 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f5801c3c0 on task 0x7f4c036b50
0:02:36.069054078  1187   0x7f64000de0 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f4c036b50
[2021-01-01 12:11:24.111] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 12:11:24.111] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 12:11:24.111] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 12:11:24.111] [INFO] Pushing raw buffer directly to appsrc...
0:02:36.071747025  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f5025d190
0:02:36.071769690  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 12:11:24.112] [INFO] Raw frame fed to appsrc successfully, total frames served: 6
[2021-01-01 12:11:24.112] [INFO] feed_data call completed
0:02:36.071817427  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 12:11:24.112] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 12:11:24.112] [INFO] appsrc: 0x7f58007680, unused: 4096, user_data: 0x5589b94e20
[2021-01-01 12:11:24.112] [INFO] Calling feed_data...
[2021-01-01 12:11:24.112] [INFO] === FEED DATA CALLED ===
[2021-01-01 12:11:24.112] [INFO] appsrc: 0x7f58007680
[2021-01-01 12:11:24.112] [INFO] DDS reader is initialized
[2021-01-01 12:11:24.112] [INFO] Attempting to read DDS frame with 100ms timeout...
0:02:36.072706838  1187   0x7f64001200 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:02:36.074418937  1187   0x7f64001200 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse0> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:02:36.074539638  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:02:36.074584263  1187   0x7f64000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:02:36.074584738  1187   0x7f64001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:02:36.074855786  1187   0x7f64001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)118687287, timestamp-offset=(uint)863402931, seqnum-offset=(uint)12590, a-framerate=(string)30
0:02:36.074902937  1187   0x7f64001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:02:36.074933400  1187   0x7f64001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:02:36.074959900  1187   0x7f64001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:02:36.075058467  1187   0x7f64001200 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f58041a60: new sender source 0x7f3c009db0
0:02:36.075122312  1187   0x7f64001200 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:02:36.075165910  1187   0x7f64001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:02:36.075202093  1187   0x7f64001200 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f58041a60 received caps 0x7f3c0059d0, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)118687287, timestamp-offset=(uint)863402931, seqnum-offset=(uint)12590, a-framerate=(string)30
0:02:36.075433832  1187   0x7f64001200 INFO               videometa gstvideometa.c:1100:gst_video_time_code_meta_api_get_type: registering
0:02:36.075528288  1187   0x7f64001200 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:02:36.075584559  1187   0x7f64001200 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin0:send_rtp_src_0> Now blocking
0:02:36.075604165  1187   0x7f64001200 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f58041a60> position: 447084:11:23.929911000
0:02:36.075682913  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f5803fbb0> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:02:36.075706063  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:02:36.075719588  1187   0x7f64000d80 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:02:36.075733063  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:02:36.075762352  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:02:36.075779728  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f5803fbb0 is prerolled
0:02:36.075807318  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:02:36.075859018  1187   0x7f64000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:02:36.075924389  1187   0x7f64000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:02:36.075937366  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f5803fbb0 no suspend
0:02:36.075947239  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:02:36.075965704  1187   0x7f64000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://************:8554/stream/
0:02:36.080120391  1187   0x7f64000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f6c006630: received a request SETUP rtsp://************:8554/stream/stream=0 1.0
0:02:36.080147738  1187   0x7f64000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x5589ccbbe0 for path /stream/stream=0
0:02:36.080165423  1187   0x7f64000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f5803fbb0 for path /stream
0:02:36.080176696  1187   0x7f64000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:02:36.080187295  1187   0x7f64000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f5803fbb0 was not prepared
0:02:36.080264291  1187   0x7f64000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f58041ee0
0:02:36.080281768  1187   0x7f64000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f58041ee0
0:02:36.080312972  1187   0x7f64000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=60186-60187
0:02:36.080325858  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:02:36.080334184  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:02:36.080363033  1187   0x7f64000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f5803fbb0 in session 0x7f5804b990
0:02:36.080376778  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:1866:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f58041a60> GST_RTSP_LOWER_TRANS_UDP, ipv4
0:02:36.080475686  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f58041a60> allocated address: 0.0.0.0 and ports: 40550, 40551
0:02:36.080489192  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:1880:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f58041a60> GST_RTSP_LOWER_TRANS_UDP, ipv6
0:02:36.080609570  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f58041a60> allocated address: :: and ports: 54284, 54285
0:02:36.083125987  1187   0x7f64000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f6c006630: received a request PLAY rtsp://************:8554/stream/ 1.0
0:02:36.083169280  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f5803fbb0> complete pipeline
0:02:36.083183157  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f58041a60> complete stream
0:02:36.083193546  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f58041a60> create receiver part
0:02:36.083215965  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f58041a60> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:02:36.083232602  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:02:36.083315649  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f5804d380> adding pad 'src'
0:02:36.083352413  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:02:36.083376738  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:02:36.083416534  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:02:36.083427582  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.083439328  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:02:36.083456593  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3778:create_receiver_part:<GstRTSPStream@0x7f58041a60> udp IPv4, create and configure udpsources
0:02:36.085320468  1187   0x7f64000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:02:36.085347121  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:02:36.085547973  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f580501d0> adding pad 'src'
0:02:36.085625259  1187   0x7f64000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:02:36.085652832  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:02:36.085667504  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.085694282  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:02:36.085727383  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:02:36.085763211  1187   0x7f64000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:02:36.085788094  1187   0x7f64000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f580505d0 on task 0x7f58050f00
0:02:36.085800842  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f58050f00
0:02:36.085967584  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:02:36.085984906  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:02:36.086009056  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:02:36.086024080  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:02:36.086037038  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.086072788  1187   0x7f64001410 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:02:36.086123830  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:02:36.086170388  1187   0x7f64001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:02:36.086174552  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:02:36.086206538  1187   0x7f64001410 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:02:36.086243113  1187   0x7f64001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:02:36.086271233  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:02:36.086283732  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.086309057  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3802:create_receiver_part:<GstRTSPStream@0x7f58041a60> udp IPv6, create and configure udpsources
0:02:36.086324259  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:02:36.086350824  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f58051a10> adding pad 'src'
0:02:36.086420791  1187   0x7f64000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:02:36.086443488  1187   0x7f64001410 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:02:36.086451056  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:02:36.086499251  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:02:36.086524568  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:02:36.086549988  1187   0x7f64001410 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:02:36.086556353  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:02:36.086602859  1187   0x7f64000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:02:36.086627617  1187   0x7f64000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f58051e10 on task 0x7f580526e0
0:02:36.086640166  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f580526e0
0:02:36.086774491  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:02:36.086790026  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:02:36.086813727  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:02:36.086828730  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:02:36.086841267  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.086888681  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:02:36.086909588  1187   0x7f64001620 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:02:36.086944574  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:02:36.086985038  1187   0x7f64001620 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:02:36.087016850  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:02:36.087031554  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.087019663  1187   0x7f64001620 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:02:36.087060990  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:02:36.087075503  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:02:36.087099143  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:02:36.087122241  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:02:36.087136540  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:02:36.087159921  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:02:36.087171111  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:02:36.087185361  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.087205954  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f58041a60> create sender part
0:02:36.087222689  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f58041a60> tcp: 0, udp: 1, mcast: 0 (ttl: 0)
0:02:36.087247779  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:02:36.087346786  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f580538d0> adding pad 'sink'
0:02:36.087391072  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:02:36.087571336  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f58055e90> adding pad 'sink'
0:02:36.087610886  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f58041a60> udp IPv4, configure udpsinks
0:02:36.087625754  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f58041a60> udp IPv6, configure udpsinks
0:02:36.087641898  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f58041a60> plug udp sink
0:02:36.087663291  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f58041a60> creating first stream
0:02:36.087700204  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:02:36.087715824  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:02:36.087736569  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:02:36.087753091  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:02:36.087775119  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:02:36.087785067  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.087795406  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:02:36.087833362  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:02:36.087846622  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:02:36.087869090  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:02:36.087884839  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:02:36.087914372  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:02:36.087924694  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5803fbb0: went from PAUSED to PAUSED (pending PAUSED)
0:02:36.087927822  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:02:36.087963446  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:02:36.087979928  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:02:36.087991449  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.088006663  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:02:36.088024096  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:02:36.088079712  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:02:36.088090204  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.088134516  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:02:36.088169627  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f580585f0> adding pad 'sink'
0:02:36.088202340  1187   0x7f64000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:02:36.088226341  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f58058e20> adding pad 'sink'
0:02:36.088264357  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f58041a60> udp IPv4, configure udpsinks
0:02:36.088278993  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f58041a60> udp IPv6, configure udpsinks
0:02:36.088293697  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f58041a60> plug udp sink
0:02:36.088312877  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f58041a60> creating first stream
0:02:36.088337696  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:02:36.088351161  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:02:36.088368226  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:02:36.088383728  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:02:36.088403813  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:02:36.088413509  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.088423753  1187   0x7f64000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:02:36.088453931  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:02:36.088466647  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:02:36.088481662  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:02:36.088500130  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:02:36.088512612  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.088529655  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:02:36.088541221  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:02:36.088555529  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:02:36.088570646  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:02:36.088582859  1187   0x7f64000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:02:36.088597159  1187   0x7f64000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:02:36.088613112  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:02:36.088641559  1187   0x7f64000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:02:36.088651485  1187   0x7f64000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:02:36.088670900  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f58041a60> pipeline successfully updated
0:02:36.088685124  1187   0x7f64000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:60186-60187
0:02:36.088772901  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:02:36.088785555  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:02:36.088813703  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f58041a60> Enabling rate control
0:02:36.088844191  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:02:36.088882010  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:02:36.088920944  1187   0x7f64000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:02:36.089000135  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f5803fbb0> seekable:0
0:02:36.089015404  1187   0x7f64000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f5803fbb0> Handle going back to 0 for none live not seekable streams.
0:02:36.089027964  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:02:36.089040267  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:02:36.089051320  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:02:36.089078372  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:02:36.089155549  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f5803fbb0, target state PAUSED
0:02:36.089169544  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:02:36.089181552  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f5803fbb0 set blocked 0
0:02:36.089193748  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f58041a60> blocked: 0
0:02:36.089211446  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f5803fbb0 do_state 1
0:02:36.089222797  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f5803fbb0
0:02:36.089234018  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f5803fbb0
0:02:36.089246141  1187   0x7f64000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f5803fbb0 set blocked 0
0:02:36.089256814  1187   0x7f64000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f58041a60> blocked: 0
0:02:36.089268536  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f5803fbb0
0:02:36.089281421  1187   0x7f64000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:02:36.089305694  1187   0x7f64000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f5803fbb0: got message type 16 (tag)
0:02:36.089307225  1187   0x7f64001200 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:02:36.089341603  1187   0x7f64001200 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:02:36.089354193  1187   0x7f64001200 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:02:36.089384716  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5803fbb0: went from PAUSED to PAUSED (pending PLAYING)
0:02:36.089411881  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f5803fbb0> got async-done
0:02:36.089627988  1187   0x7f64001830 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:02:36.089695197  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:02:36.089812788  1187   0x7f64001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:02:36.089929788  1187   0x7f64001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:02:36.089992863  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.090021213  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:02:36.090044088  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.090086813  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:02:36.090114963  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090137263  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:02:36.090157313  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:02:36.090183638  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.090206538  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:02:36.090229163  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.090258663  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:02:36.090284238  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:02:36.090307438  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:02:36.090324988  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.090351163  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:02:36.090379438  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090419538  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090441838  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:02:36.090461338  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:02:36.090483713  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090500413  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:02:36.090517463  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:02:36.090538388  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090553988  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:02:36.090570688  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:02:36.090589838  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:02:36.090609788  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:02:36.090628913  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090664463  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090680388  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:02:36.090696738  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:02:36.090731863  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090749663  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:02:36.090766838  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:02:36.090787463  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090801488  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:02:36.090818163  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:02:36.090838238  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090854888  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:02:36.090871588  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:02:36.090892238  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090907938  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:02:36.090923988  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:02:36.090945713  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.090961463  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:02:36.090977813  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:02:36.090999113  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.091014713  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:02:36.091030788  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:02:36.091049138  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:02:36.091066738  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:02:36.091089388  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:02:36.091104713  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:02:36.091121163  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:02:36.091142013  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:02:36.091161413  1187   0x7f64001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:02:36.091182913  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:02:36.091199763  1187   0x7f64001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:02:36.091286515  1187   0x7f64000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:02:36.091357648  1187   0x7f64000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f5803fbb0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 12:11:24.145] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 12:11:24.145] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 12:11:24.145] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 12:11:24.145] [INFO] Pushing raw buffer directly to appsrc...
0:02:36.105085026  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f502f3650
0:02:36.105108064  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 12:11:24.145] [INFO] Raw frame fed to appsrc successfully, total frames served: 7
[2021-01-01 12:11:24.145] [INFO] feed_data call completed
0:02:36.105154341  1187   0x7f64000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:02:37.106793063  1187   0x7f5405fca0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:02:37.106942588  1187   0x7f5405fca0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:02:37.107166297  1187   0x7f64000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:02:37.107228663  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297766746907043, sr-rtptime=(uint)4074027361, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:02:37.107240921  1187   0x7f64000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:02:37.963390746  1187   0x7f64001410 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f58041a60: new source 0x7f40013770
0:02:37.963525738  1187   0x7f64001410 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2084798165, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:60187, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:02:37.963570089  1187   0x7f64001410 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:60187 in 1 transports
0:02:37.963584307  1187   0x7f64001410 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f58041a60: found transport 0x7f5804c600 for source  0x7f40013770
0:02:37.963600608  1187   0x7f64001410 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f58041a60: source 0x7f40013770 for transport 0x7f5804c600
0:02:37.963622632  1187   0x7f64001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f58041a60: source 0x7f40013770 in transport 0x7f5804c600 is active
0:02:37.963635407  1187   0x7f64001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f58041ee0 alive
0:02:37.963689565  1187   0x7f64001410 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f58041a60: new SDES 0x7f40013770
[2021-01-01 12:11:28.304] [INFO] === RTSP Server Statistics ===
[2021-01-01 12:11:28.304] [INFO] Uptime: 160.0 seconds
[2021-01-01 12:11:28.304] [INFO] Total connections: 1
[2021-01-01 12:11:28.304] [INFO] Active connections: 1
[2021-01-01 12:11:28.304] [INFO] Frames served: 7
[2021-01-01 12:11:28.304] [INFO] Clients connected: 0
[2021-01-01 12:11:28.304] [INFO] Error count: 0
0:02:42.277607637  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297788954804247, sr-rtptime=(uint)4074492722, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:02:46.811979537  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297788954804247, sr-rtptime=(uint)4074492722, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:02:49.814749138  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297788954804247, sr-rtptime=(uint)4074492722, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 12:11:38.304] [INFO] === RTSP Server Statistics ===
[2021-01-01 12:11:38.304] [INFO] Uptime: 170.0 seconds
[2021-01-01 12:11:38.304] [INFO] Total connections: 1
[2021-01-01 12:11:38.305] [INFO] Active connections: 1
[2021-01-01 12:11:38.305] [INFO] Frames served: 7
[2021-01-01 12:11:38.305] [INFO] Clients connected: 0
[2021-01-01 12:11:38.305] [INFO] Error count: 0
0:02:52.454913563  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297788954804247, sr-rtptime=(uint)4074492722, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:02:56.878704588  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297788954804247, sr-rtptime=(uint)4074492722, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:02:59.285694314  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297788954804247, sr-rtptime=(uint)4074492722, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 12:11:48.305] [INFO] === RTSP Server Statistics ===
[2021-01-01 12:11:48.305] [INFO] Uptime: 180.0 seconds
[2021-01-01 12:11:48.305] [INFO] Total connections: 1
[2021-01-01 12:11:48.305] [INFO] Active connections: 1
[2021-01-01 12:11:48.305] [INFO] Frames served: 7
[2021-01-01 12:11:48.305] [INFO] Clients connected: 0
[2021-01-01 12:11:48.305] [INFO] Error count: 0
0:03:04.572668839  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2509:on_timeout: 0x7f58041a60: source 0x7f40013770 timeout
0:03:04.977552714  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297788954804247, sr-rtptime=(uint)4074492722, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:03:08.401042589  1187   0x7f5405fca0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)118687287, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12590, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400297788954804247, sr-rtptime=(uint)4074492722, sr-octet-count=(uint)25, sr-packet-count=(uint)1;


rtsp_server_pipe --gst-debug=3 -p 8556 -u /stream -i "( v4l2src device=/dev/video0 ! video/x-raw,format=YUY2,width=640,height=480,framerate=30/1 ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )"