root@rk3576-buildroot:/# rtsp_server --hw-encoder --gst-debug 4
[2021-01-01 14:16:25.750] [INFO] === RTSP Server Configuration ===
[2021-01-01 14:16:25.750] [INFO] DDS Topic: Video_Frames
[2021-01-01 14:16:25.750] [INFO] Server: 0.0.0.0:8554/stream
[2021-01-01 14:16:25.750] [INFO] Output: 1280x720@30fps, H264, 2000000 bps
[2021-01-01 14:16:25.750] [INFO] GOP Size: 15
[2021-01-01 14:16:25.750] [INFO] Hardware Encoder: yes
[2021-01-01 14:16:25.750] [INFO] Max Clients: 10
[2021-01-01 14:16:25.750] [INFO] Buffer Size: 5
[2021-01-01 14:16:25.750] [INFO] Zero Copy: yes
[2021-01-01 14:16:25.750] [INFO] GStreamer Debug Level: 4
[2021-01-01 14:16:25.750] [INFO] Adaptive Bitrate: 500000 - 5000000 bps
[2021-01-01 14:16:25.750] [INFO] GStreamer debug level set to: 4 (*:4)
0:00:00.000077831  1564   0x55995478f0 INFO                GST_INIT gst.c:576:init_pre: Initializing GStreamer Core Library version 1.22.9
0:00:00.000094720  1564   0x55995478f0 INFO                GST_INIT gst.c:577:init_pre: Using library installed in /lib
0:00:00.000107441  1564   0x55995478f0 INFO                GST_INIT gst.c:595:init_pre: Linux rk3576-buildroot 6.1.99-rk3576 #1 SMP Mon Jun 30 10:03:13 CST 2025 aarch64
0:00:00.000327736  1564   0x55995478f0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.000840692  1564   0x55995478f0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001063916  1564   0x55995478f0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001166599  1564   0x55995478f0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001181975  1564   0x55995478f0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001247279  1564   0x55995478f0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007271139  1564   0x55995478f0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.005984 seconds
0:00:00.007337743  1564   0x55995478f0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007948610  1564   0x55995478f0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.007968864  1564   0x55995478f0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.007981931  1564   0x55995478f0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.007991410  1564   0x55995478f0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.007998361  1564   0x55995478f0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
[2021-01-01 14:16:25.758] [INFO] GStreamer initialized successfully
[2021-01-01 14:16:25.758] [INFO] Media factory configured: shared=FALSE, eos_shutdown=TRUE
[2021-01-01 14:16:25.758] [INFO] Supported protocols: UDP, UDP_MCAST, TCP
Start init DDS reader: Video_Frames
Create share memery qos success
Create participant success
Register type success
Create subscriber success
Create topic success
DDS Reader initialized for topic: Video_Frames[2021-01-01 14:16:25.764] [INFO] Waiting for first frame from DDS topic: Video_Frames
Subscriber matched
[2021-01-01 14:16:25.964] [INFO] First frame received: 640x480 format=1448695129, output will be: 1280x720@30fps
[2021-01-01 14:16:25.964] [INFO] RTSPMediaFactory initialized for topic: Video_Frames
[2021-01-01 14:16:25.964] [INFO] Pipeline: ( appsrc name=source is-live=true do-timestamp=false format=time caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )
0:00:00.214331534  1564   0x55995478f0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x5599627460 for path /stream

(<unknown>:1564): GLib-GObject-CRITICAL **: 14:16:25.964: g_object_set_is_valid_property: object class 'GstRTSPServer' has no property named 'timeout'
[2021-01-01 14:16:25.964] [INFO] RTSP server initialized: 0.0.0.0:8554/stream
0:00:00.214451146  1564   0x55995478f0 DEBUG             rtspserver rtsp-server.c:882:gst_rtsp_server_create_socket:<GstRTSPServer@0x5599625050> getting address info of 0.0.0.0/8554
0:00:00.214874039  1564   0x55995478f0 DEBUG             rtspserver rtsp-server.c:967:gst_rtsp_server_create_socket:<GstRTSPServer@0x5599625050> opened sending server socket
0:00:00.214901548  1564   0x55995478f0 DEBUG             rtspserver rtsp-server.c:994:gst_rtsp_server_create_socket:<GstRTSPServer@0x5599625050> listening on server socket 0x5599777150 with queue of 5
[2021-01-01 14:16:25.965] [INFO] RTSP server started on 0.0.0.0:8554/stream
[2021-01-01 14:16:25.965] [INFO] RTSP server is running. Access stream at: rtsp://0.0.0.0:8554/stream
[2021-01-01 14:16:25.965] [INFO] Press Ctrl+C to stop the server
[2021-01-01 14:16:25.965] [INFO] RTSP server main loop started
0:00:07.591071203  1564   0x7f84000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f84006630 connected to server ip ***********5, ipv6 = 0
0:00:07.591100855  1564   0x7f84000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f84006630 ip ***********:54340
0:00:07.591119174  1564   0x7f84000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x5599625050> manage client 0x7f84006630
[2021-01-01 14:16:33.341] [INFO] === CLIENT CONNECTED ===
[2021-01-01 14:16:33.341] [INFO] Active connections: 1, Total connections: 1
0:00:07.591591742  1564   0x7f7c000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f84006f10
0:00:07.591599433  1564   0x7f84000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f84006630: attaching to context 0x7f84007440
0:00:07.602573792  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request OPTIONS rtsp://***********5:8554/stream 1.0
0:00:07.604498467  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request DESCRIBE rtsp://***********5:8554/stream 1.0
0:00:07.604550217  1564   0x7f7c000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x5599627460 for path /stream
0:00:07.604574992  1564   0x7f7c000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=false format=time caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:07.605364492  1564   0x7f7c000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstapp.so" loaded
0:00:07.605775742  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:07.605834992  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78007760> adding pad 'src'
0:00:07.605891717  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:07.607640567  1564   0x7f7c000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:07.607871742  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:07.607946742  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7800d140> adding pad 'sink'
0:00:07.607990492  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7800d140> adding pad 'src'
0:00:07.609010042  1564   0x7f7c000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoconvertscale.so" loaded
0:00:07.609567967  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:07.609625492  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f780179a0> adding pad 'sink'
0:00:07.609661167  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f780179a0> adding pad 'src'
0:00:07.612893042  1564   0x7f7c000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:07.613269092  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:07.613325842  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f7801c3e0> adding pad 'sink'
0:00:07.613360542  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f7801c3e0> adding pad 'src'
0:00:07.615467567  1564   0x7f7c000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:00:07.615677692  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:07.615731592  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78024b00> adding pad 'sink'
0:00:07.615764717  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78024b00> adding pad 'src'
0:00:07.615810592  1564   0x7f7c000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f78024b00> PTS interpolation: no
0:00:07.615830667  1564   0x7f7c000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f78024b00> TS inferring: no
0:00:07.618528242  1564   0x7f7c000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:07.618851517  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:07.618923742  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78034920> adding pad 'src'
0:00:07.618994467  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78034920> adding pad 'sink'
0:00:07.619061417  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:07.619213617  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue0 (0/0) with caps "(NULL)"
0:00:07.619240942  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue0:(any)
0:00:07.619266117  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue0:sink
0:00:07.619295092  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:07.619324217  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:07.619349292  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue0 in same bin, no need for ghost pads
0:00:07.619390242  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue0:sink
0:00:07.619409517  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:07.619429592  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:07.619453892  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue0:sink, successful
0:00:07.619467717  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.619483342  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:07.619530467  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue0 to some pad of GstVideoScale named videoscale0 (0/0) with caps "(NULL)"
0:00:07.619552042  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue0:(any) to element videoscale0:(any)
0:00:07.619575242  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue0:src and videoscale0:sink
0:00:07.619596092  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:07.619620842  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:07.620298692  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue0 and videoscale0 in same bin, no need for ghost pads
0:00:07.620327342  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue0:src and videoscale0:sink
0:00:07.620349542  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:07.620372667  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:07.620981467  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue0:src and videoscale0:sink, successful
0:00:07.620997942  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.621013442  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue0:src> Received event on flushing pad. Discarding
0:00:07.621065717  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:07.621093943  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:07.621234693  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f780396b0> adding pad 'sink'
0:00:07.621268993  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f780396b0> adding pad 'src'
0:00:07.621296668  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:07.621345618  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:07.621369118  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.621395368  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale0:(any) to element capsfilter0:sink
0:00:07.621413093  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:07.621430668  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale0 and capsfilter0 in same bin, no need for ghost pads
0:00:07.621456993  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale0:src and capsfilter0:sink
0:00:07.621488518  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:07.622123468  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:07.622189793  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale0:src and capsfilter0:sink, successful
0:00:07.622203968  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.622218243  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale0:src> Received event on flushing pad. Discarding
0:00:07.622248693  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mpph264enc0:(any)
0:00:07.622265393  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:07.622285518  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mpph264enc0:sink
0:00:07.622312618  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:07.623078843  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:07.623172743  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mpph264enc0 in same bin, no need for ghost pads
0:00:07.623206318  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mpph264enc0:sink
0:00:07.623239068  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:07.623987668  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:07.624108168  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mpph264enc0:sink, successful
0:00:07.624124118  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.624139368  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:07.624199218  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:00:07.624225443  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:00:07.624251168  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:00:07.624280593  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:07.624310643  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:00:07.624337843  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:00:07.624361343  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:07.624390768  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:00:07.624404768  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.624419068  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:07.624470268  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:07.624490643  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:00:07.624512143  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:00:07.624539118  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:07.624565393  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:00:07.624589468  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:00:07.624611568  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:07.624635768  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:00:07.624649418  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.624663243  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:00:07.624898118  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:07.624929118  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f78034920
0:00:07.624946368  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:07.624995443  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f7803a070: creating stream with index 0 and payloader <pay0>
0:00:07.625193418  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:07.625215818  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:07.625229743  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.625245268  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:07.625280943  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:07.625352868  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f7803ed60
0:00:07.625377068  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f7803ed60> set retransmission time 0
0:00:07.625394368  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f7803ed60> Enabling rate control
0:00:07.625428593  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f7803ed60> set retransmission pt 97
0:00:07.625450943  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:07.625483968  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:07.625511318  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:07.625537518  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:07.625563518  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:07.625598368  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:07.625754018  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f7803ed60> set retransmission time 0
[2021-01-01 14:16:33.376] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 14:16:33.376] [INFO] factory: 0x5599627460, media: 0x7f7803a070, user_data: 0x559955fe20
[2021-01-01 14:16:33.376] [INFO] Calling configure_media...
[2021-01-01 14:16:33.376] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 14:16:33.376] [INFO] Got media pipeline: 0x7f78036570
0:00:07.625892093  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element source
[2021-01-01 14:16:33.376] [INFO] Found appsrc element: 0x7f78007760
[2021-01-01 14:16:33.376] [INFO] Connected appsrc signals
0:00:07.625979118  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:07.626009893  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:07.626035218  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 14:16:33.376] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 14:16:33.376] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:07.626717218  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f7802f120
0:00:07.626770893  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 14:16:33.377] [INFO] Initial frame push result: 0
[2021-01-01 14:16:33.377] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 14:16:33.377] [INFO] Configured appsrc properties
[2021-01-01 14:16:33.377] [INFO] Pipeline latency query result: live=0, min=0 ns, max=18446744073709551615 ns
[2021-01-01 14:16:33.377] [INFO] Setting manual latency: min=33333333 ns, max=100000000 ns
0:00:07.626953118  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.033333333
0:00:07.627002818  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
[2021-01-01 14:16:33.377] [INFO] Sent latency event to pipeline
[2021-01-01 14:16:33.377] [INFO] Media configured successfully
[2021-01-01 14:16:33.377] [INFO] configure_media call completed
0:00:07.627105243  1564   0x7f7c000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f7803a070 for url /stream
0:00:07.627344918  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f7803a070
0:00:07.627365093  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:07.627452518  1564   0x7f7c000d80 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f7803adc0
0:00:07.629596268  1564   0x7f7c000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:07.629643193  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:07.630491568  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:07.630515818  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:07.630543668  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:07.630583343  1564   0x7f7c000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:07.630653543  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:07.630685218  1564   0x7f7c000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:07.630734093  1564   0x7f7c000d80 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f7803ed60 joining bin as session 0
0:00:07.630779543  1564   0x7f7c000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:07.631478618  1564   0x7f7c000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:07.631670493  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f7000c360> adding pad 'sink'
0:00:07.631712318  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f7000c360> adding pad 'rtcp_sink'
0:00:07.631744468  1564   0x7f7c000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:07.631884818  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7000d7e0> adding pad 'src'
0:00:07.631909643  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7000d7e0> adding pad 'sink'
0:00:07.632107343  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:07.632135343  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:07.632155918  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:07.632236843  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:07.632284093  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:07.632305668  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:07.632388793  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:07.632414768  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:07.632434718  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.632506793  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:07.632576093  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:07.632601693  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:07.632621168  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.632648493  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:07.632681468  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:07.632753943  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:07.632771318  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.632790843  1564   0x7f7c000d80 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:07.632822193  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:07.633085218  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:07.633188668  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:07.633216768  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:07.633233943  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.633268093  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:07.633349568  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:07.633402868  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:07.633437043  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:07.633458118  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:07.633484343  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:07.633504518  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:07.633518968  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.633579943  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:07.633603943  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:07.633622718  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.633648543  1564   0x7f7c000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:07.633719618  1564   0x7f7c000d80 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f7803ed60> successfully joined bin
0:00:07.633755543  1564   0x7f7c000d80 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f7803a070
0:00:07.633779743  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f7803a070 set blocked 1
0:00:07.633797693  1564   0x7f7c000d80 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f7803ed60> blocked: 1
0:00:07.633819368  1564   0x7f7c000d80 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f7803a070
0:00:07.633838268  1564   0x7f7c000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f7803a070
0:00:07.633881243  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:07.633931093  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:07.633955843  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:07.633978968  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634021143  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:07.634047568  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634071818  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:07.634093443  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634122043  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:07.634149243  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634172618  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:07.634194818  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634223018  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:07.634246243  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:07.634263793  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634290268  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:07.634316018  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634372168  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634397193  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:07.634414918  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634450293  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:07.634477618  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634501343  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:07.634521018  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634550118  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:07.634574293  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634598193  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:07.634615843  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634644668  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:07.634670968  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634691068  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:07.634710993  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634739418  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:07.634764218  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634786193  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:07.634805993  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634832593  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:07.634859293  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current NULL pending VOID_PENDING, desired next READY
0:00:07.634877368  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:07.634894618  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.634921243  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:07.634951618  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:07.634975093  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:07.634994568  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.635020968  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:07.635045018  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:07.635062568  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.635089468  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:07.635117593  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:07.635138643  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:07.635164843  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:07.635200894  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.635247969  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.635280494  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:07.635301169  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.635330669  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:07.635356794  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.635386269  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:07.635409119  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.635437969  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:07.635465269  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.635495244  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:07.635515244  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.635550144  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:07.635579444  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:07.635601344  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.635629494  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:07.635653444  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.635701144  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.635737619  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:07.635758294  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.635787444  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:07.635813969  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.636187044  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:07.636216319  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.636250419  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:07.636280194  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.636917594  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:07.636956944  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.636999069  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:07.637048169  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.637085869  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:07.637109019  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.637139144  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:07.637166244  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.637197094  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:07.637217794  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.637246719  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:07.637274794  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.637336994  1564   0x7f7c000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7800d980 on task 0x7f700605e0
0:00:07.637360994  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue0:src> created task 0x7f700605e0
0:00:07.637601244  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:07.637630969  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.637661494  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:07.637688544  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:07.637715244  1564   0x7f7c000d80 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:07.637775094  1564   0x7f7c000d80 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:07.637821544  1564   0x7f7c000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f78007ad0 on task 0x7f70060ca0
0:00:07.637841694  1564   0x7f7c000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f70060ca0
0:00:07.637964269  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:07.637985369  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.638011494  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:07.638021766  1564   0x7f7c000ff0 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:07.638045194  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:07.638066242  1564   0x7f7c000ff0 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:07.638077819  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.638125394  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:07.638151744  1564   0x7f7c000d80 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:07.638167469  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:07.638184044  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.638206069  1564   0x7f7c000d80 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f7803a070
0:00:07.638222544  1564   0x7f7c000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803a070
0:00:07.638326719  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:07.638357819  1564   0x7f7c000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:07.638394394  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.638428319  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.638446169  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:07.638462669  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.638489469  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:07.638513869  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.638532369  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:07.638549094  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.638573419  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:07.638594944  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.638699569  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:07.638719444  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.638745894  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:07.638768394  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:07.638785269  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.638812869  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:07.638873144  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.638893319  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:07.638910419  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.638934044  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:07.638955669  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.638973594  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:00:07.638990244  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.639013344  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:07.639035069  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.639053569  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:07.639070169  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.639093169  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:07.639114519  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.639132019  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:07.639148394  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.639171319  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:07.639192819  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.639210419  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PLAYING
0:00:07.639226519  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.639248894  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:07.639269569  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.639287044  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PLAYING
0:00:07.639305244  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.639328519  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:07.639353619  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:07.639373844  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.639412401  1564   0x7f7c000ff0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:07.639424119  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:07.639449244  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:07.639471219  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.639498894  1564   0x7f7c000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:07.639521319  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:07.639540969  1564   0x7f7c000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.639557643  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:07.639586092  1564   0x7f7c000ff0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:07.639609284  1564   0x7f7c000ff0 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 14:16:33.390] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:33.390] [INFO] appsrc: 0x7f78007760, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:33.390] [INFO] Calling feed_data...
[2021-01-01 14:16:33.390] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:33.390] [INFO] appsrc: 0x7f78007760
[2021-01-01 14:16:33.390] [INFO] DDS reader is initialized
[2021-01-01 14:16:33.390] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:33.390] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:33.390] [INFO] Creating GstBuffer for raw frame data...
0:00:07.639751294  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803a070: went from NULL to READY (pending PAUSED)
0:00:07.639969919  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803a070: went from READY to PAUSED (pending VOID_PENDING)
0:00:07.640003894  1564   0x7f7c000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a070: got message type 2048 (new-clock)
0:00:07.640171469  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803a070: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 14:16:33.390] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:33.390] [INFO] Pushing raw buffer directly to appsrc...
0:00:07.640225302  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f68002650
0:00:07.640265276  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:33.390] [INFO] Raw frame fed to appsrc successfully, total frames served: 2
[2021-01-01 14:16:33.390] [INFO] feed_data call completed
0:00:07.640315653  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:07.640335468  1564   0x7f7c000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 14:16:33.390] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:33.390] [INFO] appsrc: 0x7f78007760, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:33.390] [INFO] Calling feed_data...
[2021-01-01 14:16:33.390] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:33.390] [INFO] appsrc: 0x7f78007760
[2021-01-01 14:16:33.390] [INFO] DDS reader is initialized
[2021-01-01 14:16:33.390] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:33.390] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:33.390] [INFO] Creating GstBuffer for raw frame data...
0:00:07.640613441  1564   0x7f7c000de0 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:07.640650223  1564   0x7f7c000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:07.640915147  1564   0x7f7c000de0 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applying YUY2 1280x720 (2560x720)
[2021-01-01 14:16:33.391] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:33.391] [INFO] Pushing raw buffer directly to appsrc...
0:00:07.640972668  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f6812f880
0:00:07.640991556  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:33.391] [INFO] Raw frame fed to appsrc successfully, total frames served: 3
[2021-01-01 14:16:33.391] [INFO] feed_data call completed
0:00:07.641043699  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:33.391] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:33.391] [INFO] appsrc: 0x7f78007760, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:33.391] [INFO] Calling feed_data...
[2021-01-01 14:16:33.391] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:33.391] [INFO] appsrc: 0x7f78007760
[2021-01-01 14:16:33.391] [INFO] DDS reader is initialized
[2021-01-01 14:16:33.391] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:33.391] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:33.391] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:16:33.392] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:33.392] [INFO] Pushing raw buffer directly to appsrc...
0:00:07.641677705  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f681c6500
0:00:07.641697325  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:33.392] [INFO] Raw frame fed to appsrc successfully, total frames served: 4
[2021-01-01 14:16:33.392] [INFO] feed_data call completed
0:00:07.641744973  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:33.392] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:33.392] [INFO] appsrc: 0x7f78007760, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:33.392] [INFO] Calling feed_data...
[2021-01-01 14:16:33.392] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:33.392] [INFO] appsrc: 0x7f78007760
[2021-01-01 14:16:33.392] [INFO] DDS reader is initialized
[2021-01-01 14:16:33.392] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:33.392] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:33.392] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:16:33.392] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:33.392] [INFO] Pushing raw buffer directly to appsrc...
0:00:07.642425173  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f6825cec0
0:00:07.642446502  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:33.392] [INFO] Raw frame fed to appsrc successfully, total frames served: 5
0:00:07.642729881  1564   0x7f7c000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
[2021-01-01 14:16:33.393] [INFO] feed_data call completed
0:00:07.642846769  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:33.393] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:33.393] [INFO] appsrc: 0x7f78007760, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:33.393] [INFO] Calling feed_data...
[2021-01-01 14:16:33.393] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:33.393] [INFO] appsrc: 0x7f78007760
[2021-01-01 14:16:33.393] [INFO] DDS reader is initialized
[2021-01-01 14:16:33.393] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:33.408] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:33.408] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:16:33.408] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:33.408] [INFO] Pushing raw buffer directly to appsrc...
0:00:07.658471134  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f682f32c0
0:00:07.658492682  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:33.409] [INFO] Raw frame fed to appsrc successfully, total frames served: 6
[2021-01-01 14:16:33.409] [INFO] feed_data call completed
0:00:07.658542379  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:33.409] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:33.409] [INFO] appsrc: 0x7f78007760, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:33.409] [INFO] Calling feed_data...
[2021-01-01 14:16:33.409] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:33.409] [INFO] appsrc: 0x7f78007760
[2021-01-01 14:16:33.409] [INFO] DDS reader is initialized
[2021-01-01 14:16:33.409] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:07.661709599  1564   0x7f7c000de0 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7801c8d0 on task 0x7f74036b50
0:00:07.661730850  1564   0x7f7c000de0 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f74036b50
0:00:07.665317069  1564   0x7f7c001200 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:07.665887295  1564   0x7f7c001200 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse0> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:07.666041169  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:07.666043524  1564   0x7f7c001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:07.666110369  1564   0x7f7c000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:07.666308584  1564   0x7f7c001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2576950944, timestamp-offset=(uint)2907433890, seqnum-offset=(uint)20743, a-framerate=(string)30
0:00:07.666351498  1564   0x7f7c001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:07.666383146  1564   0x7f7c001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:07.666411504  1564   0x7f7c001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:07.666511367  1564   0x7f7c001200 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f7803ed60: new sender source 0x7f640112d0
0:00:07.666574468  1564   0x7f7c001200 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2576950944, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)20743, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:07.666619500  1564   0x7f7c001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:07.666656848  1564   0x7f7c001200 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f7803ed60 received caps 0x7f6400cef0, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2576950944, timestamp-offset=(uint)2907433890, seqnum-offset=(uint)20743, a-framerate=(string)30
0:00:07.666909293  1564   0x7f7c001200 INFO               videometa gstvideometa.c:1100:gst_video_time_code_meta_api_get_type: registering
0:00:07.667009484  1564   0x7f7c001200 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:00:07.667064961  1564   0x7f7c001200 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin0:send_rtp_src_0> Now blocking
0:00:07.667082864  1564   0x7f7c001200 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f7803ed60> position: 447086:16:33.237397000
0:00:07.667145444  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f7803a070> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:07.667175169  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:07.667188744  1564   0x7f7c000d80 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:07.667202019  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:07.667233594  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:07.667254219  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f7803a070 is prerolled
0:00:07.667289444  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:07.667365344  1564   0x7f7c000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:07.667458969  1564   0x7f7c000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:07.667478519  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f7803a070 no suspend
0:00:07.667493644  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:07.667519744  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://***********5:8554/stream/
0:00:07.670785319  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request SETUP rtsp://***********5:8554/stream/stream=0 1.0
0:00:07.670827344  1564   0x7f7c000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x5599627460 for path /stream/stream=0
0:00:07.670852419  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f7803a070 for path /stream
0:00:07.670868169  1564   0x7f7c000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:07.670884269  1564   0x7f7c000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f7803a070 was not prepared
0:00:07.671001769  1564   0x7f7c000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f78041d80
0:00:07.671026894  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f78041d80
0:00:07.671079769  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=55420-55421
0:00:07.671097469  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:07.671109544  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:07.671153019  1564   0x7f7c000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f7803a070 in session 0x7f7804b8a0
0:00:07.671173744  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:1866:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f7803ed60> GST_RTSP_LOWER_TRANS_UDP, ipv4
0:00:07.671388194  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f7803ed60> allocated address: 0.0.0.0 and ports: 45990, 45991
0:00:07.671406919  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:1880:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f7803ed60> GST_RTSP_LOWER_TRANS_UDP, ipv6
0:00:07.671540519  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f7803ed60> allocated address: :: and ports: 38600, 38601
0:00:07.674873269  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request PLAY rtsp://***********5:8554/stream/ 1.0
0:00:07.674941494  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f7803a070> complete pipeline
0:00:07.674960519  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f7803ed60> complete stream
0:00:07.674974419  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f7803ed60> create receiver part
0:00:07.675007119  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f7803ed60> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:07.675030544  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:07.675157569  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f7804d5d0> adding pad 'src'
0:00:07.675216444  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:00:07.675251594  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:07.675314544  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:00:07.675329569  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.675346544  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:00:07.675372719  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3778:create_receiver_part:<GstRTSPStream@0x7f7803ed60> udp IPv4, create and configure udpsources
0:00:07.676767469  1564   0x7f7c000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:00:07.676804769  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:07.677113044  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78050400> adding pad 'src'
0:00:07.677224094  1564   0x7f7c000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:00:07.677263044  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:00:07.677283294  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.677320019  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:07.677373494  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:07.677422344  1564   0x7f7c000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:07.677460194  1564   0x7f7c000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f78050800 on task 0x7f78050fc0
0:00:07.677480644  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f78050fc0
0:00:07.677653019  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:07.677674394  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:07.677704869  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:07.677726744  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:00:07.677745069  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.677793944  1564   0x7f7c001410 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:00:07.677865219  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:00:07.677875069  1564   0x7f7c001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:07.677928519  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:00:07.677936094  1564   0x7f7c001410 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:07.677979044  1564   0x7f7c001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:07.678013719  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:00:07.678029669  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.678065094  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3802:create_receiver_part:<GstRTSPStream@0x7f7803ed60> udp IPv6, create and configure udpsources
0:00:07.678089919  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:07.678133219  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78051be0> adding pad 'src'
0:00:07.678174819  1564   0x7f7c001410 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:07.678231269  1564   0x7f7c000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:00:07.678280619  1564   0x7f7c001410 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:00:07.678281369  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:00:07.678327369  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:07.678365994  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:07.678419069  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:07.678465344  1564   0x7f7c000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:07.678505494  1564   0x7f7c000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f78051fe0 on task 0x7f780526a0
0:00:07.678526369  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f780526a0
0:00:07.678666594  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:07.678690694  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:07.678732794  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:00:07.678759894  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:00:07.678783419  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.678792994  1564   0x7f7c001620 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:00:07.678890119  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:00:07.678890769  1564   0x7f7c001620 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:07.678951044  1564   0x7f7c001620 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:07.678955294  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:00:07.678987319  1564   0x7f7c001620 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:07.679081019  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:00:07.679103094  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.679147119  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:07.679173019  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:07.679208844  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:00:07.679245544  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:07.679269919  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:07.679304269  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:00:07.679322244  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:00:07.679343069  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.679380069  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f7803ed60> create sender part
0:00:07.679406519  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f7803ed60> tcp: 0, udp: 1, mcast: 0 (ttl: 0)
0:00:07.679449744  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:07.679612619  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f78053560> adding pad 'sink'
0:00:07.679690594  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:07.679982594  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f78055ba0> adding pad 'sink'
0:00:07.680048669  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f7803ed60> udp IPv4, configure udpsinks
0:00:07.680076344  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f7803ed60> udp IPv6, configure udpsinks
0:00:07.680103894  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f7803ed60> plug udp sink
0:00:07.680144794  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f7803ed60> creating first stream
0:00:07.680206594  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:00:07.680231544  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:00:07.680266994  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:00:07.680299769  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:00:07.680335819  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:00:07.680353744  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.680370319  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:00:07.680428044  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:07.680470969  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:07.680514669  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:00:07.680542269  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:07.680592819  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:07.680593219  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803a070: went from PAUSED to PAUSED (pending PAUSED)
0:00:07.680617544  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:00:07.680676944  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:00:07.680707919  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:07.680727119  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.680753594  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:00:07.680782269  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:00:07.680893119  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:00:07.680915394  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.681000619  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:07.681081644  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f78058380> adding pad 'sink'
0:00:07.681151219  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:07.681194444  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f78058b90> adding pad 'sink'
0:00:07.681267019  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f7803ed60> udp IPv4, configure udpsinks
0:00:07.681294394  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f7803ed60> udp IPv6, configure udpsinks
0:00:07.681321769  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f7803ed60> plug udp sink
0:00:07.681356019  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f7803ed60> creating first stream
0:00:07.681400169  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:00:07.681423794  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:00:07.681453394  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:00:07.681480444  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:00:07.681514494  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:00:07.681531919  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.681547869  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:00:07.681603469  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:07.681628694  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:07.681659194  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:00:07.681693894  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:07.681717094  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.681748569  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:07.681770944  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:07.681798544  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:00:07.681826944  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:07.681847719  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:07.681874169  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:00:07.681903594  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:07.681960719  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:00:07.681981619  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:07.682017069  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f7803ed60> pipeline successfully updated
0:00:07.682044569  1564   0x7f7c000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:55420-55421
0:00:07.682188119  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:07.682214119  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:07.682258794  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f7803ed60> Enabling rate control
0:00:07.682307419  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:07.682393569  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:07.682509469  1564   0x7f7c000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:07.682641794  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f7803a070> seekable:0
0:00:07.682662644  1564   0x7f7c000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f7803a070> Handle going back to 0 for none live not seekable streams.
0:00:07.682680269  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:07.682696869  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:07.682711019  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:07.682753094  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:07.682885394  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f7803a070, target state PAUSED
0:00:07.682905244  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:07.682923294  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f7803a070 set blocked 0
0:00:07.682940869  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f7803ed60> blocked: 0
0:00:07.682968944  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f7803a070 do_state 1
0:00:07.682985044  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f7803a070
0:00:07.683000869  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f7803a070
0:00:07.683017269  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f7803a070 set blocked 0
0:00:07.683032169  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f7803ed60> blocked: 0
0:00:07.683047794  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803a070
0:00:07.683064819  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:07.683085582  1564   0x7f7c001200 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:07.683107537  1564   0x7f7c001200 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:07.683095494  1564   0x7f7c000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803a070: got message type 16 (tag)
0:00:07.683122437  1564   0x7f7c001200 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:07.683185619  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803a070: went from PAUSED to PAUSED (pending PLAYING)
0:00:07.683221294  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f7803a070> got async-done
0:00:07.683401744  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:07.683476894  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:07.683567819  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:07.683678869  1564   0x7f7c001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:07.683740019  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.683762419  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:00:07.683813494  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.683863119  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:07.683914544  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.683931994  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:07.683957294  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:07.683980769  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.683998719  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:00:07.684041394  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.684065969  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:07.684096394  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:07.684114094  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:00:07.684130769  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.684189444  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:07.684223294  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684282894  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684300294  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:07.684317069  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:07.684362294  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684379744  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:07.684403269  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:07.684425819  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684453419  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:07.684470844  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:07.684490794  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:07.684508669  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:07.684528569  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684575244  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684591844  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:07.684618894  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:07.684640094  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684663644  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:07.684697919  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:07.684719419  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684735394  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:07.684770019  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:07.684791744  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684819769  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:07.684839769  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:07.684861369  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684895169  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:07.684923969  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:07.684948369  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.684964544  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:07.684981219  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:07.685000044  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.685015669  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:07.685032119  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:07.685049819  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:07.685075619  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:07.685099369  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:07.685115494  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:07.685131919  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:07.685151744  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:07.685171194  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:07.685201944  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:07.685240444  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:07.685369769  1564   0x7f7c000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:07.685481394  1564   0x7f7c000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803a070: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 14:16:33.442] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:33.442] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:16:33.442] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:33.442] [INFO] Pushing raw buffer directly to appsrc...
0:00:07.691844127  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f682f3620
0:00:07.691865360  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:33.442] [INFO] Raw frame fed to appsrc successfully, total frames served: 7
[2021-01-01 14:16:33.442] [INFO] feed_data call completed
0:00:07.691910688  1564   0x7f7c000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:08.947149269  1564   0x7f70061440 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:08.947306369  1564   0x7f70061440 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:08.947589294  1564   0x7f7c000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:08.947594169  1564   0x7f70061440 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2576950944, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)20743, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400330020021270550, sr-rtptime=(uint)2498950842, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:08.947706694  1564   0x7f7c000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:09.977862064  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f7803ed60: new source 0x7f580139f0
0:00:09.978008729  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1361315542, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:55421, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:09.978052788  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:55421 in 1 transports
0:00:09.978067486  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f7803ed60: found transport 0x7f7804c820 for source  0x7f580139f0
0:00:09.978083671  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f7803ed60: source 0x7f580139f0 for transport 0x7f7804c820
0:00:09.978107810  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f7803ed60: source 0x7f580139f0 in transport 0x7f7804c820 is active
0:00:09.978119547  1564   0x7f7c001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78041d80 alive
0:00:09.978173876  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f7803ed60: new SDES 0x7f580139f0
[2021-01-01 14:16:35.966] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:16:35.966] [INFO] Uptime: 10.0 seconds
[2021-01-01 14:16:35.966] [INFO] Total connections: 1
[2021-01-01 14:16:35.966] [INFO] Active connections: 1
[2021-01-01 14:16:35.966] [INFO] Frames served: 7
[2021-01-01 14:16:35.966] [INFO] Clients connected: 0
[2021-01-01 14:16:35.966] [INFO] Error count: 0
0:00:12.637031852  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f7803ed60: source 0x7f580139f0 in transport 0x7f7804c820 is active
0:00:12.637062494  1564   0x7f7c001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78041d80 alive
0:00:15.000222994  1564   0x7f70061440 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2576950944, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)20743, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400330046018428419, sr-rtptime=(uint)2499495606, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:16.930608895  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f7803ed60: source 0x7f580139f0 in transport 0x7f7804c820 is active
0:00:16.930640610  1564   0x7f7c001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78041d80 alive
0:00:18.218765174  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f7803ed60: source 0x7f580139f0 in transport 0x7f7804c820 is active
0:00:18.218795211  1564   0x7f7c001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78041d80 alive
0:00:18.218818833  1564   0x7f7c001410 INFO              rtspstream rtsp-stream.c:2488:on_bye_ssrc: 0x7f7803ed60: source 0x7f580139f0 bye
0:00:18.218966544  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request TEARDOWN rtsp://***********5:8554/stream/ 1.0
0:00:18.219040269  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f7803a070, target state PLAYING
0:00:18.219067294  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:18.219090544  1564   0x7f7c000b70 INFO              rtspstream rtsp-stream.c:4774:update_transport: removing ***********:55420-55421
0:00:18.219146144  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f7803a070 do_state 1
0:00:18.219163619  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f7803a070
0:00:18.219181669  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f7803a070
0:00:18.219199344  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:18.219214869  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:18.219232569  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803a070
0:00:18.219410969  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:18.219519044  1564   0x7f7c000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:18.219577219  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.219600669  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink1> skipping transition from PLAYING to  PLAYING
0:00:18.219624294  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:18.219650944  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.219671894  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:18.219692719  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:18.219717544  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.219737769  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee1> skipping transition from PLAYING to  PLAYING
0:00:18.219756919  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:18.219781144  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.219800019  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee0> skipping transition from PLAYING to  PLAYING
0:00:18.219819544  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:18.219846144  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.219882869  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.219902719  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:18.219923019  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:18.219948294  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.219969669  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:18.219989444  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:18.220015094  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220037169  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:18.220060569  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:18.220087694  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:18.220109719  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:18.220135594  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220179594  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220200869  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:18.220220569  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:18.220247269  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220268069  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:18.220288869  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:18.220314419  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220333569  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:18.220353669  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:18.220377294  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220398469  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:18.220417894  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:18.220453244  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220475819  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:18.220496669  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:18.220521794  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220541544  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:18.220561944  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:18.220582244  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220602869  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:18.220621969  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:18.220643694  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:18.220662969  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:18.220690419  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.220707769  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:18.220725344  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:18.220746544  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:18.220766869  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:18.220789469  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:18.220805719  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:18.220891894  1564   0x7f7c001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = flushing
0:00:18.220924344  1564   0x7f7c001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:18.220989794  1564   0x7f7c001620 INFO                    task gsttask.c:370:gst_task_func:<udpsrc1:src> Task resume from paused
0:00:18.221013655  1564   0x7f7c001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = flushing
0:00:18.221032592  1564   0x7f7c001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:18.221062988  1564   0x7f7c001410 INFO                    task gsttask.c:370:gst_task_func:<udpsrc0:src> Task resume from paused
0:00:18.221076199  1564   0x7f7c001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = eos
0:00:18.221094369  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f7805bc00, time 99:99:99.999999999, seq-num 182, (NULL)
0:00:18.221097894  1564   0x7f7c001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:18.221142444  1564   0x7f7c000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f7804b8a0
0:00:18.221166144  1564   0x7f7c000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f7803a070 was not prepared
0:00:18.221185544  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f7803a070 is already unpreparing
0:00:18.221218794  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f84006630: sending close message
0:00:18.221270844  1564   0x7f7c001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = eos
0:00:18.221341744  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f84006630: session 0x7f78041d80 removed
0:00:18.221366044  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f84006630: unwatch session 0x7f78041d80
0:00:18.221400169  1564   0x7f7c000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f78041d80
0:00:18.221416394  1564   0x7f7c001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:18.221481219  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f84006630: connection closed
0:00:18.221504919  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f84006630: watch destroyed
0:00:18.221530069  1564   0x7f7c000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x5599625050> unmanage client 0x7f84006630
0:00:18.221567594  1564   0x7f7c000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f84006cb0
0:00:18.221593394  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f84006630
0:00:18.221707844  1564   0x7f7c000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f84006f10
0:00:18.224924519  1564   0x7f84000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f84006630 connected to server ip ***********5, ipv6 = 0
0:00:18.224958619  1564   0x7f84000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f84006630 ip ***********:54356
0:00:18.224986844  1564   0x7f84000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x5599625050> manage client 0x7f84006630
[2021-01-01 14:16:43.975] [INFO] === CLIENT CONNECTED ===
[2021-01-01 14:16:43.975] [INFO] Active connections: 2, Total connections: 2
0:00:18.225148144  1564   0x7f7c000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f84008020
0:00:18.225164819  1564   0x7f84000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f84006630: attaching to context 0x7f84008140
0:00:18.234836594  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request OPTIONS rtsp://***********5:8554/stream 1.0
0:00:18.236810044  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request DESCRIBE rtsp://***********5:8554/stream 1.0
0:00:18.236868444  1564   0x7f7c000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x5599627460 for path /stream
0:00:18.236899044  1564   0x7f7c000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=false format=time caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:18.237041219  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:18.237118694  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f7805c8d0> adding pad 'src'
0:00:18.237178344  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:18.237245269  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:18.237305469  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7805d0f0> adding pad 'sink'
0:00:18.237352619  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7805d0f0> adding pad 'src'
0:00:18.237404844  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:18.237447094  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7805f6c0> adding pad 'sink'
0:00:18.237483519  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7805f6c0> adding pad 'src'
0:00:18.237570344  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:18.237614019  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f78060490> adding pad 'sink'
0:00:18.237648269  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f78060490> adding pad 'src'
0:00:18.237695644  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:18.237737469  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78061380> adding pad 'sink'
0:00:18.237772169  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78061380> adding pad 'src'
0:00:18.237806669  1564   0x7f7c000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f78061380> PTS interpolation: no
0:00:18.237829444  1564   0x7f7c000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f78061380> TS inferring: no
0:00:18.237874319  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:18.237913669  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78064ba0> adding pad 'src'
0:00:18.237949594  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78064ba0> adding pad 'sink'
0:00:18.237993094  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:18.238120344  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue1 (0/0) with caps "(NULL)"
0:00:18.238148669  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue1:(any)
0:00:18.238176669  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue1:sink
0:00:18.238205594  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:18.238234169  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue1:src> pad has no peer
0:00:18.238260044  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue1 in same bin, no need for ghost pads
0:00:18.238290919  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue1:sink
0:00:18.238310744  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:18.238331344  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue1:src> pad has no peer
0:00:18.238356944  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue1:sink, successful
0:00:18.238371944  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.238388719  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:18.238426869  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue1 to some pad of GstVideoScale named videoscale1 (0/0) with caps "(NULL)"
0:00:18.238452569  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue1:(any) to element videoscale1:(any)
0:00:18.238474894  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue1:src and videoscale1:sink
0:00:18.238496369  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:18.238521194  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale1:src> pad has no peer
0:00:18.239142544  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue1 and videoscale1 in same bin, no need for ghost pads
0:00:18.239173844  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue1:src and videoscale1:sink
0:00:18.239197169  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:18.239221119  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale1:src> pad has no peer
0:00:18.239818719  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue1:src and videoscale1:sink, successful
0:00:18.239840869  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.239856944  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue1:src> Received event on flushing pad. Discarding
0:00:18.239908194  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale1 to some pad of GstMppH264Enc named mpph264enc1 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:18.239935519  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:18.239986394  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f78067390> adding pad 'sink'
0:00:18.240024419  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f78067390> adding pad 'src'
0:00:18.240055944  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin1> getting state
0:00:18.240100894  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to NULL
0:00:18.240130319  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.240157394  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale1:(any) to element capsfilter1:sink
0:00:18.240179794  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:sink
0:00:18.240197219  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale1 and capsfilter1 in same bin, no need for ghost pads
0:00:18.240227094  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale1:src and capsfilter1:sink
0:00:18.240257569  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:18.240865594  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter1:src> pad has no peer
0:00:18.240936294  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale1:src and capsfilter1:sink, successful
0:00:18.240952419  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.240967969  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale1:src> Received event on flushing pad. Discarding
0:00:18.241001669  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter1:src to element mpph264enc1:(any)
0:00:18.241023119  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:src
0:00:18.241045694  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter1:src and mpph264enc1:sink
0:00:18.241076394  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:18.241814744  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:18.241894944  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter1 and mpph264enc1 in same bin, no need for ghost pads
0:00:18.241928919  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter1:src and mpph264enc1:sink
0:00:18.241962844  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:18.242695219  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:18.242808769  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter1:src and mpph264enc1:sink, successful
0:00:18.242824844  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.242840369  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter1:src> Received event on flushing pad. Discarding
0:00:18.242879994  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc1 to some pad of GstH264Parse named h264parse1 (0/0) with caps "(NULL)"
0:00:18.242905019  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc1:(any) to element h264parse1:(any)
0:00:18.242930694  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc1:src and h264parse1:sink
0:00:18.242961819  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:00:18.242991444  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc1 and h264parse1 in same bin, no need for ghost pads
0:00:18.243016744  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc1:src and h264parse1:sink
0:00:18.243040119  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:00:18.243069294  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc1:src and h264parse1:sink, successful
0:00:18.243083694  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.243098519  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc1:src> Received event on flushing pad. Discarding
0:00:18.243152269  1564   0x7f7c000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse1 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:18.243177594  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse1:(any) to element pay0:(any)
0:00:18.243201219  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse1:src and pay0:sink
0:00:18.243228319  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:18.243255994  1564   0x7f7c000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse1 and pay0 in same bin, no need for ghost pads
0:00:18.243284544  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse1:src and pay0:sink
0:00:18.243309994  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:18.243333894  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse1:src and pay0:sink, successful
0:00:18.243350594  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.243364869  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse1:src> Received event on flushing pad. Discarding
0:00:18.243418394  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay0
0:00:18.243446669  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f78064ba0
0:00:18.243468994  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:18.243491294  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f78068030: creating stream with index 0 and payloader <pay0>
0:00:18.243567269  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad5
0:00:18.243590744  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad5, successful
0:00:18.243606769  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.243621169  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:18.243653794  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin1> adding pad 'src_0'
0:00:18.243678119  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f780685a0
0:00:18.243701744  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f780685a0> set retransmission time 0
0:00:18.243721794  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f780685a0> Enabling rate control
0:00:18.243758944  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f780685a0> set retransmission pt 97
0:00:18.243785044  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay0
0:00:18.243818744  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay0
0:00:18.243846369  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay1
0:00:18.243873669  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay1
0:00:18.243900619  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay1
0:00:18.243936719  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:18.244086444  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f780685a0> set retransmission time 0
[2021-01-01 14:16:43.994] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 14:16:43.994] [INFO] factory: 0x5599627460, media: 0x7f78068030, user_data: 0x559955fe20
[2021-01-01 14:16:43.994] [INFO] Calling configure_media...
[2021-01-01 14:16:43.994] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 14:16:43.994] [INFO] Got media pipeline: 0x7f780668d0
0:00:18.244229519  1564   0x7f7c000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element source
[2021-01-01 14:16:43.994] [INFO] Found appsrc element: 0x7f7805c8d0
[2021-01-01 14:16:43.994] [INFO] Connected appsrc signals
0:00:18.244318969  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:18.244353594  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:18.244379219  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 14:16:43.994] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 14:16:43.994] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:18.245104194  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f7805c490
0:00:18.245159169  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 14:16:43.995] [INFO] Initial frame push result: 0
[2021-01-01 14:16:43.995] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 14:16:43.995] [INFO] Configured appsrc properties
[2021-01-01 14:16:43.995] [INFO] Pipeline latency query result: live=0, min=0 ns, max=18446744073709551615 ns
[2021-01-01 14:16:43.995] [INFO] Setting manual latency: min=33333333 ns, max=100000000 ns
0:00:18.245349319  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.033333333
0:00:18.245397919  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
[2021-01-01 14:16:43.995] [INFO] Sent latency event to pipeline
[2021-01-01 14:16:43.995] [INFO] Media configured successfully
[2021-01-01 14:16:43.995] [INFO] configure_media call completed
0:00:18.245505094  1564   0x7f7c000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f78068030 for url /stream
0:00:18.245567369  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f78068030
0:00:18.245578186  1564   0x7f7c001830 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f780686d0
0:00:18.245591294  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:18.245642544  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:18.245785644  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:18.245800544  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:18.245816481  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:18.245841736  1564   0x7f7c001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:18.245884814  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:18.245904293  1564   0x7f7c001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:18.245932191  1564   0x7f7c001830 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f780685a0 joining bin as session 0
0:00:18.245959625  1564   0x7f7c001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:18.246029940  1564   0x7f7c001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:18.246063960  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f54005660> adding pad 'sink'
0:00:18.246086044  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f54005660> adding pad 'rtcp_sink'
0:00:18.246102709  1564   0x7f7c001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:18.246150970  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f54005fd0> adding pad 'src'
0:00:18.246163512  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f54005fd0> adding pad 'sink'
0:00:18.246262185  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:00:18.246277272  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:00:18.246289073  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:00:18.246326414  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_sink'
0:00:18.246357973  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_src'
0:00:18.246372742  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:send_rtp_src
0:00:18.246424281  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6
0:00:18.246443184  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6, successful
0:00:18.246453449  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.246479647  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_src_0'
0:00:18.246511943  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink
0:00:18.246525792  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink, successful
0:00:18.246534655  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.246552039  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_sink_0'
0:00:18.246572198  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin1:src_0 and rtpbin1:send_rtp_sink_0
0:00:18.246613695  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin1:src_0 and rtpbin1:send_rtp_sink_0, successful
0:00:18.246623736  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.246636446  1564   0x7f7c001830 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:18.246655476  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin1:send_rtp_src_0
0:00:18.246695061  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtcp_src'
0:00:18.246738525  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8
0:00:18.246752672  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8, successful
0:00:18.246762017  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.246781575  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtcp_src_0'
0:00:18.246815218  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'recv_rtcp_sink'
0:00:18.246849415  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'sync_src'
0:00:18.246867850  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:sync_src
0:00:18.246879571  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux1:rtcp_sink
0:00:18.246895435  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink
0:00:18.246908717  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink, successful
0:00:18.246917277  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.246955973  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink
0:00:18.246969928  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink, successful
0:00:18.246978724  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.246995709  1564   0x7f7c001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'recv_rtcp_sink_0'
0:00:18.247033582  1564   0x7f7c001830 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f780685a0> successfully joined bin
0:00:18.247053958  1564   0x7f7c001830 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f78068030
0:00:18.247065636  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f78068030 set blocked 1
0:00:18.247076925  1564   0x7f7c001830 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f780685a0> blocked: 1
0:00:18.247091029  1564   0x7f7c001830 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f78068030
0:00:18.247103039  1564   0x7f7c001830 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f78068030
0:00:18.247126993  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247157590  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247170678  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to READY
0:00:18.247182226  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247205332  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 2(READY) successfully
0:00:18.247220868  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247233343  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to READY
0:00:18.247244559  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247260629  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 2(READY) successfully
0:00:18.247275498  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247288107  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to READY
0:00:18.247299529  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247320315  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 2(READY) successfully
0:00:18.247334367  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to READY
0:00:18.247345789  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247361697  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 2(READY) successfully
0:00:18.247374914  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247410042  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:18.247423568  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:18.247434535  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247451332  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 2(READY) successfully
0:00:18.247466141  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247479482  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to READY
0:00:18.247490455  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247506119  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 2(READY) successfully
0:00:18.247522621  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247537143  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to READY
0:00:18.247548186  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247564099  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 2(READY) successfully
0:00:18.247578477  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247590847  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to READY
0:00:18.247600387  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247616091  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 2(READY) successfully
0:00:18.247630326  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247642888  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to READY
0:00:18.247654055  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247669997  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 2(READY) successfully
0:00:18.247684175  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current NULL pending VOID_PENDING, desired next READY
0:00:18.247696880  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to READY
0:00:18.247707241  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247727237  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 2(READY) successfully
0:00:18.247740855  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:18.247753737  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:18.247765130  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247780605  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 2(READY) successfully
0:00:18.247793737  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to READY
0:00:18.247805413  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:18.247821995  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 2(READY) successfully
0:00:18.247837336  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:18.247849264  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:18.247863076  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:18.247883778  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.247911429  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.247928934  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PAUSED
0:00:18.247940321  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.247956657  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 3(PAUSED) successfully
0:00:18.247971528  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.247988688  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PAUSED
0:00:18.248000278  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.248016198  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 3(PAUSED) successfully
0:00:18.248030034  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.248045735  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PAUSED
0:00:18.248056927  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.248072833  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 3(PAUSED) successfully
0:00:18.248088437  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PAUSED
0:00:18.248100021  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.248119996  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 3(PAUSED) successfully
0:00:18.248133667  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.248160096  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.248181968  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:18.248194089  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.248210839  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 3(PAUSED) successfully
0:00:18.248225889  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.248417285  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PAUSED
0:00:18.248431759  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.248461556  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 3(PAUSED) successfully
0:00:18.248479311  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.248821884  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PAUSED
0:00:18.248840611  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.248876592  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 3(PAUSED) successfully
0:00:18.248898408  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.248920679  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PAUSED
0:00:18.248933104  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.248950357  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 3(PAUSED) successfully
0:00:18.248966194  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.248985475  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to PAUSED
0:00:18.248997164  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.249013772  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 3(PAUSED) successfully
0:00:18.249029404  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.249062617  1564   0x7f7c001830 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7805d7b0 on task 0x7f54057780
0:00:18.249076637  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue1:src> created task 0x7f54057780
0:00:18.249278518  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to PAUSED
0:00:18.249292753  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.249312196  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 3(PAUSED) successfully
0:00:18.249328033  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:18.249345363  1564   0x7f7c001830 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:18.249371205  1564   0x7f7c001830 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:18.249408089  1564   0x7f7c001830 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7805cc40 on task 0x7f54058030
0:00:18.249420480  1564   0x7f7c001830 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f54058030
0:00:18.249534297  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:18.249549341  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.249568384  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin1> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:18.249582046  1564   0x7f7c001c50 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:18.249589665  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PAUSED
0:00:18.249611248  1564   0x7f7c001c50 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:18.249615222  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.249649748  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 3(PAUSED) successfully without preroll
0:00:18.249668260  1564   0x7f7c001830 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:18.249678958  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:18.249692049  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.249708072  1564   0x7f7c001830 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f78068030
0:00:18.249720152  1564   0x7f7c001830 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f78068030
0:00:18.249772714  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:18.249807114  1564   0x7f7c001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:18.249832936  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.249857197  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.249870280  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PLAYING
0:00:18.249882341  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.249900009  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:18.249914875  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.249928015  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PLAYING
0:00:18.249939634  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.249957860  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:18.249973004  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.250047539  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PLAYING
0:00:18.250062175  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250081501  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:18.250095698  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:18.250107997  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250124544  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:18.250154902  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.250168958  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:18.250180350  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250209598  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:18.250225380  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.250238384  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PLAYING
0:00:18.250250094  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250267327  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:18.250281798  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.250295162  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PLAYING
0:00:18.250306636  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250324354  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:18.250339399  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.250351921  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PLAYING
0:00:18.250363067  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250379770  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:18.250394633  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.250406643  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to PLAYING
0:00:18.250417220  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250433647  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:18.250448826  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.250461489  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to PLAYING
0:00:18.250472316  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250487831  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:18.250505437  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:18.250518857  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250537105  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:18.250541789  1564   0x7f7c001c50 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:18.250553126  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:18.250598720  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250619243  1564   0x7f7c001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:18.250634416  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:18.250646235  1564   0x7f7c001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.250675395  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:18.250705356  1564   0x7f7c001c50 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:18.250728761  1564   0x7f7c001c50 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 14:16:44.001] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:44.001] [INFO] appsrc: 0x7f7805c8d0, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:44.001] [INFO] Calling feed_data...
[2021-01-01 14:16:44.001] [INFO] === FEED DATA CALLED ===
0:00:18.250803913  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78068030: went from NULL to READY (pending PAUSED)
[2021-01-01 14:16:44.001] [INFO] appsrc: 0x7f7805c8d0
[2021-01-01 14:16:44.001] [INFO] DDS reader is initialized
[2021-01-01 14:16:44.001] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:44.001] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:44.001] [INFO] Creating GstBuffer for raw frame data...
0:00:18.250982381  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78068030: went from READY to PAUSED (pending VOID_PENDING)
0:00:18.251016000  1564   0x7f7c001830 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f78068030: got message type 2048 (new-clock)
0:00:18.251155652  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78068030: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 14:16:44.001] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:44.001] [INFO] Pushing raw buffer directly to appsrc...
0:00:18.251392406  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f4c0025a0
0:00:18.251430376  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:44.001] [INFO] Raw frame fed to appsrc successfully, total frames served: 9
[2021-01-01 14:16:44.001] [INFO] feed_data call completed
0:00:18.251475824  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:44.002] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:44.002] [INFO] appsrc: 0x7f7805c8d0, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:44.002] [INFO] Calling feed_data...
[2021-01-01 14:16:44.002] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:44.002] [INFO] appsrc: 0x7f7805c8d0
[2021-01-01 14:16:44.002] [INFO] DDS reader is initialized
[2021-01-01 14:16:44.002] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:44.002] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:44.002] [INFO] Creating GstBuffer for raw frame data...
0:00:18.252114794  1564   0x7f7c001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 14:16:44.002] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:44.002] [INFO] Pushing raw buffer directly to appsrc...
0:00:18.252209152  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f4c12f7d0
0:00:18.252227492  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:44.002] [INFO] Raw frame fed to appsrc successfully, total frames served: 10
[2021-01-01 14:16:44.002] [INFO] feed_data call completed
0:00:18.252280619  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:44.002] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:44.002] [INFO] appsrc: 0x7f7805c8d0, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:44.002] [INFO] Calling feed_data...
[2021-01-01 14:16:44.002] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:44.002] [INFO] appsrc: 0x7f7805c8d0
[2021-01-01 14:16:44.002] [INFO] DDS reader is initialized
[2021-01-01 14:16:44.002] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:44.002] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:44.002] [INFO] Creating GstBuffer for raw frame data...
0:00:18.252684819  1564   0x7f7c001a40 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter1> reuse caps
0:00:18.252740844  1564   0x7f7c001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 14:16:44.003] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:44.003] [INFO] Pushing raw buffer directly to appsrc...
0:00:18.252915524  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f4c1c6450
0:00:18.252933676  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:44.003] [INFO] Raw frame fed to appsrc successfully, total frames served: 11
[2021-01-01 14:16:44.003] [INFO] feed_data call completed
0:00:18.252978207  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:44.003] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:44.003] [INFO] appsrc: 0x7f7805c8d0, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:44.003] [INFO] Calling feed_data...
[2021-01-01 14:16:44.003] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:44.003] [INFO] appsrc: 0x7f7805c8d0
[2021-01-01 14:16:44.003] [INFO] DDS reader is initialized
[2021-01-01 14:16:44.003] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:44.003] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:44.003] [INFO] Creating GstBuffer for raw frame data...
0:00:18.253215494  1564   0x7f7c001a40 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc1> applying YUY2 1280x720 (2560x720)
[2021-01-01 14:16:44.004] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:44.004] [INFO] Pushing raw buffer directly to appsrc...
0:00:18.253740544  1564   0x7f7c001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
0:00:18.253750341  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f4c25ce10
0:00:18.253823408  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:44.004] [INFO] Raw frame fed to appsrc successfully, total frames served: 12
[2021-01-01 14:16:44.004] [INFO] feed_data call completed
0:00:18.254164926  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:44.004] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:44.004] [INFO] appsrc: 0x7f7805c8d0, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:44.004] [INFO] Calling feed_data...
[2021-01-01 14:16:44.004] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:44.004] [INFO] appsrc: 0x7f7805c8d0
[2021-01-01 14:16:44.004] [INFO] DDS reader is initialized
[2021-01-01 14:16:44.004] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:16:44.021] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:44.021] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:16:44.021] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:44.021] [INFO] Pushing raw buffer directly to appsrc...
0:00:18.271177273  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f4c2f3210
0:00:18.271201817  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:16:44.021] [INFO] Raw frame fed to appsrc successfully, total frames served: 13
[2021-01-01 14:16:44.021] [INFO] feed_data call completed
0:00:18.271250515  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:16:44.021] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:16:44.021] [INFO] appsrc: 0x7f7805c8d0, unused: 4096, user_data: 0x559955fe20
[2021-01-01 14:16:44.021] [INFO] Calling feed_data...
[2021-01-01 14:16:44.021] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:16:44.021] [INFO] appsrc: 0x7f7805c8d0
[2021-01-01 14:16:44.021] [INFO] DDS reader is initialized
[2021-01-01 14:16:44.021] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:18.284885456  1564   0x7f7c001a40 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f78060dc0 on task 0x7f48051a20
0:00:18.284911660  1564   0x7f7c001a40 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc1:src> created task 0x7f48051a20
0:00:18.288626669  1564   0x7f7c001e60 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:18.290484069  1564   0x7f7c001e60 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse1> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:18.290628119  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:18.290665894  1564   0x7f7c001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:18.290768519  1564   0x7f7c001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:18.291114819  1564   0x7f7c001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)1223241133, timestamp-offset=(uint)3859032329, seqnum-offset=(uint)24705, a-framerate=(string)30
0:00:18.291178819  1564   0x7f7c001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:18.291225369  1564   0x7f7c001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:18.291264419  1564   0x7f7c001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:18.291337594  1564   0x7f7c001e60 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f780685a0: new sender source 0x7f38010e10
0:00:18.291434319  1564   0x7f7c001e60 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1223241133, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)24705, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:18.291505719  1564   0x7f7c001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:18.291561344  1564   0x7f7c001e60 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f780685a0 received caps 0x7f3800ce30, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)1223241133, timestamp-offset=(uint)3859032329, seqnum-offset=(uint)24705, a-framerate=(string)30
0:00:18.291984919  1564   0x7f7c001e60 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession1> Can't determine running time for this packet without knowing configured latency
0:00:18.292047544  1564   0x7f7c001e60 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin1:send_rtp_src_0> Now blocking
0:00:18.292073169  1564   0x7f7c001e60 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f780685a0> position: 447086:16:43.850071000
0:00:18.292165594  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f78068030> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:18.292199119  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:18.292220294  1564   0x7f7c001830 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:18.292261569  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:18.292295939  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:18.292315608  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f78068030 is prerolled
0:00:18.292346831  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:18.292389996  1564   0x7f7c000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:18.292451792  1564   0x7f7c000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:18.292466055  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f78068030 no suspend
0:00:18.292477026  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:18.292496503  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://***********5:8554/stream/
0:00:18.295793694  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request SETUP rtsp://***********5:8554/stream/stream=0 1.0
0:00:18.295820033  1564   0x7f7c000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x5599627460 for path /stream/stream=0
0:00:18.295834259  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f78068030 for path /stream
0:00:18.295845322  1564   0x7f7c000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:18.295857065  1564   0x7f7c000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f78068030 was not prepared
0:00:18.295886368  1564   0x7f7c000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f7806c920
0:00:18.295901928  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f7806c920
0:00:18.295934438  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP/TCP;unicast;interleaved=0-1
0:00:18.295947352  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:18.295956461  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:18.295969049  1564   0x7f7c000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f78068030 in session 0x7f7806d130
0:00:18.299500616  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request PLAY rtsp://***********5:8554/stream/ 1.0
0:00:18.299536392  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f78068030> complete pipeline
0:00:18.299549309  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f780685a0> complete stream
0:00:18.299560232  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f780685a0> create receiver part
0:00:18.299582132  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f780685a0> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:18.299597174  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:18.299646491  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f78068930> adding pad 'src'
0:00:18.299685119  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel1:src
0:00:18.299709183  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel1:src and rtpbin1:recv_rtcp_sink_0
0:00:18.299748887  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel1:src and rtpbin1:recv_rtcp_sink_0, successful
0:00:18.299759694  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.299771500  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel1:src> Received event on flushing pad. Discarding
0:00:18.299790247  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:18.299821054  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78069020> adding pad 'src'
0:00:18.299857184  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsrc0:src
0:00:18.299876614  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsrc0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:18.299889200  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:18.299915902  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsrc0> continue state change READY to PAUSED, final PLAYING
0:00:18.299931388  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<appsrc0> starting
0:00:18.299958290  1564   0x7f7c000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<appsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:18.299988101  1564   0x7f7c000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f78069390 on task 0x7f78069870
0:00:18.300000328  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<appsrc0:src> created task 0x7f78069870
0:00:18.300163873  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:18.300181128  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:18.300204261  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:18.300218389  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsrc0> completed state change to PLAYING
0:00:18.300230351  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.300280046  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel1> adding pad 'funnelpad2'
0:00:18.300289469  1564   0x7f7c002070 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "appsrc0"
0:00:18.300343519  1564   0x7f7c002070 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<appsrc0:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:18.300347255  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link appsrc0:src and funnel1:funnelpad2
0:00:18.300397119  1564   0x7f7c002070 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<appsrc0:src> pad has no peer
0:00:18.300397873  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<appsrc0> caps: (NULL)
0:00:18.300469805  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked appsrc0:src and funnel1:funnelpad2, successful
0:00:18.300482139  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.300513380  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel1> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:18.300526791  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed NULL to READY (PLAYING pending)
0:00:18.300547494  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel1> continue state change READY to PAUSED, final PLAYING
0:00:18.300567456  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:18.300580571  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:18.300599901  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel1> continue state change PAUSED to PLAYING, final PLAYING
0:00:18.300611553  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel1> completed state change to PLAYING
0:00:18.300627519  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.300649668  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f780685a0> create sender part
0:00:18.300664246  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f780685a0> tcp: 1, udp: 0, mcast: 0 (ttl: 0)
0:00:18.300688715  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:18.300731364  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f78069cd0> adding pad 'sink'
0:00:18.300770989  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsink"
0:00:18.300929438  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f7806fb50> adding pad 'sink'
0:00:18.300960367  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3472:plug_tcp_sink:<GstRTSPStream@0x7f780685a0> plug tcp sink
0:00:18.300999176  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee2> adding pad 'src_0'
0:00:18.301014332  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsink0:sink
0:00:18.301034849  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee2:src_0 and appsink0:sink
0:00:18.301051784  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee2:sink> pad has no peer
0:00:18.301074284  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee2:src_0 and appsink0:sink, successful
0:00:18.301085117  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.301096978  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee2:src_0> Received event on flushing pad. Discarding
0:00:18.301119636  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:18.301132646  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:18.301154788  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsink0> continue state change READY to PAUSED, final PLAYING
0:00:18.301173941  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:18.301201628  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee2> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:18.301214828  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed NULL to READY (PAUSED pending)
0:00:18.301226344  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78068030: went from PAUSED to PAUSED (pending PAUSED)
0:00:18.301233202  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee2> continue state change READY to PAUSED, final PAUSED
0:00:18.301284500  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee2> completed state change to PAUSED
0:00:18.301297453  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.301313600  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee2:sink
0:00:18.301332161  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin1:send_rtp_src_0 and tee2:sink
0:00:18.301392772  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin1:send_rtp_src_0 and tee2:sink, successful
0:00:18.301402993  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.301451925  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:18.301491358  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f780714a0> adding pad 'sink'
0:00:18.301525258  1564   0x7f7c000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsink"
0:00:18.301550982  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f78071e50> adding pad 'sink'
0:00:18.301580616  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:3472:plug_tcp_sink:<GstRTSPStream@0x7f780685a0> plug tcp sink
0:00:18.301614758  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee3> adding pad 'src_0'
0:00:18.301629077  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsink1:sink
0:00:18.301646935  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee3:src_0 and appsink1:sink
0:00:18.301663022  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee3:sink> pad has no peer
0:00:18.301683835  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee3:src_0 and appsink1:sink, successful
0:00:18.301693755  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.301703769  1564   0x7f7c000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee3:src_0> Received event on flushing pad. Discarding
0:00:18.301725296  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:18.301737609  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:18.301753669  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsink1> continue state change READY to PAUSED, final PAUSED
0:00:18.301772653  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsink1> completed state change to PAUSED
0:00:18.301784739  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.301803143  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee3> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:18.301815251  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed NULL to READY (PAUSED pending)
0:00:18.301829840  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee3> continue state change READY to PAUSED, final PAUSED
0:00:18.301846036  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee3> completed state change to PAUSED
0:00:18.301857966  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:18.301873022  1564   0x7f7c000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee3:sink
0:00:18.301889435  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin1:send_rtcp_src_0 and tee3:sink
0:00:18.301920210  1564   0x7f7c000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin1:send_rtcp_src_0 and tee3:sink, successful
0:00:18.301930019  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:18.301950917  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f780685a0> pipeline successfully updated
0:00:18.301963222  1564   0x7f7c000b70 INFO              rtspstream rtsp-stream.c:4783:update_transport: adding TCP ***********
0:00:18.302014427  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:18.302026934  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:18.302050190  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f780685a0> Enabling rate control
0:00:18.302082171  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:18.302113335  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:18.302153815  1564   0x7f7c001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:18.302230544  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f78068030> seekable:0
0:00:18.302244222  1564   0x7f7c000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f78068030> Handle going back to 0 for none live not seekable streams.
0:00:18.302256551  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:18.302267791  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:18.302278405  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:18.302306878  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:18.302387137  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f78068030, target state PAUSED
0:00:18.302400975  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:18.302413149  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f78068030 set blocked 0
0:00:18.302425462  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f780685a0> blocked: 0
0:00:18.302447215  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f78068030 do_state 1
0:00:18.302459574  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f78068030
0:00:18.302471601  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f78068030
0:00:18.302484162  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f78068030 set blocked 0
0:00:18.302495357  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f780685a0> blocked: 0
0:00:18.302507298  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f78068030
0:00:18.302518476  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:18.302519533  1564   0x7f7c001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)1223241133, timestamp-offset=(uint)3859032329, seqnum-offset=(uint)24705, a-framerate=(string)30
0:00:18.302632444  1564   0x7f7c001830 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f78068030: got message type 16 (tag)
0:00:18.302633148  1564   0x7f7c001e60 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:18.302699819  1564   0x7f7c001e60 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:18.302722794  1564   0x7f7c001e60 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:18.302773044  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78068030: went from PAUSED to PAUSED (pending PLAYING)
0:00:18.302822894  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f78068030> got async-done
0:00:18.302910667  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:18.303036624  1564   0x7f7c002280 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:18.303079444  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:18.303172600  1564   0x7f7c002280 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:18.303249379  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.303274801  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsink1> completed state change to PLAYING
0:00:18.303292388  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.303356336  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink1' changed state to 4(PLAYING) successfully
0:00:18.303421705  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.303435671  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink0> skipping transition from PLAYING to  PLAYING
0:00:18.303448788  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink0' changed state to 4(PLAYING) successfully
0:00:18.303473776  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee3> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.303494350  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee3> completed state change to PLAYING
0:00:18.303510356  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.303543495  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee3' changed state to 4(PLAYING) successfully
0:00:18.303569535  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee2> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:18.303587586  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee2> completed state change to PLAYING
0:00:18.303599910  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:18.303618515  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee2' changed state to 4(PLAYING) successfully
0:00:18.303639966  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.303670789  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.303687648  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage1> skipping transition from PLAYING to  PLAYING
0:00:18.303701576  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:18.303719773  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.303733744  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux1> skipping transition from PLAYING to  PLAYING
0:00:18.303749931  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:18.303772700  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.303786942  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession1> skipping transition from PLAYING to  PLAYING
0:00:18.303801620  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:18.303820485  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:18.303839799  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:18.303866625  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.303911013  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.303935434  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:18.303914808  1564   0x7f7c001a40 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter1> reuse caps
0:00:18.303948637  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:18.304022456  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.304036981  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse1> skipping transition from PLAYING to  PLAYING
0:00:18.304050821  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:18.304070174  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.304081457  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc1> skipping transition from PLAYING to  PLAYING
0:00:18.304094256  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:18.304113608  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.304125780  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:18.304137971  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:18.304154191  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.304169311  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale1> skipping transition from PLAYING to  PLAYING
0:00:18.304185219  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:18.304206350  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.304219525  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue1> skipping transition from PLAYING to  PLAYING
0:00:18.304234871  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:18.304252190  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.304265923  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:18.304279583  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:18.304297578  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:18.304317970  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:18.304343973  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:18.304358225  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel1> skipping transition from PLAYING to  PLAYING
0:00:18.304373148  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel1' changed state to 4(PLAYING) successfully
0:00:18.304392571  1564   0x7f7c002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsrc0' changed state to 4(PLAYING) successfully
0:00:18.304421490  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:18.304437789  1564   0x7f7c002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
[2021-01-01 14:16:44.054] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:16:44.055] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:16:44.055] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:16:44.055] [INFO] Pushing raw buffer directly to appsrc...
0:00:18.304696113  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f4c2f3570
0:00:18.304715609  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
0:00:18.304723944  1564   0x7f7c001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> confi[2021-01-01 14:16:44.055] [INFO] Raw frame fed to appsrc successfully, total frames served: 14
gured latency of 0:00:00.053333333
[2021-01-01 14:16:44.055] [INFO] feed_data call completed
0:00:18.304767031  1564   0x7f7c001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:18.304909144  1564   0x7f7c001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78068030: went from PAUSED to PLAYING (pending VOID_PENDING)
0:00:19.328625669  1564   0x7f70061440 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2576950944, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)20743, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400330046018428419, sr-rtptime=(uint)2499495606, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 14:16:45.966] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:16:45.966] [INFO] Uptime: 20.0 seconds
[2021-01-01 14:16:45.967] [INFO] Total connections: 2
[2021-01-01 14:16:45.967] [INFO] Active connections: 2
[2021-01-01 14:16:45.967] [INFO] Frames served: 14
[2021-01-01 14:16:45.967] [INFO] Clients connected: 0
[2021-01-01 14:16:45.967] [INFO] Error count: 0
0:00:20.625763503  1564   0x7f7c000b70 DEBUG             rtspstream rtsp-stream.c:4664:gst_rtsp_stream_recv_rtcp: stream 0x7f780685a0: first buffer at time 2:16:46.985306277, base 2:16:44.609295102
0:00:20.625797881  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<appsrc0> queueing buffer 0x7f780712f0
0:00:20.625815717  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<appsrc0> Currently queued: 36 bytes, 1 buffers, 0:00:00.000000000
0:00:20.625849090  1564   0x7f7c002070 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<appsrc0> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:20.625884775  1564   0x7f7c002070 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:20.625918993  1564   0x7f7c002070 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<appsrc0> marking pending DISCONT
0:00:20.625967254  1564   0x7f7c002070 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:20.626064223  1564   0x7f7c002070 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f780685a0: new source 0x7f300020f0
0:00:20.626162525  1564   0x7f7c002070 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3531133162, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:54356, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:20.626200395  1564   0x7f7c002070 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:54356 in 1 transports
0:00:20.626213106  1564   0x7f7c002070 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f780685a0: found transport 0x7f7806d320 for source  0x7f300020f0
0:00:20.626226445  1564   0x7f7c002070 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f780685a0: source 0x7f300020f0 for transport 0x7f7806d320
0:00:20.626244903  1564   0x7f7c002070 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f780685a0: source 0x7f300020f0 in transport 0x7f7806d320 is active
0:00:20.626255682  1564   0x7f7c002070 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806c920 alive
0:00:20.626302423  1564   0x7f7c002070 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f780685a0: new SDES 0x7f300020f0
0:00:20.866174553  1564   0x7f54000df0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:20.866248779  1564   0x7f54000df0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:20.866281944  1564   0x7f54000df0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:20.866517567  1564   0x7f54000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1223241133, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)24705, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400330071213531212, sr-rtptime=(uint)3451622109, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:20.866560128  1564   0x7f40001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f780685a0> message send complete
0:00:20.866578810  1564   0x7f40001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806c920 alive
0:00:20.866647445  1564   0x7f7c001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:20.866801545  1564   0x7f7c001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:23.124017020  1564   0x7f70061440 INFO              rtspstream rtsp-stream.c:2496:on_bye_timeout: 0x7f7803ed60: source 0x7f580139f0 bye timeout
0:00:23.124350520  1564   0x7f70061440 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2576950944, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)20743, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400330046018428419, sr-rtptime=(uint)2499495606, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:26.320684216  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<appsrc0> queueing buffer 0x7f78052dd0
0:00:26.320719400  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<appsrc0> Currently queued: 36 bytes, 1 buffers, 0:00:00.000000000
0:00:26.320753157  1564   0x7f7c002070 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<appsrc0> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:26.320844344  1564   0x7f7c002070 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f780685a0: source 0x7f300020f0 in transport 0x7f7806d320 is active
0:00:26.320863183  1564   0x7f7c002070 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806c920 alive
0:00:26.738206752  1564   0x7f54000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1223241133, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)24705, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400330096433557699, sr-rtptime=(uint)3452150588, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:26.738210590  1564   0x7f40001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f780685a0> message send complete
0:00:26.738269775  1564   0x7f40001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806c920 alive
0:00:27.891865444  1564   0x7f70061440 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2576950944, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)20743, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400330046018428419, sr-rtptime=(uint)2499495606, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:28.830593582  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f84006630: received a request TEARDOWN rtsp://***********5:8554/stream/ 1.0
0:00:28.830650820  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f78068030, target state PLAYING
0:00:28.830667938  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:28.830680256  1564   0x7f7c000b70 INFO              rtspstream rtsp-stream.c:4787:update_transport: removing TCP ***********
0:00:28.830694368  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f78068030 do_state 1
0:00:28.830705811  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f78068030
0:00:28.830717014  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f78068030
0:00:28.830729257  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:28.830740140  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:28.830755718  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f78068030
0:00:28.830878730  1564   0x7f7c000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:28.830940650  1564   0x7f7c000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:28.830976928  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.830989311  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink1> skipping transition from PLAYING to  PLAYING
0:00:28.831001769  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink1' changed state to 4(PLAYING) successfully
0:00:28.831016119  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831026968  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink0> skipping transition from PLAYING to  PLAYING
0:00:28.831038100  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink0' changed state to 4(PLAYING) successfully
0:00:28.831051969  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee3> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831063013  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee3> skipping transition from PLAYING to  PLAYING
0:00:28.831073504  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee3' changed state to 4(PLAYING) successfully
0:00:28.831087467  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee2> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831099043  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee2> skipping transition from PLAYING to  PLAYING
0:00:28.831110129  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee2' changed state to 4(PLAYING) successfully
0:00:28.831124867  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831150917  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831162312  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage1> skipping transition from PLAYING to  PLAYING
0:00:28.831172993  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:28.831186959  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831198145  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux1> skipping transition from PLAYING to  PLAYING
0:00:28.831209301  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:28.831222737  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831233554  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession1> skipping transition from PLAYING to  PLAYING
0:00:28.831244192  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:28.831258205  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:28.831270159  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:28.831282556  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831306011  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831316518  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:28.831327424  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:28.831340377  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831351275  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse1> skipping transition from PLAYING to  PLAYING
0:00:28.831362100  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:28.831375916  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831386501  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc1> skipping transition from PLAYING to  PLAYING
0:00:28.831397405  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:28.831410288  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831420555  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:28.831430994  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:28.831443971  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831454993  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale1> skipping transition from PLAYING to  PLAYING
0:00:28.831465623  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:28.831478581  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831489014  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue1> skipping transition from PLAYING to  PLAYING
0:00:28.831499594  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:28.831511412  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831521482  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:28.831531794  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:28.831543520  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:28.831555197  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:28.831569149  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:28.831579517  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel1> skipping transition from PLAYING to  PLAYING
0:00:28.831590158  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel1' changed state to 4(PLAYING) successfully
0:00:28.831602816  1564   0x7f7c000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsrc0' changed state to 4(PLAYING) successfully
0:00:28.831616149  1564   0x7f7c000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:28.831626530  1564   0x7f7c000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:28.831648081  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<appsrc0> queue event: eos event: 0x7f78072c30, time 99:99:99.999999999, seq-num 354, (NULL)
0:00:28.831674985  1564   0x7f7c000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f78072c30, time 99:99:99.999999999, seq-num 354, (NULL)
0:00:28.831677306  1564   0x7f7c002070 DEBUG                 appsrc gstappsrc.c:1683:gst_app_src_create:<appsrc0> pop event eos event: 0x7f78072c30, time 99:99:99.999999999, seq-num 354, (NULL)
0:00:28.831700239  1564   0x7f7c000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f7806d130
0:00:28.831716648  1564   0x7f7c000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f78068030 was not prepared
0:00:28.831728541  1564   0x7f7c000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f78068030 is already unpreparing
0:00:28.831750571  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f84006630: sending close message
0:00:28.831817296  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f84006630: session 0x7f7806c920 removed
0:00:28.831833300  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f84006630: unwatch session 0x7f7806c920
0:00:28.831853921  1564   0x7f7c000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f7806c920
0:00:28.831927634  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f84006630: connection closed
0:00:28.831945385  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f84006630: watch destroyed
0:00:28.831962771  1564   0x7f7c000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x5599625050> unmanage client 0x7f84006630
0:00:28.831987226  1564   0x7f7c000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f84007550
0:00:28.832004404  1564   0x7f7c000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f84006630
0:00:28.832083881  1564   0x7f7c000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f84008020
[2021-01-01 14:16:55.967] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:16:55.967] [INFO] Uptime: 30.0 seconds