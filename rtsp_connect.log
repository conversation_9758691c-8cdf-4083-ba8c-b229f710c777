root@rk3576-buildroot:/# rtsp_server --hw-encoder --gst-debug 4
[2021-01-01 17:44:11.928] [INFO] === RTSP Server Configuration ===
[2021-01-01 17:44:11.928] [INFO] DDS Topic: Video_Frames
[2021-01-01 17:44:11.928] [INFO] Server: 0.0.0.0:8554/stream
[2021-01-01 17:44:11.928] [INFO] Output: 1280x720@30fps, H264, 2000000 bps
[2021-01-01 17:44:11.928] [INFO] GOP Size: 15
[2021-01-01 17:44:11.928] [INFO] Hardware Encoder: yes
[2021-01-01 17:44:11.928] [INFO] Max Clients: 10
[2021-01-01 17:44:11.928] [INFO] Buffer Size: 5
[2021-01-01 17:44:11.928] [INFO] Zero Copy: yes
[2021-01-01 17:44:11.928] [INFO] GStreamer Debug Level: 4
[2021-01-01 17:44:11.928] [INFO] Adaptive Bitrate: 500000 - 5000000 bps
[2021-01-01 17:44:11.928] [INFO] GStreamer debug level set to: 4 (*:4)
0:00:00.000083308  1717   0x55853d48f0 INFO                GST_INIT gst.c:576:init_pre: Initializing GStreamer Core Library version 1.22.9
0:00:00.000100047  1717   0x55853d48f0 INFO                GST_INIT gst.c:577:init_pre: Using library installed in /lib
0:00:00.000112247  1717   0x55853d48f0 INFO                GST_INIT gst.c:595:init_pre: Linux rk3576-buildroot 6.1.99-rk3576 #1 SMP Mon Jun 30 10:03:13 CST 2025 aarch64
0:00:00.000350423  1717   0x55853d48f0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.000864021  1717   0x55853d48f0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001084991  1717   0x55853d48f0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001190246  1717   0x55853d48f0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001204981  1717   0x55853d48f0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001269306  1717   0x55853d48f0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007390102  1717   0x55853d48f0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.006074 seconds
0:00:00.007453923  1717   0x55853d48f0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.008053897  1717   0x55853d48f0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.008072042  1717   0x55853d48f0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.008084945  1717   0x55853d48f0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.008095042  1717   0x55853d48f0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.008102753  1717   0x55853d48f0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
[2021-01-01 17:44:11.936] [INFO] GStreamer initialized successfully
[2021-01-01 17:44:11.937] [INFO] Media factory configured: shared=FALSE, eos_shutdown=TRUE
[2021-01-01 17:44:11.937] [INFO] Supported protocols: UDP, UDP_MCAST, TCP
Start init DDS reader: Video_Frames
Create share memery qos success
Create participant success
Register type success
Create subscriber success
Create topic success
DDS Reader initialized for topic: Video_Frames[2021-01-01 17:44:11.943] [INFO] Waiting for first frame from DDS topic: Video_Frames
Subscriber matched
[2021-01-01 17:44:12.143] [INFO] First frame received: 640x480 format=1448695129, output will be: 1280x720@30fps
[2021-01-01 17:44:12.143] [INFO] RTSPMediaFactory initialized for topic: Video_Frames
[2021-01-01 17:44:12.143] [INFO] Pipeline: ( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )
0:00:00.214917990  1717   0x55853d48f0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x55854b4280 for path /stream

(<unknown>:1717): GLib-GObject-CRITICAL **: 17:44:12.143: g_object_set_is_valid_property: object class 'GstRTSPServer' has no property named 'timeout'
[2021-01-01 17:44:12.143] [INFO] RTSP server initialized: 0.0.0.0:8554/stream
0:00:00.215042619  1717   0x55853d48f0 DEBUG             rtspserver rtsp-server.c:882:gst_rtsp_server_create_socket:<GstRTSPServer@0x55854b1e70> getting address info of 0.0.0.0/8554
0:00:00.215471513  1717   0x55853d48f0 DEBUG             rtspserver rtsp-server.c:967:gst_rtsp_server_create_socket:<GstRTSPServer@0x55854b1e70> opened sending server socket
0:00:00.215501654  1717   0x55853d48f0 DEBUG             rtspserver rtsp-server.c:994:gst_rtsp_server_create_socket:<GstRTSPServer@0x55854b1e70> listening on server socket 0x5585603f10 with queue of 5
[2021-01-01 17:44:12.144] [INFO] RTSP server started on 0.0.0.0:8554/stream
[2021-01-01 17:44:12.144] [INFO] RTSP server is running. Access stream at: rtsp://0.0.0.0:8554/stream
[2021-01-01 17:44:12.144] [INFO] Press Ctrl+C to stop the server
[2021-01-01 17:44:12.144] [INFO] RTSP server main loop started





[2021-01-01 17:44:22.145] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:44:22.145] [INFO] Uptime: 10.0 seconds
[2021-01-01 17:44:22.145] [INFO] Total connections: 0
[2021-01-01 17:44:22.145] [INFO] Active connections: 0
[2021-01-01 17:44:22.145] [INFO] Frames served: 0
[2021-01-01 17:44:22.145] [INFO] Clients connected: 0
[2021-01-01 17:44:22.145] [INFO] Error count: 0
[2021-01-01 17:44:32.145] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:44:32.145] [INFO] Uptime: 20.0 seconds
[2021-01-01 17:44:32.145] [INFO] Total connections: 0
[2021-01-01 17:44:32.145] [INFO] Active connections: 0
[2021-01-01 17:44:32.145] [INFO] Frames served: 0
[2021-01-01 17:44:32.145] [INFO] Clients connected: 0
[2021-01-01 17:44:32.145] [INFO] Error count: 0
0:00:23.905528339  1717   0x7f88000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f88006630 connected to server ip ***********5, ipv6 = 0
0:00:23.905578514  1717   0x7f88000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f88006630 ip ***********:64864
0:00:23.905612364  1717   0x7f88000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x55854b1e70> manage client 0x7f88006630
[2021-01-01 17:44:35.834] [INFO] === CLIENT CONNECTED ===
[2021-01-01 17:44:35.834] [INFO] Active connections: 1, Total connections: 1
0:00:23.906106643  1717   0x7f84000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f88006f10
0:00:23.906154864  1717   0x7f88000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f88006630: attaching to context 0x7f88007440
0:00:23.915510756  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request OPTIONS rtsp://***********5:8554/stream 1.0
0:00:23.917455861  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request DESCRIBE rtsp://***********5:8554/stream 1.0
0:00:23.917485849  1717   0x7f84000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x55854b4280 for path /stream
0:00:23.917503231  1717   0x7f84000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:23.917967059  1717   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstapp.so" loaded
0:00:23.918232454  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:23.918267207  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78007680> adding pad 'src'
0:00:23.918286689  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 0
0:00:23.918316904  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:23.919357437  1717   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:23.919502155  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:23.919545979  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7800d010> adding pad 'sink'
0:00:23.919573661  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7800d010> adding pad 'src'
0:00:23.920143391  1717   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoconvertscale.so" loaded
0:00:23.920468451  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:23.920499938  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f78017830> adding pad 'sink'
0:00:23.920525886  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f78017830> adding pad 'src'
0:00:23.922584548  1717   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:23.922821550  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:23.922855580  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f7801bc60> adding pad 'sink'
0:00:23.922877761  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f7801bc60> adding pad 'src'
0:00:23.924084125  1717   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:00:23.924210353  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:23.924241995  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78024710> adding pad 'sink'
0:00:23.924263874  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78024710> adding pad 'src'
0:00:23.924294720  1717   0x7f84000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f78024710> PTS interpolation: no
0:00:23.924307623  1717   0x7f84000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f78024710> TS inferring: no
0:00:23.925976442  1717   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:23.926190175  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:23.926235209  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78034580> adding pad 'src'
0:00:23.926274803  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78034580> adding pad 'sink'
0:00:23.926325656  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:23.926414655  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue0 (0/0) with caps "(NULL)"
0:00:23.926430751  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue0:(any)
0:00:23.926457172  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue0:sink
0:00:23.926476155  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:23.926495483  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:23.926512927  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue0 in same bin, no need for ghost pads
0:00:23.926532797  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue0:sink
0:00:23.926545868  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:23.926569683  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:23.926587721  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue0:sink, successful
0:00:23.926597488  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.926608062  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:23.926639766  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue0 to some pad of GstVideoScale named videoscale0 (0/0) with caps "(NULL)"
0:00:23.926652810  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue0:(any) to element videoscale0:(any)
0:00:23.926666765  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue0:src and videoscale0:sink
0:00:23.926681678  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:23.926699280  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:23.927118753  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue0 and videoscale0 in same bin, no need for ghost pads
0:00:23.927137197  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue0:src and videoscale0:sink
0:00:23.927152678  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:23.927168818  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:23.927537097  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue0:src and videoscale0:sink, successful
0:00:23.927588807  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.927611985  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue0:src> Received event on flushing pad. Discarding
0:00:23.927686651  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:23.927710207  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:23.927845821  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7803dfc0> adding pad 'sink'
0:00:23.927890242  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7803dfc0> adding pad 'src'
0:00:23.927929397  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:23.927989759  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:23.928023832  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.928055506  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale0:(any) to element capsfilter0:sink
0:00:23.928071825  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:23.928088467  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale0 and capsfilter0 in same bin, no need for ghost pads
0:00:23.928115869  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale0:src and capsfilter0:sink
0:00:23.928150317  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:23.928689911  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:23.928747534  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale0:src and capsfilter0:sink, successful
0:00:23.928759253  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.928770303  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale0:src> Received event on flushing pad. Discarding
0:00:23.928796105  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mpph264enc0:(any)
0:00:23.928808741  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:23.928824244  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mpph264enc0:sink
0:00:23.928847666  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:23.929260212  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:23.929311027  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mpph264enc0 in same bin, no need for ghost pads
0:00:23.929329942  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mpph264enc0:sink
0:00:23.929347406  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:23.929750343  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:23.929812887  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mpph264enc0:sink, successful
0:00:23.929822816  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.929832202  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:23.929855787  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:00:23.929868518  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:00:23.929882789  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:00:23.929900287  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:23.929918305  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:00:23.929934299  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:00:23.929948802  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:23.929967622  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:00:23.929977274  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.929986366  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:23.930006391  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:23.930019015  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:00:23.930033343  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:00:23.930051930  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:23.930069890  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:00:23.930086304  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:00:23.930102020  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:23.930118224  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:00:23.930127809  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.930138228  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:00:23.930264395  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:23.930283598  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f78034580
0:00:23.930295463  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:23.930326493  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f7803fbb0: creating stream with index 0 and payloader <pay0>
0:00:23.930452286  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:23.930467602  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:23.930476642  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.930486835  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:23.930507178  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:23.930544825  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f78041a60
0:00:23.930561500  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f78041a60> set retransmission time 0
0:00:23.930573386  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f78041a60> Enabling rate control
0:00:23.930594071  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f78041a60> set retransmission pt 97
0:00:23.930607505  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:23.930627665  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:23.930645150  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:23.930662051  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:23.930679373  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:23.930701770  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:23.930804384  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f78041a60> set retransmission time 0
[2021-01-01 17:44:35.859] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 17:44:35.859] [INFO] factory: 0x55854b4280, media: 0x7f7803fbb0, user_data: 0x55853ece20
[2021-01-01 17:44:35.859] [INFO] Calling configure_media...
[2021-01-01 17:44:35.859] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 17:44:35.859] [INFO] Got media pipeline: 0x7f780361d0
0:00:23.930890854  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element source
[2021-01-01 17:44:35.859] [INFO] Found appsrc element: 0x7f78007680
[2021-01-01 17:44:35.859] [INFO] Connected appsrc signals
0:00:23.930949919  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:23.930969537  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:23.930984203  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 17:44:35.859] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 17:44:35.859] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:23.931473851  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f7802edb0
0:00:23.931502675  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:44:35.860] [INFO] Initial frame push result: 0
[2021-01-01 17:44:35.860] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 17:44:35.860] [INFO] Configured appsrc properties
[2021-01-01 17:44:35.860] [INFO] Media configured successfully
[2021-01-01 17:44:35.860] [INFO] configure_media call completed
0:00:23.931606398  1717   0x7f84000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f7803fbb0 for url /stream
0:00:23.931736563  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f7803fbb0
0:00:23.931751583  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:23.931780913  1717   0x7f84000d80 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f78039670
0:00:23.933062779  1717   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:23.933088437  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:23.933608180  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:23.933622156  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:23.933645751  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:23.933672532  1717   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:23.933719141  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:23.933739161  1717   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:23.933768641  1717   0x7f84000d80 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f78041a60 joining bin as session 0
0:00:23.933793694  1717   0x7f84000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:23.934199790  1717   0x7f84000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:23.934307725  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f7c00b3b0> adding pad 'sink'
0:00:23.934330246  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f7c00b3b0> adding pad 'rtcp_sink'
0:00:23.934347054  1717   0x7f84000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:23.934430751  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7c00c830> adding pad 'src'
0:00:23.934445060  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f7c00c830> adding pad 'sink'
0:00:23.934556643  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:23.934573095  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:23.934585063  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:23.934631484  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:23.934666497  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:23.934681200  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:23.934726935  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:23.934742312  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:23.934751932  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.934783715  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:23.934818399  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:23.934832091  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:23.934841372  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.934858547  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:23.934877998  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:23.934919374  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:23.934930185  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.934943467  1717   0x7f84000d80 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:23.934964277  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:23.935135250  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:23.935184274  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:23.935198650  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:23.935208772  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.935237132  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:23.935279880  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:23.935307875  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:23.935327924  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:23.935340423  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:23.935357373  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:23.935370476  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:23.935379371  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.935419889  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:23.935433468  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:23.935442238  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:23.935459696  1717   0x7f84000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:23.935499561  1717   0x7f84000d80 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f78041a60> successfully joined bin
0:00:23.935520261  1717   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f7803fbb0
0:00:23.935532359  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f7803fbb0 set blocked 1
0:00:23.935544068  1717   0x7f84000d80 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f78041a60> blocked: 1
0:00:23.935558900  1717   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f7803fbb0
0:00:23.935571581  1717   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f7803fbb0
0:00:23.935597688  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:23.935630708  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:23.935644065  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:23.935656043  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.935680822  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:23.935697373  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:23.935709759  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:23.935721447  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.935737809  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:23.935752584  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:23.935764968  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:23.935776129  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.935797466  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:23.935811467  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:23.935823289  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.935840813  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:23.935854568  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:23.935892519  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:23.935906991  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:23.935919057  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.935935764  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:23.935950285  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:00:23.935963412  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:23.935975643  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.935991422  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:23.936006555  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:23.936018872  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:23.936030561  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.936046686  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:23.936061691  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:23.936075283  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:23.936086857  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.936102407  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:23.936118026  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current NULL pending VOID_PENDING, desired next READY
0:00:23.936130530  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:23.936142040  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.936160650  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:23.936175498  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current NULL pending VOID_PENDING, desired next READY
0:00:23.936188495  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:23.936199738  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.936215182  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:23.936228674  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:23.936241977  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:23.936253315  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.936269217  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:23.936282399  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:23.936294141  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:23.936309380  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:23.936324290  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:23.936336595  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:23.936350713  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:23.936370105  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.936398158  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.936416082  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:23.936427957  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.936447341  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:23.936462761  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.936479992  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:23.936491833  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.936507568  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:23.936525244  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.936541603  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:23.936553126  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.936569054  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:23.936585046  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:23.936596780  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.936612785  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:23.936625752  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.936672738  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.936698295  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:23.936713605  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.936731830  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:23.936747486  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.937013091  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:23.937029293  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.937049033  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:23.937066524  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.937451083  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:23.937479604  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.937505144  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:23.937525735  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.937547586  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:23.937562394  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.937579291  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:23.937595376  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.937614768  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:23.937627171  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.937644664  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:23.937660205  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.937696790  1717   0x7f84000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7800d820 on task 0x7f7c05ea00
0:00:23.937710195  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue0:src> created task 0x7f7c05ea00
0:00:23.937873845  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:23.937889098  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.937977332  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:23.937994550  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:23.938011780  1717   0x7f84000d80 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:23.938038392  1717   0x7f84000d80 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:23.938062789  1717   0x7f84000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f780079f0 on task 0x7f7c05efc0
0:00:23.938074857  1717   0x7f84000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f7c05efc0
0:00:23.938213755  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:23.938229928  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.938248936  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:23.938261278  1717   0x7f84000ff0 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:23.938269393  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:23.938291708  1717   0x7f84000ff0 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:23.938296285  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.938333738  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:23.938352411  1717   0x7f84000d80 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:23.938363621  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:23.938374830  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:23.938392618  1717   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f7803fbb0
0:00:23.938416291  1717   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803fbb0
0:00:23.938470008  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:23.938491079  1717   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:23.938515391  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.938538994  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.938552105  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:23.938563983  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.938581837  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:23.938596655  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.938609385  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:23.938620751  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.938637697  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:23.938652495  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.938724850  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:23.938739349  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.938783883  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:23.938802144  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:23.938814523  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.938832215  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:23.938860806  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.938874911  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:23.938886578  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.938906404  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:23.938921563  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.938934600  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:00:23.938946335  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.938960322  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:23.938975325  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.938988917  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:23.939000019  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.939016039  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:23.939030706  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.939043652  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:23.939055170  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.939070737  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:23.939085495  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.939098099  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PLAYING
0:00:23.939109479  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.939125560  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:23.939141166  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:23.939153914  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PLAYING
0:00:23.939175004  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.939192484  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:23.939210779  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:23.939224329  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.939241033  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:23.939247010  1717   0x7f84000ff0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:23.939256648  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:23.939287638  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.939306175  1717   0x7f84000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:23.939320768  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:23.939332507  1717   0x7f84000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:23.939373104  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:23.939408097  1717   0x7f84000ff0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:23.939436906  1717   0x7f84000ff0 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 17:44:35.868] [INFO] === NEED DATA CALLBACK TRIGGERED ===
0:00:23.939486916  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803fbb0: went[2021-01-01 17:44:35.868] [INFO] appsrc: 0x7f78007680, unused: 4096, user_data: 0x55853ece20
 from NULL to READY (pending PAUSED)
[2021-01-01 17:44:35.868] [INFO] Calling feed_data...
[2021-01-01 17:44:35.868] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:35.868] [INFO] appsrc: 0x7f78007680
[2021-01-01 17:44:35.868] [INFO] DDS reader is initialized
[2021-01-01 17:44:35.868] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:35.868] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:35.868] [INFO] Creating GstBuffer for raw frame data...
0:00:23.939663815  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803fbb0: went from READY to PAUSED (pending VOID_PENDING)
0:00:23.939695636  1717   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803fbb0: got message type 2048 (new-clock)
0:00:23.939836732  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803fbb0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 17:44:35.868] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:35.868] [INFO] Pushing raw buffer directly to appsrc...
0:00:23.940127557  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f74001f70
0:00:23.940148927  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:35.868] [INFO] Raw frame fed to appsrc successfully, total frames served: 2
[2021-01-01 17:44:35.868] [INFO] feed_data call completed
0:00:23.940192790  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:35.869] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:35.869] [INFO] appsrc: 0x7f78007680, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:35.869] [INFO] Calling feed_data...
[2021-01-01 17:44:35.869] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:35.869] [INFO] appsrc: 0x7f78007680
[2021-01-01 17:44:35.869] [INFO] DDS reader is initialized
[2021-01-01 17:44:35.869] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:35.869] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:35.869] [INFO] Creating GstBuffer for raw frame data...
0:00:23.940693690  1717   0x7f84000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 17:44:35.869] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:35.869] [INFO] Pushing raw buffer directly to appsrc...
0:00:23.940876586  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f7412e800
0:00:23.940896970  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:35.869] [INFO] Raw frame fed to appsrc successfully, total frames served: 3
[2021-01-01 17:44:35.869] [INFO] feed_data call completed
0:00:23.940939825  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:35.869] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:35.869] [INFO] appsrc: 0x7f78007680, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:35.869] [INFO] Calling feed_data...
[2021-01-01 17:44:35.869] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:35.869] [INFO] appsrc: 0x7f78007680
[2021-01-01 17:44:35.869] [INFO] DDS reader is initialized
[2021-01-01 17:44:35.869] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:35.869] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:35.869] [INFO] Creating GstBuffer for raw frame data...
0:00:23.941155440  1717   0x7f84000de0 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:23.941200265  1717   0x7f84000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 17:44:35.870] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:35.870] [INFO] Pushing raw buffer directly to appsrc...
0:00:23.941510301  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f741c4c00
0:00:23.941529991  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:35.870] [INFO] Raw frame fed to appsrc successfully, total frames served: 4
0:00:23.941544865  1717   0x7f84000de0 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applying YUY2 1280x720 (2560x720)
[2021-01-01 17:44:35.870] [INFO] feed_data call completed
0:00:23.941582748  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:35.870] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:35.870] [INFO] appsrc: 0x7f78007680, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:35.870] [INFO] Calling feed_data...
[2021-01-01 17:44:35.870] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:35.870] [INFO] appsrc: 0x7f78007680
[2021-01-01 17:44:35.870] [INFO] DDS reader is initialized
[2021-01-01 17:44:35.870] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:35.870] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:35.870] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:44:35.871] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:35.871] [INFO] Pushing raw buffer directly to appsrc...
0:00:23.942267179  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f7425afb0
0:00:23.942288494  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:35.871] [INFO] Raw frame fed to appsrc successfully, total frames served: 5
[2021-01-01 17:44:35.871] [INFO] feed_data call completed
0:00:23.942650343  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:35.871] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:35.871] [INFO] appsrc: 0x7f78007680, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:35.871] [INFO] Calling feed_data...
[2021-01-01 17:44:35.871] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:35.871] [INFO] appsrc: 0x7f78007680
[2021-01-01 17:44:35.871] [INFO] DDS reader is initialized
[2021-01-01 17:44:35.871] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:23.943804941  1717   0x7f84000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
[2021-01-01 17:44:35.897] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:35.897] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:44:35.898] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:35.898] [INFO] Pushing raw buffer directly to appsrc...
0:00:23.969318117  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f742f1170
0:00:23.969340890  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:35.898] [INFO] Raw frame fed to appsrc successfully, total frames served: 6
[2021-01-01 17:44:35.898] [INFO] feed_data call completed
0:00:23.969387149  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:35.898] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:35.898] [INFO] appsrc: 0x7f78007680, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:35.898] [INFO] Calling feed_data...
[2021-01-01 17:44:35.898] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:35.898] [INFO] appsrc: 0x7f78007680
[2021-01-01 17:44:35.898] [INFO] DDS reader is initialized
[2021-01-01 17:44:35.898] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:23.975103199  1717   0x7f84000de0 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7801c3c0 on task 0x7f70035c60
0:00:23.975127757  1717   0x7f84000de0 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f70035c60
0:00:23.978922153  1717   0x7f84001200 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:23.979484533  1717   0x7f84001200 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse0> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:23.979568001  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:23.979594775  1717   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:23.979648076  1717   0x7f84001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:23.979875542  1717   0x7f84001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)3373745737, timestamp-offset=(uint)973388742, seqnum-offset=(uint)7670, a-framerate=(string)30
0:00:23.979919307  1717   0x7f84001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:23.979952236  1717   0x7f84001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:23.979981095  1717   0x7f84001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:23.980081170  1717   0x7f84001200 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f78041a60: new sender source 0x7f64010ca0
0:00:23.980143660  1717   0x7f84001200 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3373745737, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)7670, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:23.980186126  1717   0x7f84001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:23.980225446  1717   0x7f84001200 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f78041a60 received caps 0x7f6400c860, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)3373745737, timestamp-offset=(uint)973388742, seqnum-offset=(uint)7670, a-framerate=(string)30
0:00:23.980470112  1717   0x7f84001200 INFO               videometa gstvideometa.c:1100:gst_video_time_code_meta_api_get_type: registering
0:00:23.980572881  1717   0x7f84001200 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:00:23.980629630  1717   0x7f84001200 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin0:send_rtp_src_0> Now blocking
0:00:23.980660428  1717   0x7f84001200 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f78041a60> position: 447089:44:35.695940000
0:00:23.980723496  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f7803fbb0> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:23.980737638  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:23.980747663  1717   0x7f84000d80 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:23.980757135  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:23.980785310  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:23.980800141  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f7803fbb0 is prerolled
0:00:23.980827149  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:23.980880915  1717   0x7f84000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:23.980946005  1717   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:23.980959780  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f7803fbb0 no suspend
0:00:23.980970484  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:23.980992097  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://***********5:8554/stream/
0:00:23.998240583  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request SETUP rtsp://***********5:8554/stream/stream=0 1.0
0:00:23.998275194  1717   0x7f84000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x55854b4280 for path /stream/stream=0
0:00:23.998294172  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f7803fbb0 for path /stream
0:00:23.998305331  1717   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:23.998316065  1717   0x7f84000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f7803fbb0 was not prepared
0:00:23.998397274  1717   0x7f84000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f78041ee0
0:00:23.998414506  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f78041ee0
0:00:23.998446813  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=64548-64549
0:00:23.998459759  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:23.998468497  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:23.998498212  1717   0x7f84000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f7803fbb0 in session 0x7f7804b990
0:00:23.998512814  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:1866:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f78041a60> GST_RTSP_LOWER_TRANS_UDP, ipv4
0:00:23.998648070  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f78041a60> allocated address: 0.0.0.0 and ports: 43900, 43901
0:00:23.998661761  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:1880:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f78041a60> GST_RTSP_LOWER_TRANS_UDP, ipv6
0:00:23.998757981  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f78041a60> allocated address: :: and ports: 52088, 52089
0:00:24.000694456  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request PLAY rtsp://***********5:8554/stream/ 1.0
0:00:24.000728443  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f7803fbb0> complete pipeline
0:00:24.000741503  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f78041a60> complete stream
0:00:24.000751482  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f78041a60> create receiver part
0:00:24.000772083  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f78041a60> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:24.000787429  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:24.000868287  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f7804d500> adding pad 'src'
0:00:24.000905387  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:00:24.000930240  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:24.000970306  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:00:24.000981602  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.000993658  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:00:24.001011481  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3778:create_receiver_part:<GstRTSPStream@0x7f78041a60> udp IPv4, create and configure udpsources
0:00:24.001796714  1717   0x7f84000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:00:24.001821026  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:24.002014488  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78050350> adding pad 'src'
0:00:24.002090061  1717   0x7f84000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:00:24.002118068  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:00:24.002132494  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.002158532  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:24.002192253  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:24.002224821  1717   0x7f84000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.002249259  1717   0x7f84000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f78050750 on task 0x7f78051080
0:00:24.002262834  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f78051080
0:00:24.002542235  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:24.002559874  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:24.002585396  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:24.002604070  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:00:24.002617366  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.002658567  1717   0x7f84001410 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:00:24.002690421  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:00:24.002714705  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:00:24.002774142  1717   0x7f84001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:24.002806850  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:00:24.002821834  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.002810542  1717   0x7f84001410 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:24.002847542  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3802:create_receiver_part:<GstRTSPStream@0x7f78041a60> udp IPv6, create and configure udpsources
0:00:24.002872912  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:24.002901944  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f78051cd0> adding pad 'src'
0:00:24.002970505  1717   0x7f84000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:00:24.002999173  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:00:24.003013090  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:24.003035653  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:24.003067826  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:24.003097620  1717   0x7f84000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.003100967  1717   0x7f84001410 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.003124967  1717   0x7f84000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f780520d0 on task 0x7f780527f0
0:00:24.003159217  1717   0x7f84001410 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:00:24.003161326  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f780527f0
0:00:24.003400992  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:24.003416757  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:24.003438426  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:00:24.003453449  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:00:24.003467972  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.003506217  1717   0x7f84001620 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:00:24.003558777  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:00:24.003592650  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:00:24.003596067  1717   0x7f84001620 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:24.003643767  1717   0x7f84001620 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:24.003669536  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:00:24.003681605  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.003709480  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:24.003722323  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:24.003743068  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:00:24.003763351  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:24.003775533  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:24.003793724  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:00:24.003804932  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:00:24.003817175  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.003838085  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f78041a60> create sender part
0:00:24.003853085  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f78041a60> tcp: 0, udp: 1, mcast: 0 (ttl: 0)
0:00:24.003875822  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:24.003967422  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f78053680> adding pad 'sink'
0:00:24.004008380  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:24.004188871  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f78055c40> adding pad 'sink'
0:00:24.004229374  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f78041a60> udp IPv4, configure udpsinks
0:00:24.004243915  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f78041a60> udp IPv6, configure udpsinks
0:00:24.004259934  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f78041a60> plug udp sink
0:00:24.004283345  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f78041a60> creating first stream
0:00:24.004317800  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:00:24.004332568  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:00:24.004353115  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:00:24.004369644  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:00:24.004391364  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:00:24.004401276  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.004411227  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:00:24.004445983  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:24.004459229  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:24.004481159  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:00:24.004497079  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:24.004526390  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:24.004532753  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803fbb0: went from PAUSED to PAUSED (pending PAUSED)
0:00:24.004561043  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:00:24.004580788  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:00:24.004598241  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:24.004610844  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.004627508  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:00:24.004658829  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:00:24.004721882  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:00:24.004733172  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.004783678  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:24.004823224  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f780586a0> adding pad 'sink'
0:00:24.004861012  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:24.004887701  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f78058ed0> adding pad 'sink'
0:00:24.004926419  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f78041a60> udp IPv4, configure udpsinks
0:00:24.004940801  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f78041a60> udp IPv6, configure udpsinks
0:00:24.004955559  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f78041a60> plug udp sink
0:00:24.004976111  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f78041a60> creating first stream
0:00:24.005000977  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:00:24.005014881  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:00:24.005032392  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:00:24.005047843  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:00:24.005068634  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:00:24.005078246  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.005088157  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:00:24.005120454  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:24.005133096  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:24.005149047  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:00:24.005167459  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:24.005179585  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.005197114  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:24.005208810  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:24.005222932  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:00:24.005238406  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:24.005250455  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:24.005265647  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:00:24.005282362  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:24.005312945  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:00:24.005322853  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:24.005343041  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f78041a60> pipeline successfully updated
0:00:24.005357640  1717   0x7f84000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:64548-64549
0:00:24.005446108  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:24.005457992  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:24.005485326  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f78041a60> Enabling rate control
0:00:24.005515336  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:24.005552083  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:24.005593012  1717   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:24.005670410  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f7803fbb0> seekable:0
0:00:24.005684294  1717   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f7803fbb0> Handle going back to 0 for none live not seekable streams.
0:00:24.005696034  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:24.005708008  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:24.005717982  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:24.005744481  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:24.005826259  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f7803fbb0, target state PAUSED
0:00:24.005840412  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:24.005852472  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f7803fbb0 set blocked 0
0:00:24.005863865  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f78041a60> blocked: 0
0:00:24.005887461  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f7803fbb0 do_state 1
0:00:24.005899060  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f7803fbb0
0:00:24.005910421  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f7803fbb0
0:00:24.005922285  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f7803fbb0 set blocked 0
0:00:24.005933212  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f78041a60> blocked: 0
0:00:24.005943958  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803fbb0
0:00:24.005954813  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:24.005983637  1717   0x7f84001200 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:24.005999666  1717   0x7f84001200 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:24.006012729  1717   0x7f84001200 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:24.006014367  1717   0x7f84000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f7803fbb0: got message type 16 (tag)
0:00:24.006117917  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803fbb0: went from PAUSED to PAUSED (pending PLAYING)
0:00:24.006169567  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f7803fbb0> got async-done
0:00:24.006296117  1717   0x7f84001830 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:24.006482792  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:24.006517117  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:24.006590917  1717   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:24.006646092  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.006669767  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:00:24.006687717  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.006723092  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:24.006745892  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.006762592  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:24.006779417  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:24.006802167  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.006820142  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:00:24.006837167  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.006861117  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:24.006882542  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:24.006900042  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:00:24.006917042  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.006939392  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:24.006962142  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007000342  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007017592  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:24.007034792  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:24.007056967  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007073167  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:24.007089617  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:24.007110817  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007126867  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:24.007143667  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:24.007162892  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:24.007181217  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:24.007200542  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007236192  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007252517  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:24.007270067  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:24.007292067  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007308442  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:24.007325917  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:24.007348842  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007364967  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:24.007381642  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:24.007402492  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007418642  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:24.007435817  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:24.007456667  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007473067  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:24.007489492  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:24.007513917  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007530017  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:24.007549692  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:24.007568792  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007584217  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:24.007600667  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:24.007618292  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:24.007636417  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:24.007660042  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:24.007676192  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:24.007692992  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:24.007712867  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:24.007732792  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:24.007753167  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:24.007770192  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:24.007920917  1717   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:24.008043667  1717   0x7f84000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f7803fbb0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 17:44:35.937] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:35.937] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:44:35.937] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:35.937] [INFO] Pushing raw buffer directly to appsrc...
0:00:24.008737452  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f742f1290
0:00:24.008757851  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:35.937] [INFO] Raw frame fed to appsrc successfully, total frames served: 7
[2021-01-01 17:44:35.937] [INFO] feed_data call completed
0:00:24.008805469  1717   0x7f84000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:26.740372261  1717   0x7f7c05f8c0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:26.740460841  1717   0x7f7c05f8c0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:26.740662896  1717   0x7f7c05f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3373745737, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)7670, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400383638271068282, sr-rtptime=(uint)1688461345, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:26.740763117  1717   0x7f84000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:26.740876767  1717   0x7f84000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:26.907746142  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f78041a60: new source 0x7f580139e0
0:00:26.907961642  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3143091859, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:64549, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:26.908036542  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:64549 in 1 transports
0:00:26.908058742  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f78041a60: found transport 0x7f7804c780 for source  0x7f580139e0
0:00:26.908081067  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f78041a60: source 0x7f580139e0 for transport 0x7f7804c780
0:00:26.908114667  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f78041a60: source 0x7f580139e0 in transport 0x7f7804c780 is active
0:00:26.908131492  1717   0x7f84001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78041ee0 alive
0:00:26.908211467  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f78041a60: new SDES 0x7f580139e0
0:00:29.156861571  1717   0x7f7c05f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3373745737, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)7670, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400383648649440141, sr-rtptime=(uint)1688678821, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 17:44:42.146] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:44:42.146] [INFO] Uptime: 30.0 seconds
[2021-01-01 17:44:42.146] [INFO] Total connections: 1
[2021-01-01 17:44:42.146] [INFO] Active connections: 1
[2021-01-01 17:44:42.146] [INFO] Frames served: 7
[2021-01-01 17:44:42.146] [INFO] Clients connected: 0
[2021-01-01 17:44:42.146] [INFO] Error count: 0
0:00:31.278034518  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f78041a60: source 0x7f580139e0 in transport 0x7f7804c780 is active
0:00:31.278090943  1717   0x7f84001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78041ee0 alive
0:00:34.557996727  1717   0x7f7c05f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3373745737, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)7670, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400383648649440141, sr-rtptime=(uint)1688678821, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:34.599515644  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f78041a60: source 0x7f580139e0 in transport 0x7f7804c780 is active
0:00:34.599561944  1717   0x7f84001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f78041ee0 alive
0:00:34.599577159  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request TEARDOWN rtsp://***********5:8554/stream/ 1.0
0:00:34.599598369  1717   0x7f84001410 INFO              rtspstream rtsp-stream.c:2488:on_bye_ssrc: 0x7f78041a60: source 0x7f580139e0 bye
0:00:34.599631279  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f7803fbb0, target state PLAYING
0:00:34.599647853  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:34.599662780  1717   0x7f84000b70 INFO              rtspstream rtsp-stream.c:4774:update_transport: removing ***********:64548-64549
0:00:34.599701237  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f7803fbb0 do_state 1
0:00:34.599713715  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f7803fbb0
0:00:34.599725027  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f7803fbb0
0:00:34.599737161  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:34.599748257  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:34.599759368  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f7803fbb0
0:00:34.599876037  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:34.599938195  1717   0x7f84000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:34.599973367  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.599985817  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink1> skipping transition from PLAYING to  PLAYING
0:00:34.599997842  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:34.600012672  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600024140  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:34.600035983  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:34.600050860  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600062393  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee1> skipping transition from PLAYING to  PLAYING
0:00:34.600073447  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:34.600087883  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600098977  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee0> skipping transition from PLAYING to  PLAYING
0:00:34.600109613  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:34.600124294  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600148843  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600160647  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:34.600172157  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:34.600186987  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600198470  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:34.600209722  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:34.600223743  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600235156  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:34.600246210  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:34.600260079  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:34.600272888  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:34.600286316  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600310301  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600321448  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:34.600333381  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:34.600347284  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600358482  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:34.600369023  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:34.600382347  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600393461  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:34.600404147  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:34.600417889  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600429584  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:34.600440786  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:34.600454355  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600465372  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:34.600478322  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:34.600492062  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600502929  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:34.600513580  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:34.600526084  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600536905  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:34.600549305  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:34.600561691  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:34.600574073  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:34.600589080  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.600600564  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:34.600612297  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:34.600625662  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:34.600650171  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:34.600667146  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:34.600678477  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:34.600747094  1717   0x7f84001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = flushing
0:00:34.600780169  1717   0x7f84001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:34.600827794  1717   0x7f84001620 INFO                    task gsttask.c:370:gst_task_func:<udpsrc1:src> Task resume from paused
0:00:34.600851544  1717   0x7f84001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = flushing
0:00:34.600879769  1717   0x7f84001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:34.600908944  1717   0x7f84001410 INFO                    task gsttask.c:370:gst_task_func:<udpsrc0:src> Task resume from paused
0:00:34.600924266  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f7805a390, time 99:99:99.999999999, seq-num 181, (NULL)
0:00:34.600928844  1717   0x7f84001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = eos
0:00:34.600953208  1717   0x7f84000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f7804b990
0:00:34.600985322  1717   0x7f84000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f7803fbb0 was not prepared
0:00:34.600998405  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f7803fbb0 is already unpreparing
0:00:34.600985869  1717   0x7f84001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:34.601022265  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f88006630: sending close message
0:00:34.601091744  1717   0x7f84001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = eos
0:00:34.601116040  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f88006630: session 0x7f78041ee0 removed
0:00:34.601131267  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f88006630: unwatch session 0x7f78041ee0
0:00:34.601151026  1717   0x7f84000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f78041ee0
0:00:34.601202888  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f88006630: connection closed
0:00:34.601219458  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f88006630: watch destroyed
0:00:34.601236079  1717   0x7f84000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x55854b1e70> unmanage client 0x7f88006630
0:00:34.601247894  1717   0x7f84001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:34.601259755  1717   0x7f84000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f88006cb0
0:00:34.601293126  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f88006630
0:00:34.601364102  1717   0x7f84000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f88006f10
0:00:34.603594594  1717   0x7f88000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f88006630 connected to server ip ***********5, ipv6 = 0
0:00:34.603628769  1717   0x7f88000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f88006630 ip ***********:64877
0:00:34.603650369  1717   0x7f88000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x55854b1e70> manage client 0x7f88006630
[2021-01-01 17:44:46.532] [INFO] === CLIENT CONNECTED ===
[2021-01-01 17:44:46.532] [INFO] Active connections: 2, Total connections: 2
0:00:34.603770616  1717   0x7f84000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f88008020
0:00:34.603812669  1717   0x7f88000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f88006630: attaching to context 0x7f88008140
0:00:34.604013829  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request OPTIONS rtsp://***********5:8554/stream 1.0
0:00:34.605334142  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request DESCRIBE rtsp://***********5:8554/stream 1.0
0:00:34.605361851  1717   0x7f84000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x55854b4280 for path /stream
0:00:34.605380925  1717   0x7f84000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:34.605467411  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:34.605509955  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f7805ca10> adding pad 'src'
0:00:34.605532498  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 0
0:00:34.605558727  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:34.605595399  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:34.605633356  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7805d270> adding pad 'sink'
0:00:34.605662107  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f7805d270> adding pad 'src'
0:00:34.605692667  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:34.605717419  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7805f780> adding pad 'sink'
0:00:34.605737936  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f7805f780> adding pad 'src'
0:00:34.605790449  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:34.605818577  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f78060190> adding pad 'sink'
0:00:34.605838763  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f78060190> adding pad 'src'
0:00:34.605869016  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:34.605893940  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78060f70> adding pad 'sink'
0:00:34.605914936  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f78060f70> adding pad 'src'
0:00:34.605934827  1717   0x7f84000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f78060f70> PTS interpolation: no
0:00:34.605946821  1717   0x7f84000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f78060f70> TS inferring: no
0:00:34.605978003  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:34.606003961  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78064b00> adding pad 'src'
0:00:34.606024579  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f78064b00> adding pad 'sink'
0:00:34.606051912  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:34.606129716  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue1 (0/0) with caps "(NULL)"
0:00:34.606145397  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue1:(any)
0:00:34.606160476  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue1:sink
0:00:34.606177916  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:34.606197710  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue1:src> pad has no peer
0:00:34.606214650  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue1 in same bin, no need for ghost pads
0:00:34.606235291  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue1:sink
0:00:34.606249386  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:34.606264759  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue1:src> pad has no peer
0:00:34.606282056  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue1:sink, successful
0:00:34.606291965  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.606302960  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:34.606326934  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue1 to some pad of GstVideoScale named videoscale1 (0/0) with caps "(NULL)"
0:00:34.606339647  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue1:(any) to element videoscale1:(any)
0:00:34.606353138  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue1:src and videoscale1:sink
0:00:34.606368266  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:34.606386234  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale1:src> pad has no peer
0:00:34.606751310  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue1 and videoscale1 in same bin, no need for ghost pads
0:00:34.606769609  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue1:src and videoscale1:sink
0:00:34.606785547  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:34.606801509  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale1:src> pad has no peer
0:00:34.607130310  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue1:src and videoscale1:sink, successful
0:00:34.607140816  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.607150956  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue1:src> Received event on flushing pad. Discarding
0:00:34.607180195  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale1 to some pad of GstMppH264Enc named mpph264enc1 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:34.607195823  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:34.607228632  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f78067100> adding pad 'sink'
0:00:34.607249951  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f78067100> adding pad 'src'
0:00:34.607267258  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin1> getting state
0:00:34.607295543  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to NULL
0:00:34.607311842  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.607329138  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale1:(any) to element capsfilter1:sink
0:00:34.607341420  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:sink
0:00:34.607352946  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale1 and capsfilter1 in same bin, no need for ghost pads
0:00:34.607368899  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale1:src and capsfilter1:sink
0:00:34.607387106  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:34.607719602  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter1:src> pad has no peer
0:00:34.607758212  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale1:src and capsfilter1:sink, successful
0:00:34.607768122  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.607777727  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale1:src> Received event on flushing pad. Discarding
0:00:34.607795373  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter1:src to element mpph264enc1:(any)
0:00:34.607806959  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:src
0:00:34.607820064  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter1:src and mpph264enc1:sink
0:00:34.607836730  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:34.608238572  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:34.608286946  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter1 and mpph264enc1 in same bin, no need for ghost pads
0:00:34.608304672  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter1:src and mpph264enc1:sink
0:00:34.608322537  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:34.608744913  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:34.608805866  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter1:src and mpph264enc1:sink, successful
0:00:34.608816305  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.608825869  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter1:src> Received event on flushing pad. Discarding
0:00:34.608858832  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc1 to some pad of GstH264Parse named h264parse1 (0/0) with caps "(NULL)"
0:00:34.608871861  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc1:(any) to element h264parse1:(any)
0:00:34.608885702  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc1:src and h264parse1:sink
0:00:34.608904980  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:00:34.608924477  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc1 and h264parse1 in same bin, no need for ghost pads
0:00:34.608940579  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc1:src and h264parse1:sink
0:00:34.608955296  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:00:34.608973801  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc1:src and h264parse1:sink, successful
0:00:34.608982870  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.608992131  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc1:src> Received event on flushing pad. Discarding
0:00:34.609013343  1717   0x7f84000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse1 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:34.609025444  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse1:(any) to element pay0:(any)
0:00:34.609038368  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse1:src and pay0:sink
0:00:34.609055655  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:34.609073156  1717   0x7f84000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse1 and pay0 in same bin, no need for ghost pads
0:00:34.609088742  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse1:src and pay0:sink
0:00:34.609103239  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:34.609118790  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse1:src and pay0:sink, successful
0:00:34.609127919  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.609136908  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse1:src> Received event on flushing pad. Discarding
0:00:34.609167823  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay0
0:00:34.609185413  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f78064b00
0:00:34.609196264  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:34.609210182  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f78067db0: creating stream with index 0 and payloader <pay0>
0:00:34.609256922  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad5
0:00:34.609270538  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad5, successful
0:00:34.609279266  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.609288771  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:34.609308844  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin1> adding pad 'src_0'
0:00:34.609323198  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f78068950
0:00:34.609338325  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f78068950> set retransmission time 0
0:00:34.609350061  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f78068950> Enabling rate control
0:00:34.609371061  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f78068950> set retransmission pt 97
0:00:34.609384798  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay0
0:00:34.609404673  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay0
0:00:34.609422176  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay1
0:00:34.609438935  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay1
0:00:34.609455818  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay1
0:00:34.609477062  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:34.609559894  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f78068950> set retransmission time 0
[2021-01-01 17:44:46.538] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 17:44:46.538] [INFO] factory: 0x55854b4280, media: 0x7f78067db0, user_data: 0x55853ece20
[2021-01-01 17:44:46.538] [INFO] Calling configure_media...
[2021-01-01 17:44:46.538] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 17:44:46.538] [INFO] Got media pipeline: 0x7f78066770
0:00:34.609653691  1717   0x7f84000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element source
[2021-01-01 17:44:46.538] [INFO] Found appsrc element: 0x7f7805ca10
[2021-01-01 17:44:46.538] [INFO] Connected appsrc signals
0:00:34.609717591  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:34.609736638  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:34.609752450  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 17:44:46.538] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 17:44:46.538] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:34.610294733  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f78068300
0:00:34.610323130  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 17:44:46.539] [INFO] Initial frame push result: 0
[2021-01-01 17:44:46.539] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 17:44:46.539] [INFO] Configured appsrc properties
[2021-01-01 17:44:46.539] [INFO] Media configured successfully
[2021-01-01 17:44:46.539] [INFO] configure_media call completed
0:00:34.610429420  1717   0x7f84000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f78067db0 for url /stream
0:00:34.610466822  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f78067db0
0:00:34.610478663  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:34.610493972  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:34.610494369  1717   0x7f84001830 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f78068250
0:00:34.610587701  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:34.610598589  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:34.610640919  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:34.610685394  1717   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:34.610750719  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:34.610782819  1717   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:34.610825944  1717   0x7f84001830 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f78068950 joining bin as session 0
0:00:34.610871794  1717   0x7f84001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:34.610988944  1717   0x7f84001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:34.611043394  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f50005810> adding pad 'sink'
0:00:34.611088394  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f50005810> adding pad 'rtcp_sink'
0:00:34.611116894  1717   0x7f84001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:34.611188369  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f50006280> adding pad 'src'
0:00:34.611206769  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f50006280> adding pad 'sink'
0:00:34.611377419  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:00:34.611399794  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:00:34.611418419  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:00:34.611486519  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_sink'
0:00:34.611531469  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_src'
0:00:34.611557444  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:send_rtp_src
0:00:34.611644269  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6
0:00:34.611667444  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6, successful
0:00:34.611682694  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.611726994  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_src_0'
0:00:34.611776369  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink
0:00:34.611803469  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink, successful
0:00:34.611820394  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.611847019  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_sink_0'
0:00:34.611878769  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin1:src_0 and rtpbin1:send_rtp_sink_0
0:00:34.611952994  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin1:src_0 and rtpbin1:send_rtp_sink_0, successful
0:00:34.611973069  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.611992444  1717   0x7f84001830 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:34.612024194  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin1:send_rtp_src_0
0:00:34.612091894  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtcp_src'
0:00:34.612158419  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8
0:00:34.612191544  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8, successful
0:00:34.612208944  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.612237469  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtcp_src_0'
0:00:34.612290994  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'recv_rtcp_sink'
0:00:34.612336444  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'sync_src'
0:00:34.612365844  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:sync_src
0:00:34.612387194  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux1:rtcp_sink
0:00:34.612413419  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink
0:00:34.612436519  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink, successful
0:00:34.612452794  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.612515544  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink
0:00:34.612541719  1717   0x7f84001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink, successful
0:00:34.612561794  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.612589069  1717   0x7f84001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'recv_rtcp_sink_0'
0:00:34.612691594  1717   0x7f84001830 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f78068950> successfully joined bin
0:00:34.612730119  1717   0x7f84001830 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f78067db0
0:00:34.612749394  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f78067db0 set blocked 1
0:00:34.612768569  1717   0x7f84001830 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f78068950> blocked: 1
0:00:34.612794869  1717   0x7f84001830 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f78067db0
0:00:34.612815369  1717   0x7f84001830 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f78067db0
0:00:34.612866144  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current NULL pending VOID_PENDING, desired next READY
0:00:34.612919794  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current NULL pending VOID_PENDING, desired next READY
0:00:34.612943344  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to READY
0:00:34.612962219  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613009044  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 2(READY) successfully
0:00:34.613034469  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current NULL pending VOID_PENDING, desired next READY
0:00:34.613055894  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to READY
0:00:34.613073069  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613097119  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 2(READY) successfully
0:00:34.613119244  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current NULL pending VOID_PENDING, desired next READY
0:00:34.613136969  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to READY
0:00:34.613153944  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613192219  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 2(READY) successfully
0:00:34.613213844  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to READY
0:00:34.613232294  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613257794  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 2(READY) successfully
0:00:34.613279919  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current NULL pending VOID_PENDING, desired next READY
0:00:34.613339669  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:34.613360819  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:34.613379419  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613404619  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 2(READY) successfully
0:00:34.613427669  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current NULL pending VOID_PENDING, desired next READY
0:00:34.613448119  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to READY
0:00:34.613465544  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613490869  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 2(READY) successfully
0:00:34.613514994  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current NULL pending VOID_PENDING, desired next READY
0:00:34.613535419  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to READY
0:00:34.613552994  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613576794  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 2(READY) successfully
0:00:34.613601294  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current NULL pending VOID_PENDING, desired next READY
0:00:34.613619144  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to READY
0:00:34.613636219  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613660194  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 2(READY) successfully
0:00:34.613683869  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current NULL pending VOID_PENDING, desired next READY
0:00:34.613701694  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to READY
0:00:34.613718394  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613740944  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 2(READY) successfully
0:00:34.613763794  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current NULL pending VOID_PENDING, desired next READY
0:00:34.613781069  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to READY
0:00:34.613814894  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613839169  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 2(READY) successfully
0:00:34.613859419  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:34.613877244  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:34.613894144  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613916994  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 2(READY) successfully
0:00:34.613938419  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to READY
0:00:34.613955244  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:34.613982094  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 2(READY) successfully
0:00:34.614007569  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:34.614025644  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:34.614046069  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:34.614081594  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.614130944  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.614157994  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PAUSED
0:00:34.614175769  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.614202769  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 3(PAUSED) successfully
0:00:34.614226094  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.614252469  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PAUSED
0:00:34.614270869  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.614298644  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 3(PAUSED) successfully
0:00:34.614323669  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.614349444  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PAUSED
0:00:34.614366644  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.614390019  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 3(PAUSED) successfully
0:00:34.614412919  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PAUSED
0:00:34.614429894  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.614453844  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 3(PAUSED) successfully
0:00:34.614474144  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.614517169  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.614549269  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:34.614567394  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.614590944  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 3(PAUSED) successfully
0:00:34.614613494  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.614887069  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PAUSED
0:00:34.614911619  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.614940744  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 3(PAUSED) successfully
0:00:34.614964119  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.615479994  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PAUSED
0:00:34.615510919  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.615548619  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 3(PAUSED) successfully
0:00:34.615579919  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.615611244  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PAUSED
0:00:34.615629444  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.615667619  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 3(PAUSED) successfully
0:00:34.615694069  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.615721244  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to PAUSED
0:00:34.615739144  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.615762369  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 3(PAUSED) successfully
0:00:34.615785344  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.615832369  1717   0x7f84001830 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7805d870 on task 0x7f50057340
0:00:34.615851169  1717   0x7f84001830 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue1:src> created task 0x7f50057340
0:00:34.616079794  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to PAUSED
0:00:34.616101944  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.616127394  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 3(PAUSED) successfully
0:00:34.616149669  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:34.616174119  1717   0x7f84001830 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:34.616211869  1717   0x7f84001830 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:34.616254544  1717   0x7f84001830 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7805cd80 on task 0x7f500579a0
0:00:34.616272694  1717   0x7f84001830 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f500579a0
0:00:34.616430469  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:34.616498119  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.616531594  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin1> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:34.616532500  1717   0x7f84001c50 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:34.616564244  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PAUSED
0:00:34.616588769  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.616608669  1717   0x7f84001c50 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:34.616614994  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 3(PAUSED) successfully without preroll
0:00:34.616679044  1717   0x7f84001830 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:34.616694944  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:34.616711869  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.616733494  1717   0x7f84001830 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f78067db0
0:00:34.616750569  1717   0x7f84001830 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f78067db0
0:00:34.616832144  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:34.616861669  1717   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:34.616897869  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.616932019  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.616950919  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PLAYING
0:00:34.616967569  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.616992869  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:34.617015194  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.617033419  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PLAYING
0:00:34.617052719  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617101719  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:34.617125369  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.617246919  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PLAYING
0:00:34.617266844  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617292019  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:34.617311769  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:34.617329144  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617353144  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:34.617394394  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.617414144  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:34.617430919  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617454094  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:34.617475069  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.617492819  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PLAYING
0:00:34.617509269  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617532094  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:34.617553644  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.617572344  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PLAYING
0:00:34.617589369  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617613844  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:34.617635044  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.617653794  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PLAYING
0:00:34.617671344  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617706819  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:34.617729469  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.617746969  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to PLAYING
0:00:34.617763494  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617786369  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:34.617807644  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.617825219  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to PLAYING
0:00:34.617841744  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617864844  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:34.617889894  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:34.617910119  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.617935407  1717   0x7f84001c50 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:34.617936994  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:34.617978594  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:34.617995844  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.618020519  1717   0x7f84001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:34.618039919  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:34.618046371  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:34.618062869  1717   0x7f84001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.618089781  1717   0x7f84001c50 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:34.618127082  1717   0x7f84001c50 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 17:44:46.546] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:46.547] [INFO] appsrc: 0x7f7805ca10, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:46.547] [INFO] Calling feed_data...
[2021-01-01 17:44:46.547] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:46.547] [INFO] appsrc: 0x7f7805ca10
[2021-01-01 17:44:46.547] [INFO] DDS reader is initialized
[2021-01-01 17:44:46.547] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:46.547] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:46.547] [INFO] Creating GstBuffer for raw frame data...
0:00:34.618286469  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78067db0: went from NULL to READY (pending PAUSED)
0:00:34.618501869  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78067db0: went from READY to PAUSED (pending VOID_PENDING)
0:00:34.618536669  1717   0x7f84001830 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f78067db0: got message type 2048 (new-clock)
0:00:34.618702244  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78067db0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 17:44:46.547] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:46.547] [INFO] Pushing raw buffer directly to appsrc...
0:00:34.618792959  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f48002660
0:00:34.618834696  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:46.547] [INFO] Raw frame fed to appsrc successfully, total frames served: 9
[2021-01-01 17:44:46.547] [INFO] feed_data call completed
0:00:34.618885772  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:46.547] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:46.547] [INFO] appsrc: 0x7f7805ca10, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:46.547] [INFO] Calling feed_data...
[2021-01-01 17:44:46.547] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:46.547] [INFO] appsrc: 0x7f7805ca10
[2021-01-01 17:44:46.547] [INFO] DDS reader is initialized
[2021-01-01 17:44:46.547] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:46.547] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:46.547] [INFO] Creating GstBuffer for raw frame data...
0:00:34.619170419  1717   0x7f84001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 17:44:46.548] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:46.548] [INFO] Pushing raw buffer directly to appsrc...
0:00:34.619554077  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f4812f890
0:00:34.619573636  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:46.548] [INFO] Raw frame fed to appsrc successfully, total frames served: 10
0:00:34.619589944  1717   0x7f84001a40 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilt[2021-01-01 17:44:46.548] [INFO] feed_data call completed
er1> reuse caps
0:00:34.619630594  1717   0x7f84001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:34.619634886  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:46.548] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:46.548] [INFO] appsrc: 0x7f7805ca10, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:46.548] [INFO] Calling feed_data...
[2021-01-01 17:44:46.548] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:46.548] [INFO] appsrc: 0x7f7805ca10
[2021-01-01 17:44:46.548] [INFO] DDS reader is initialized
[2021-01-01 17:44:46.548] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:46.548] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:46.548] [INFO] Creating GstBuffer for raw frame data...
0:00:34.619959694  1717   0x7f84001a40 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc1> applying YUY2 1280x720 (2560x720)
[2021-01-01 17:44:46.549] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:46.549] [INFO] Pushing raw buffer directly to appsrc...
0:00:34.620380564  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f481c6510
0:00:34.620407130  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
0:00:34.620411044  1717   0x7f84001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video[2021-01-01 17:44:46.549] [INFO] Raw frame fed to appsrc successfully, total frames served: 11
/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
[2021-01-01 17:44:46.549] [INFO] feed_data call completed
0:00:34.620466644  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:46.549] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:46.549] [INFO] appsrc: 0x7f7805ca10, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:46.549] [INFO] Calling feed_data...
[2021-01-01 17:44:46.549] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:46.549] [INFO] appsrc: 0x7f7805ca10
[2021-01-01 17:44:46.549] [INFO] DDS reader is initialized
[2021-01-01 17:44:46.549] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:46.549] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:46.549] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:44:46.550] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:46.550] [INFO] Pushing raw buffer directly to appsrc...
0:00:34.621303012  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f4825d090
0:00:34.621323389  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:46.550] [INFO] Raw frame fed to appsrc successfully, total frames served: 12
[2021-01-01 17:44:46.550] [INFO] feed_data call completed
0:00:34.621371618  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:46.550] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:46.550] [INFO] appsrc: 0x7f7805ca10, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:46.550] [INFO] Calling feed_data...
[2021-01-01 17:44:46.550] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:46.550] [INFO] appsrc: 0x7f7805ca10
[2021-01-01 17:44:46.550] [INFO] DDS reader is initialized
[2021-01-01 17:44:46.550] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 17:44:46.553] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:46.553] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:44:46.553] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:46.554] [INFO] Pushing raw buffer directly to appsrc...
0:00:34.625230129  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f482f3490
0:00:34.625251807  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:46.554] [INFO] Raw frame fed to appsrc successfully, total frames served: 13
[2021-01-01 17:44:46.554] [INFO] feed_data call completed
0:00:34.625294907  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 17:44:46.554] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 17:44:46.554] [INFO] appsrc: 0x7f7805ca10, unused: 4096, user_data: 0x55853ece20
[2021-01-01 17:44:46.554] [INFO] Calling feed_data...
[2021-01-01 17:44:46.554] [INFO] === FEED DATA CALLED ===
[2021-01-01 17:44:46.554] [INFO] appsrc: 0x7f7805ca10
[2021-01-01 17:44:46.554] [INFO] DDS reader is initialized
[2021-01-01 17:44:46.554] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:34.639446117  1717   0x7f84001a40 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f780609b0 on task 0x7f540028f0
0:00:34.639474431  1717   0x7f84001a40 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc1:src> created task 0x7f540028f0
0:00:34.643425694  1717   0x7f84001e60 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:34.645296694  1717   0x7f84001e60 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse1> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:34.645422044  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:34.645468444  1717   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:34.645566919  1717   0x7f84001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:34.645921269  1717   0x7f84001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2137577396, timestamp-offset=(uint)586540, seqnum-offset=(uint)1520, a-framerate=(string)30
0:00:34.645997019  1717   0x7f84001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:34.646044719  1717   0x7f84001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:34.646085244  1717   0x7f84001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:34.646157069  1717   0x7f84001e60 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f78068950: new sender source 0x7f380107a0
0:00:34.646253944  1717   0x7f84001e60 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2137577396, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)1520, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:34.646318044  1717   0x7f84001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:34.646379119  1717   0x7f84001e60 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f78068950 received caps 0x7f3800c7c0, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2137577396, timestamp-offset=(uint)586540, seqnum-offset=(uint)1520, a-framerate=(string)30
0:00:34.646803294  1717   0x7f84001e60 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession1> Can't determine running time for this packet without knowing configured latency
0:00:34.646873219  1717   0x7f84001e60 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin1:send_rtp_src_0> Now blocking
0:00:34.646901794  1717   0x7f84001e60 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f78068950> position: 447089:44:46.341288000
0:00:34.646987579  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f78067db0> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:34.647004733  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:34.647013994  1717   0x7f84001830 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:34.647023143  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:34.647053519  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:34.647077819  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f78067db0 is prerolled
0:00:34.647115369  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:34.647175169  1717   0x7f84000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:34.647246619  1717   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:34.647269244  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f78067db0 no suspend
0:00:34.647284419  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:34.647311619  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://***********5:8554/stream/
0:00:34.650663344  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request SETUP rtsp://***********5:8554/stream/stream=0 1.0
0:00:34.650708069  1717   0x7f84000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x55854b4280 for path /stream/stream=0
0:00:34.650735969  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f78067db0 for path /stream
0:00:34.650751694  1717   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:34.650769269  1717   0x7f84000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f78067db0 was not prepared
0:00:34.650815919  1717   0x7f84000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f7806a8b0
0:00:34.650840744  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f7806a8b0
0:00:34.650895019  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP/TCP;unicast;interleaved=0-1
0:00:34.650918594  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:34.650932019  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:34.650954844  1717   0x7f84000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f78067db0 in session 0x7f7806a9b0
0:00:34.655144944  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request PLAY rtsp://***********5:8554/stream/ 1.0
0:00:34.655234194  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f78067db0> complete pipeline
0:00:34.655254219  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f78068950> complete stream
0:00:34.655274894  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f78068950> create receiver part
0:00:34.655311669  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f78068950> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:34.655334069  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:34.655417369  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f7806b3d0> adding pad 'src'
0:00:34.655477419  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel1:src
0:00:34.655513944  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel1:src and rtpbin1:recv_rtcp_sink_0
0:00:34.655577094  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel1:src and rtpbin1:recv_rtcp_sink_0, successful
0:00:34.655592519  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.655609944  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel1:src> Received event on flushing pad. Discarding
0:00:34.655637919  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:34.655686669  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f7806bac0> adding pad 'src'
0:00:34.655748019  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsrc0:src
0:00:34.655781594  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsrc0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:34.655805694  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:34.655846694  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsrc0> continue state change READY to PAUSED, final PLAYING
0:00:34.655873919  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<appsrc0> starting
0:00:34.655911294  1717   0x7f84000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<appsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:34.655949719  1717   0x7f84000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f7806be30 on task 0x7f7806c130
0:00:34.655970869  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<appsrc0:src> created task 0x7f7806c130
0:00:34.656278719  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:34.656302244  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:34.656335469  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:34.656348694  1717   0x7f84002070 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "appsrc0"
0:00:34.656381194  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsrc0> completed state change to PLAYING
0:00:34.656389094  1717   0x7f84002070 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<appsrc0:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:34.656401869  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.656452144  1717   0x7f84002070 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<appsrc0:src> pad has no peer
0:00:34.656496119  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel1> adding pad 'funnelpad2'
0:00:34.656529444  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link appsrc0:src and funnel1:funnelpad2
0:00:34.656552519  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<appsrc0> caps: (NULL)
0:00:34.656612644  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked appsrc0:src and funnel1:funnelpad2, successful
0:00:34.656628269  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.656681994  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel1> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:34.656701569  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed NULL to READY (PLAYING pending)
0:00:34.656729344  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel1> continue state change READY to PAUSED, final PLAYING
0:00:34.656756794  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:34.656774544  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:34.656800994  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel1> continue state change PAUSED to PLAYING, final PLAYING
0:00:34.656817469  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel1> completed state change to PLAYING
0:00:34.656834544  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.656864844  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f78068950> create sender part
0:00:34.656886044  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f78068950> tcp: 1, udp: 0, mcast: 0 (ttl: 0)
0:00:34.656922944  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:34.656981444  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f7806c6f0> adding pad 'sink'
0:00:34.657040944  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsink"
0:00:34.657304219  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f7806ec70> adding pad 'sink'
0:00:34.657348569  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3472:plug_tcp_sink:<GstRTSPStream@0x7f78068950> plug tcp sink
0:00:34.657400694  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee2> adding pad 'src_0'
0:00:34.657420719  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsink0:sink
0:00:34.657448919  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee2:src_0 and appsink0:sink
0:00:34.657473169  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee2:sink> pad has no peer
0:00:34.657505819  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee2:src_0 and appsink0:sink, successful
0:00:34.657520719  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.657535894  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee2:src_0> Received event on flushing pad. Discarding
0:00:34.657570694  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:34.657590494  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:34.657622119  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsink0> continue state change READY to PAUSED, final PLAYING
0:00:34.657644719  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:34.657686844  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee2> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:34.657688299  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78067db0: went from PAUSED to PAUSED (pending PAUSED)
0:00:34.657708894  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed NULL to READY (PAUSED pending)
0:00:34.657757769  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee2> continue state change READY to PAUSED, final PAUSED
0:00:34.657780894  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee2> completed state change to PAUSED
0:00:34.657798544  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.657819944  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee2:sink
0:00:34.657845769  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin1:send_rtp_src_0 and tee2:sink
0:00:34.657937494  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin1:send_rtp_src_0 and tee2:sink, successful
0:00:34.657952819  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.658028969  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:34.658082594  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f780705c0> adding pad 'sink'
0:00:34.658128869  1717   0x7f84000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsink"
0:00:34.658162669  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f78070ed0> adding pad 'sink'
0:00:34.658201894  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:3472:plug_tcp_sink:<GstRTSPStream@0x7f78068950> plug tcp sink
0:00:34.658245594  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee3> adding pad 'src_0'
0:00:34.658265194  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsink1:sink
0:00:34.658290044  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee3:src_0 and appsink1:sink
0:00:34.658311394  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee3:sink> pad has no peer
0:00:34.658340819  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee3:src_0 and appsink1:sink, successful
0:00:34.658355119  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.658368944  1717   0x7f84000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee3:src_0> Received event on flushing pad. Discarding
0:00:34.658403169  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:34.658423069  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:34.658446069  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsink1> continue state change READY to PAUSED, final PAUSED
0:00:34.658471569  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsink1> completed state change to PAUSED
0:00:34.658489819  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.658516094  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee3> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:34.658534144  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed NULL to READY (PAUSED pending)
0:00:34.658555244  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee3> continue state change READY to PAUSED, final PAUSED
0:00:34.658572373  1717   0x7f84001a40 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter1> reuse caps
0:00:34.658577169  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee3> completed state change to PAUSED
0:00:34.658618094  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:34.658640369  1717   0x7f84000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee3:sink
0:00:34.658664919  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin1:send_rtcp_src_0 and tee3:sink
0:00:34.658711019  1717   0x7f84000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin1:send_rtcp_src_0 and tee3:sink, successful
0:00:34.658725844  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:34.658757394  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f78068950> pipeline successfully updated
0:00:34.658775594  1717   0x7f84000b70 INFO              rtspstream rtsp-stream.c:4783:update_transport: adding TCP ***********
0:00:34.658842794  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:34.658858144  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:34.658889969  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f78068950> Enabling rate control
0:00:34.658936094  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:34.658952348  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:34.658991984  1717   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:34.659100194  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f78067db0> seekable:0
0:00:34.659120419  1717   0x7f84000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f78067db0> Handle going back to 0 for none live not seekable streams.
0:00:34.659138194  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:34.659153719  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:34.659167319  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:34.659204594  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:34.659333694  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f78067db0, target state PAUSED
0:00:34.659356644  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:34.659374919  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f78067db0 set blocked 0
0:00:34.659391219  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f78068950> blocked: 0
0:00:34.659416094  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f78067db0 do_state 1
0:00:34.659433294  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f78067db0
0:00:34.659453044  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f78067db0
0:00:34.659474519  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f78067db0 set blocked 0
0:00:34.659490344  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f78068950> blocked: 0
0:00:34.659505969  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f78067db0
0:00:34.659518194  1717   0x7f84001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2137577396, timestamp-offset=(uint)586540, seqnum-offset=(uint)1520, a-framerate=(string)30
0:00:34.659521319  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:34.659622768  1717   0x7f84001830 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f78067db0: got message type 16 (tag)
0:00:34.659649769  1717   0x7f84001e60 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:34.659669694  1717   0x7f84001e60 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:34.659688244  1717   0x7f84001e60 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:34.659725195  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78067db0: went from PAUSED to PAUSED (pending PLAYING)
0:00:34.659754008  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f78067db0> got async-done
0:00:34.659979363  1717   0x7f84002280 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:34.660073154  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:34.660080375  1717   0x7f84002280 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:34.660157749  1717   0x7f84002280 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:34.660194461  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.660210140  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsink1> completed state change to PLAYING
0:00:34.660222955  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.660249396  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink1' changed state to 4(PLAYING) successfully
0:00:34.660265771  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660277539  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink0> skipping transition from PLAYING to  PLAYING
0:00:34.660288546  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink0' changed state to 4(PLAYING) successfully
0:00:34.660303710  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee3> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.660316707  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee3> completed state change to PLAYING
0:00:34.660328569  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.660345475  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee3' changed state to 4(PLAYING) successfully
0:00:34.660360924  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee2> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:34.660373611  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee2> completed state change to PLAYING
0:00:34.660386316  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.660402973  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee2' changed state to 4(PLAYING) successfully
0:00:34.660419192  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660444602  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660456598  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage1> skipping transition from PLAYING to  PLAYING
0:00:34.660468751  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:34.660483809  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660495152  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux1> skipping transition from PLAYING to  PLAYING
0:00:34.660506312  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:34.660520797  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660531828  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession1> skipping transition from PLAYING to  PLAYING
0:00:34.660543353  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:34.660556802  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:34.660570304  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:34.660583751  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660608442  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660620042  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:34.660631663  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:34.660658641  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660670380  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse1> skipping transition from PLAYING to  PLAYING
0:00:34.660683905  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:34.660699159  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660710184  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc1> skipping transition from PLAYING to  PLAYING
0:00:34.660721943  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:34.660736027  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660747508  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:34.660759176  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:34.660774563  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660785429  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale1> skipping transition from PLAYING to  PLAYING
0:00:34.660796968  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:34.660811467  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660822338  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue1> skipping transition from PLAYING to  PLAYING
0:00:34.660834001  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:34.660847066  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660857634  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:34.660868693  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:34.660881493  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:34.660894267  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:34.660908795  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:34.660919690  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel1> skipping transition from PLAYING to  PLAYING
0:00:34.660930578  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel1' changed state to 4(PLAYING) successfully
0:00:34.660944035  1717   0x7f84002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsrc0' changed state to 4(PLAYING) successfully
0:00:34.660958805  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:34.660970854  1717   0x7f84002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:34.661041140  1717   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:34.661105708  1717   0x7f84001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f78067db0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 17:44:46.593] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 17:44:46.593] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 17:44:46.593] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 17:44:46.593] [INFO] Pushing raw buffer directly to appsrc...
0:00:34.664857887  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f482f35b0
0:00:34.664879058  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 17:44:46.593] [INFO] Raw frame fed to appsrc successfully, total frames served: 14
[2021-01-01 17:44:46.593] [INFO] feed_data call completed
0:00:34.664923076  1717   0x7f84001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:36.968805840  1717   0x7f50000df0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:36.968876696  1717   0x7f50000df0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:36.968911227  1717   0x7f50000df0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:36.969165327  1717   0x7f50000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2137577396, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)1520, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400383682201857801, sr-rtptime=(uint)716576733, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:36.969193795  1717   0x7f84001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:36.969200105  1717   0x7f40001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f78068950> message send complete
0:00:36.969253632  1717   0x7f40001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806a8b0 alive
0:00:36.969315995  1717   0x7f84001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:37.501685045  1717   0x7f84000b70 DEBUG             rtspstream rtsp-stream.c:4664:gst_rtsp_stream_recv_rtcp: stream 0x7f78068950: first buffer at time 5:31:40.041011324, base 5:31:37.156129948
0:00:37.501738020  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<appsrc0> queueing buffer 0x7f78052ff0
0:00:37.501764545  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<appsrc0> Currently queued: 36 bytes, 1 buffers, 0:00:00.000000000
0:00:37.501811145  1717   0x7f84002070 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<appsrc0> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:37.501857945  1717   0x7f84002070 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:37.501908320  1717   0x7f84002070 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<appsrc0> marking pending DISCONT
0:00:37.501980345  1717   0x7f84002070 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:37.502135720  1717   0x7f84002070 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f78068950: new source 0x7f3c002160
0:00:37.502288620  1717   0x7f84002070 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2288496633, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:64877, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:37.502357745  1717   0x7f84002070 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:64877 in 1 transports
0:00:37.502378895  1717   0x7f84002070 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f78068950: found transport 0x7f7806aba0 for source  0x7f3c002160
0:00:37.502408345  1717   0x7f84002070 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f78068950: source 0x7f3c002160 for transport 0x7f7806aba0
0:00:37.502439670  1717   0x7f84002070 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f78068950: source 0x7f3c002160 in transport 0x7f7806aba0 is active
0:00:37.502456595  1717   0x7f84002070 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806a8b0 alive
0:00:37.502533495  1717   0x7f84002070 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f78068950: new SDES 0x7f3c002160
0:00:39.243445656  1717   0x7f7c05f8c0 INFO              rtspstream rtsp-stream.c:2496:on_bye_timeout: 0x7f78041a60: source 0x7f580139e0 bye timeout
0:00:39.572996382  1717   0x7f7c05f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3373745737, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)7670, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400383648649440141, sr-rtptime=(uint)1688678821, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 17:44:52.147] [INFO] === RTSP Server Statistics ===
[2021-01-01 17:44:52.147] [INFO] Uptime: 40.0 seconds
[2021-01-01 17:44:52.147] [INFO] Total connections: 2
[2021-01-01 17:44:52.147] [INFO] Active connections: 2
[2021-01-01 17:44:52.147] [INFO] Frames served: 14
[2021-01-01 17:44:52.147] [INFO] Clients connected: 0
[2021-01-01 17:44:52.147] [INFO] Error count: 0
0:00:42.445073237  1717   0x7f50000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2137577396, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)1520, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400383705722056492, sr-rtptime=(uint)717069593, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:42.445077351  1717   0x7f40001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f78068950> message send complete
0:00:42.445142952  1717   0x7f40001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806a8b0 alive
0:00:43.376271745  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<appsrc0> queueing buffer 0x7f78051ad0
0:00:43.376328845  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<appsrc0> Currently queued: 36 bytes, 1 buffers, 0:00:00.000000000
0:00:43.376377920  1717   0x7f84002070 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<appsrc0> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:43.376533645  1717   0x7f84002070 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f78068950: source 0x7f3c002160 in transport 0x7f7806aba0 is active
0:00:43.376565970  1717   0x7f84002070 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806a8b0 alive
0:00:43.536021450  1717   0x7f7c05f8c0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3373745737, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)7670, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400383648649440141, sr-rtptime=(uint)1688678821, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:44.789581423  1717   0x7f50000df0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2137577396, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)1520, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400383705722056492, sr-rtptime=(uint)717069593, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:44.789590216  1717   0x7f40001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f78068950> message send complete
0:00:44.789650259  1717   0x7f40001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f7806a8b0 alive
0:00:45.187042370  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f88006630: received a request TEARDOWN rtsp://***********5:8554/stream/ 1.0
0:00:45.187134620  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f78067db0, target state PLAYING
0:00:45.187155470  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:45.187174520  1717   0x7f84000b70 INFO              rtspstream rtsp-stream.c:4787:update_transport: removing TCP ***********
0:00:45.187201870  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f78067db0 do_state 1
0:00:45.187218795  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f78067db0
0:00:45.187237120  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f78067db0
0:00:45.187256295  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:45.187272645  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:45.187292545  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f78067db0
0:00:45.187487470  1717   0x7f84000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:45.187592695  1717   0x7f84000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:45.187650545  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.187670370  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink1> skipping transition from PLAYING to  PLAYING
0:00:45.187689095  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink1' changed state to 4(PLAYING) successfully
0:00:45.187716920  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.187738795  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink0> skipping transition from PLAYING to  PLAYING
0:00:45.187759645  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink0' changed state to 4(PLAYING) successfully
0:00:45.187788070  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee3> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.187809995  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee3> skipping transition from PLAYING to  PLAYING
0:00:45.187832845  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee3' changed state to 4(PLAYING) successfully
0:00:45.187859420  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee2> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.187881320  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee2> skipping transition from PLAYING to  PLAYING
0:00:45.187901420  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee2' changed state to 4(PLAYING) successfully
0:00:45.187929295  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.187970570  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.187992870  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage1> skipping transition from PLAYING to  PLAYING
0:00:45.188016220  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:45.188044320  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188066470  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux1> skipping transition from PLAYING to  PLAYING
0:00:45.188089845  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:45.188116995  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188139345  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession1> skipping transition from PLAYING to  PLAYING
0:00:45.188160295  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:45.188187095  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:45.188209420  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:45.188235095  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188278070  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188300220  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:45.188317770  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:45.188341945  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188364995  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse1> skipping transition from PLAYING to  PLAYING
0:00:45.188384920  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:45.188411195  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188432645  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc1> skipping transition from PLAYING to  PLAYING
0:00:45.188452345  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:45.188478945  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188500495  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:45.188519970  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:45.188546770  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188568720  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale1> skipping transition from PLAYING to  PLAYING
0:00:45.188591570  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:45.188618395  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188652220  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue1> skipping transition from PLAYING to  PLAYING
0:00:45.188671945  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:45.188692570  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188711745  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:45.188732370  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:45.188755620  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:45.188777495  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:45.188806995  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:45.188825745  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel1> skipping transition from PLAYING to  PLAYING
0:00:45.188850946  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel1' changed state to 4(PLAYING) successfully
0:00:45.188872171  1717   0x7f84000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsrc0' changed state to 4(PLAYING) successfully
0:00:45.188898821  1717   0x7f84000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:45.188915971  1717   0x7f84000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:45.188962971  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<appsrc0> queue event: eos event: 0x7f78073fa0, time 99:99:99.999999999, seq-num 352, (NULL)
0:00:45.189007271  1717   0x7f84002070 DEBUG                 appsrc gstappsrc.c:1683:gst_app_src_create:<appsrc0> pop event eos event: 0x7f78073fa0, time 99:99:99.999999999, seq-num 352, (NULL)
0:00:45.189012621  1717   0x7f84000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f78073fa0, time 99:99:99.999999999, seq-num 352, (NULL)
0:00:45.189087371  1717   0x7f84000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f7806a9b0
0:00:45.189112321  1717   0x7f84000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f78067db0 was not prepared
0:00:45.189131171  1717   0x7f84000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f78067db0 is already unpreparing
0:00:45.189172071  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f88006630: sending close message
0:00:45.189323146  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f88006630: session 0x7f7806a8b0 removed
0:00:45.189347796  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f88006630: unwatch session 0x7f7806a8b0
0:00:45.189384771  1717   0x7f84000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f7806a8b0
0:00:45.189521621  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f88006630: connection closed
0:00:45.189550921  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f88006630: watch destroyed
0:00:45.189585971  1717   0x7f84000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x55854b1e70> unmanage client 0x7f88006630
0:00:45.189622246  1717   0x7f84000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f88007550
0:00:45.189648096  1717   0x7f84000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f88006630
0:00:45.189807196  1717   0x7f84000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f88008020