root@rk3576-buildroot:/# rtsp_server --hw-encoder --gst-debug 4
[2021-01-01 14:01:23.144] [INFO] === RTSP Server Configuration ===
[2021-01-01 14:01:23.144] [INFO] DDS Topic: Video_Frames
[2021-01-01 14:01:23.144] [INFO] Server: 0.0.0.0:8554/stream
[2021-01-01 14:01:23.144] [INFO] Output: 1280x720@30fps, H264, 2000000 bps
[2021-01-01 14:01:23.144] [INFO] GOP Size: 15
[2021-01-01 14:01:23.144] [INFO] Hardware Encoder: yes
[2021-01-01 14:01:23.144] [INFO] Max Clients: 10
[2021-01-01 14:01:23.144] [INFO] Buffer Size: 5
[2021-01-01 14:01:23.144] [INFO] Zero Copy: yes
[2021-01-01 14:01:23.144] [INFO] GStreamer Debug Level: 4
[2021-01-01 14:01:23.144] [INFO] Adaptive Bitrate: 500000 - 5000000 bps
[2021-01-01 14:01:23.145] [INFO] GStreamer debug level set to: 4 (*:4)
0:00:00.000079362  1525   0x5569f228f0 INFO                GST_INIT gst.c:576:init_pre: Initializing GStreamer Core Library version 1.22.9
0:00:00.000096722  1525   0x5569f228f0 INFO                GST_INIT gst.c:577:init_pre: Using library installed in /lib
0:00:00.000109313  1525   0x5569f228f0 INFO                GST_INIT gst.c:595:init_pre: Linux rk3576-buildroot 6.1.99-rk3576 #1 SMP Mon Jun 30 10:03:13 CST 2025 aarch64
0:00:00.000334742  1525   0x5569f228f0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.000850412  1525   0x5569f228f0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001069332  1525   0x5569f228f0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001172407  1525   0x5569f228f0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001187018  1525   0x5569f228f0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001247164  1525   0x5569f228f0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007445240  1525   0x5569f228f0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.006160 seconds
0:00:00.007514835  1525   0x5569f228f0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.008111467  1525   0x5569f228f0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.008129327  1525   0x5569f228f0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.008142920  1525   0x5569f228f0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.008153122  1525   0x5569f228f0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.008160438  1525   0x5569f228f0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
[2021-01-01 14:01:23.153] [INFO] GStreamer initialized successfully
[2021-01-01 14:01:23.153] [INFO] Media factory configured: shared=FALSE, eos_shutdown=TRUE
[2021-01-01 14:01:23.153] [INFO] Supported protocols: UDP, UDP_MCAST, TCP
Start init DDS reader: Video_Frames
Create share memery qos success
Create participant success
Register type success
Create subscriber success
Create topic success
DDS Reader initialized for topic: Video_Frames[2021-01-01 14:01:23.159] [INFO] Waiting for first frame from DDS topic: Video_Frames
Subscriber matched
[2021-01-01 14:01:23.359] [INFO] First frame received: 640x480 format=1448695129, output will be: 1280x720@30fps
[2021-01-01 14:01:23.359] [INFO] RTSPMediaFactory initialized for topic: Video_Frames
[2021-01-01 14:01:23.359] [INFO] Pipeline: ( appsrc name=source is-live=true do-timestamp=false format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )
0:00:00.214646206  1525   0x5569f228f0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x556a002460 for path /stream

(<unknown>:1525): GLib-GObject-CRITICAL **: 14:01:23.359: g_object_set_is_valid_property: object class 'GstRTSPServer' has no property named 'timeout'
[2021-01-01 14:01:23.359] [INFO] RTSP server initialized: 0.0.0.0:8554/stream
0:00:00.214809414  1525   0x5569f228f0 DEBUG             rtspserver rtsp-server.c:882:gst_rtsp_server_create_socket:<GstRTSPServer@0x556a000050> getting address info of 0.0.0.0/8554
0:00:00.215395846  1525   0x5569f228f0 DEBUG             rtspserver rtsp-server.c:967:gst_rtsp_server_create_socket:<GstRTSPServer@0x556a000050> opened sending server socket
0:00:00.215442919  1525   0x5569f228f0 DEBUG             rtspserver rtsp-server.c:994:gst_rtsp_server_create_socket:<GstRTSPServer@0x556a000050> listening on server socket 0x556a1520f0 with queue of 5
[2021-01-01 14:01:23.360] [INFO] RTSP server started on 0.0.0.0:8554/stream
[2021-01-01 14:01:23.360] [INFO] RTSP server is running. Access stream at: rtsp://0.0.0.0:8554/stream
[2021-01-01 14:01:23.360] [INFO] Press Ctrl+C to stop the server
[2021-01-01 14:01:23.360] [INFO] RTSP server main loop started
[2021-01-01 14:01:33.361] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:01:33.361] [INFO] Uptime: 10.0 seconds
[2021-01-01 14:01:33.361] [INFO] Total connections: 0
[2021-01-01 14:01:33.361] [INFO] Active connections: 0
[2021-01-01 14:01:33.361] [INFO] Frames served: 0
[2021-01-01 14:01:33.361] [INFO] Clients connected: 0
[2021-01-01 14:01:33.361] [INFO] Error count: 0
[2021-01-01 14:01:43.362] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:01:43.362] [INFO] Uptime: 20.0 seconds
[2021-01-01 14:01:43.362] [INFO] Total connections: 0
[2021-01-01 14:01:43.362] [INFO] Active connections: 0
[2021-01-01 14:01:43.362] [INFO] Frames served: 0
[2021-01-01 14:01:43.362] [INFO] Clients connected: 0
[2021-01-01 14:01:43.362] [INFO] Error count: 0
0:00:22.372499651  1525   0x7f98000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f98006630 connected to server ip ***********5, ipv6 = 0
0:00:22.372529277  1525   0x7f98000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f98006630 ip ***********:50975
0:00:22.372545313  1525   0x7f98000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x556a000050> manage client 0x7f98006630
[2021-01-01 14:01:45.517] [INFO] === CLIENT CONNECTED ===
[2021-01-01 14:01:45.517] [INFO] Active connections: 1, Total connections: 1
0:00:22.372897012  1525   0x7f98000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f98006630: attaching to context 0x7f98007440
0:00:22.372911436  1525   0x7f94000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f98006f10
0:00:22.382719361  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request OPTIONS rtsp://***********5:8554/stream 1.0
0:00:22.384630111  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request DESCRIBE rtsp://***********5:8554/stream 1.0
0:00:22.384734561  1525   0x7f94000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556a002460 for path /stream
0:00:22.384761186  1525   0x7f94000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=false format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:22.385600861  1525   0x7f94000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstapp.so" loaded
0:00:22.386064061  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:22.386125586  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f88007680> adding pad 'src'
0:00:22.386158536  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 0
0:00:22.386206161  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:22.388060586  1525   0x7f94000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:22.388303011  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:22.388377061  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f8800d010> adding pad 'sink'
0:00:22.388419811  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f8800d010> adding pad 'src'
0:00:22.389477961  1525   0x7f94000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoconvertscale.so" loaded
0:00:22.390081686  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:22.390146286  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f88017830> adding pad 'sink'
0:00:22.390194611  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f88017830> adding pad 'src'
0:00:22.393557461  1525   0x7f94000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:22.393966211  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:22.394028261  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f8801bc60> adding pad 'sink'
0:00:22.394061436  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f8801bc60> adding pad 'src'
0:00:22.396224236  1525   0x7f94000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:00:22.396440136  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:22.396495086  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f88024710> adding pad 'sink'
0:00:22.396529711  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f88024710> adding pad 'src'
0:00:22.396575011  1525   0x7f94000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f88024710> PTS interpolation: no
0:00:22.396593511  1525   0x7f94000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f88024710> TS inferring: no
0:00:22.399348286  1525   0x7f94000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:22.399681861  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:22.399755661  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f88034580> adding pad 'src'
0:00:22.399812786  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f88034580> adding pad 'sink'
0:00:22.399887511  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:22.400029111  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue0 (0/0) with caps "(NULL)"
0:00:22.400054461  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue0:(any)
0:00:22.400092211  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue0:sink
0:00:22.400121311  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:22.400152311  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:22.400178361  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue0 in same bin, no need for ghost pads
0:00:22.400211411  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue0:sink
0:00:22.400230261  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:22.400262661  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:22.400288761  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue0:sink, successful
0:00:22.400302511  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.400318336  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:22.400366211  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue0 to some pad of GstVideoScale named videoscale0 (0/0) with caps "(NULL)"
0:00:22.400387861  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue0:(any) to element videoscale0:(any)
0:00:22.400411361  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue0:src and videoscale0:sink
0:00:22.400432386  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:22.400457286  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:22.401171261  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue0 and videoscale0 in same bin, no need for ghost pads
0:00:22.401201686  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue0:src and videoscale0:sink
0:00:22.401225561  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:22.401249411  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:22.401896411  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue0:src and videoscale0:sink, successful
0:00:22.401914711  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.401931011  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue0:src> Received event on flushing pad. Discarding
0:00:22.401986586  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:22.402013736  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:22.402164386  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f8803dfc0> adding pad 'sink'
0:00:22.402200561  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f8803dfc0> adding pad 'src'
0:00:22.402227986  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:22.402280111  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:22.402302786  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.402329236  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale0:(any) to element capsfilter0:sink
0:00:22.402347386  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:22.402365736  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale0 and capsfilter0 in same bin, no need for ghost pads
0:00:22.402391061  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale0:src and capsfilter0:sink
0:00:22.402422286  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:22.403094511  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:22.403163436  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale0:src and capsfilter0:sink, successful
0:00:22.403178161  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.403194111  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale0:src> Received event on flushing pad. Discarding
0:00:22.403225761  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mpph264enc0:(any)
0:00:22.403243011  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:22.403264686  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mpph264enc0:sink
0:00:22.403294486  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:22.404065211  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:22.404149111  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mpph264enc0 in same bin, no need for ghost pads
0:00:22.404177436  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mpph264enc0:sink
0:00:22.404205261  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:22.404973311  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:22.405088661  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mpph264enc0:sink, successful
0:00:22.405103411  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.405117886  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:22.405157086  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:00:22.405182361  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:00:22.405204661  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:00:22.405248161  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:22.405277386  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:00:22.405302611  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:00:22.405324236  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:22.405352936  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:00:22.405366561  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.405380586  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:22.405413386  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:22.405434636  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:00:22.405455986  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:00:22.405483511  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:22.405510911  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:00:22.405537111  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:00:22.405560111  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:22.405583386  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:00:22.405596136  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.405609336  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:00:22.405829086  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:22.405859536  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f88034580
0:00:22.405892611  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:22.405949411  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f8803fbb0: creating stream with index 0 and payloader <pay0>
0:00:22.406144836  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:22.406167811  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:22.406181161  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.406196411  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:22.406228436  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:22.406298811  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f88041a60
0:00:22.406322636  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f88041a60> set retransmission time 0
0:00:22.406339686  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f88041a60> Enabling rate control
0:00:22.406374611  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f88041a60> set retransmission pt 97
0:00:22.406396236  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:22.406429336  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:22.406457136  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:22.406483511  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:22.406509786  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:22.406544936  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:22.406719861  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f88041a60> set retransmission time 0
[2021-01-01 14:01:45.551] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 14:01:45.551] [INFO] factory: 0x556a002460, media: 0x7f8803fbb0, user_data: 0x5569f3ae20
[2021-01-01 14:01:45.551] [INFO] Calling configure_media...
[2021-01-01 14:01:45.551] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 14:01:45.551] [INFO] Got media pipeline: 0x7f880361d0
0:00:22.406856336  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element source
[2021-01-01 14:01:45.551] [INFO] Found appsrc element: 0x7f88007680
[2021-01-01 14:01:45.552] [INFO] Connected appsrc signals
0:00:22.406945911  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 545460846592
0:00:22.406966686  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:22.406995611  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:22.407016111  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 14:01:45.552] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 14:01:45.552] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:22.407700361  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f8802edb0
0:00:22.407754761  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 14:01:45.552] [INFO] Initial frame push result: 0
[2021-01-01 14:01:45.552] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 14:01:45.552] [INFO] Configured appsrc properties
[2021-01-01 14:01:45.553] [INFO] Pipeline latency query result: live=0, min=0 ns, max=18446744073709551615 ns
[2021-01-01 14:01:45.553] [INFO] Setting manual latency: min=33333333 ns, max=100000000 ns
0:00:22.407945211  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.033333333
0:00:22.407996486  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
[2021-01-01 14:01:45.553] [INFO] Sent latency event to pipeline
[2021-01-01 14:01:45.553] [INFO] Media configured successfully
[2021-01-01 14:01:45.553] [INFO] configure_media call completed
0:00:22.408108786  1525   0x7f94000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f8803fbb0 for url /stream
0:00:22.408225461  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f8803fbb0
0:00:22.408244761  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:22.408262147  1525   0x7f94000d80 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f88039670
0:00:22.410458086  1525   0x7f94000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:22.410493311  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:22.411328136  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:22.411349011  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:22.411368376  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:22.411393990  1525   0x7f94000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:22.411441509  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:22.411460906  1525   0x7f94000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:22.411491610  1525   0x7f94000d80 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f88041a60 joining bin as session 0
0:00:22.411520018  1525   0x7f94000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:22.411955038  1525   0x7f94000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:22.412060026  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f8c00c3b0> adding pad 'sink'
0:00:22.412083900  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f8c00c3b0> adding pad 'rtcp_sink'
0:00:22.412101649  1525   0x7f94000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:22.412190931  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f8c00d830> adding pad 'src'
0:00:22.412205140  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f8c00d830> adding pad 'sink'
0:00:22.412316591  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:22.412332118  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:22.412345123  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:22.412391768  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:22.412426750  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:22.412441660  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:22.412489955  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:22.412505462  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:22.412515383  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.412549926  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:22.412581234  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:22.412594552  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:22.412604701  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.412626632  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:22.412649892  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:22.412692566  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:22.412703212  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.412718691  1525   0x7f94000d80 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:22.412738807  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:22.412914842  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:22.412963232  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:22.412977326  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:22.412987516  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.413015227  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:22.413056528  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:22.413083876  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:22.413102969  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:22.413115107  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:22.413131641  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:22.413145108  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:22.413154573  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.413199337  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:22.413213542  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:22.413222832  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.413239941  1525   0x7f94000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:22.413280263  1525   0x7f94000d80 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f88041a60> successfully joined bin
0:00:22.413301869  1525   0x7f94000d80 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f8803fbb0
0:00:22.413314795  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f8803fbb0 set blocked 1
0:00:22.413327818  1525   0x7f94000d80 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f88041a60> blocked: 1
0:00:22.413343024  1525   0x7f94000d80 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f8803fbb0
0:00:22.413356323  1525   0x7f94000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f8803fbb0
0:00:22.413385396  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413420178  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413434030  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:22.413445723  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413470754  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:22.413486808  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413499524  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:22.413510474  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413526522  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:22.413540902  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413553341  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:22.413564164  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413586512  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:22.413601025  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:22.413612720  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413629719  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:22.413643254  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413679861  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413694256  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:22.413706195  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413722644  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:22.413737166  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413750371  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:22.413762562  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413778615  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:22.413793273  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413806986  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:22.413818444  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413834351  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:22.413849401  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413861952  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:22.413885650  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413902395  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:22.413917636  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413930278  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:22.413941585  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.413960601  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:22.413975615  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current NULL pending VOID_PENDING, desired next READY
0:00:22.413988085  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:22.413998970  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.414014126  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:22.414027312  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:22.414039377  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:22.414050169  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.414065936  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:22.414079375  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:22.414090876  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.414106915  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:22.414121656  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:22.414133355  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:22.414147061  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:22.414167158  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.414195850  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.414213809  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:22.414225811  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.414246568  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:22.414261579  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.414279247  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:22.414291414  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.414308735  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:22.414323283  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.414340332  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:22.414352068  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.414367785  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:22.414384084  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:22.414395803  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.414411194  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:22.414424648  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.414453426  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.414476090  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:22.414488218  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.414503520  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:22.414518157  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.414771706  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:22.414788373  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.414807369  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:22.414824479  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.415208227  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:22.415228729  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.415262265  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:22.415283822  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.415306714  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:22.415319023  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.415335862  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:22.415352289  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.415371756  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:22.415383785  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.415400274  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:22.415415789  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.415450484  1525   0x7f94000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f8800d820 on task 0x7f8c05f820
0:00:22.415465064  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue0:src> created task 0x7f8c05f820
0:00:22.415587687  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:22.415603445  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.415624367  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:22.415640046  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:22.415656967  1525   0x7f94000d80 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:22.415695226  1525   0x7f94000d80 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:22.415723160  1525   0x7f94000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f880079f0 on task 0x7f8c05f5e0
0:00:22.415736770  1525   0x7f94000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f8c05f5e0
0:00:22.415832334  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:22.415848279  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.415867018  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:22.415872985  1525   0x7f94000ff0 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:22.415889921  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:22.415915573  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.415915502  1525   0x7f94000ff0 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:22.415935367  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:22.415965040  1525   0x7f94000d80 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:22.415977074  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:22.415988839  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.416005001  1525   0x7f94000d80 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f8803fbb0
0:00:22.416016699  1525   0x7f94000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f8803fbb0
0:00:22.416081263  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:22.416103268  1525   0x7f94000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:22.416127818  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416151031  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416164321  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:22.416175675  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416196591  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:22.416211887  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416224922  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:22.416236162  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416252331  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:22.416266617  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416329576  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:22.416344518  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416364837  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:22.416380391  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:22.416392471  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416411496  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:22.416442963  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416458058  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:22.416470451  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416498394  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:22.416514326  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416527280  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:00:22.416538268  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416554095  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:22.416568112  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416581233  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:22.416592288  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416607346  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:22.416621461  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416633594  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:22.416644462  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416659808  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:22.416673833  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416686312  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PLAYING
0:00:22.416696983  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416712510  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:22.416726670  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.416740295  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PLAYING
0:00:22.416751974  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416768906  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:22.416788140  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:22.416801854  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416820378  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:22.416825639  1525   0x7f94000ff0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:22.416836236  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:22.416882592  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416902452  1525   0x7f94000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:22.416917169  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:22.416929427  1525   0x7f94000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.416956270  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:22.416985727  1525   0x7f94000ff0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:22.417010242  1525   0x7f94000ff0 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 14:01:45.562] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:45.562] [INFO] appsrc: 0x7f88007680, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:45.562] [INFO] Calling feed_data...
0:00:22.417081743  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8803fbb0: went from NULL to READY (pending PAUSED)
[2021-01-01 14:01:45.562] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:45.562] [INFO] appsrc: 0x7f88007680
[2021-01-01 14:01:45.562] [INFO] DDS reader is initialized
[2021-01-01 14:01:45.562] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:45.562] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:45.562] [INFO] Creating GstBuffer for raw frame data...
0:00:22.417254764  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8803fbb0: went from READY to PAUSED (pending VOID_PENDING)
0:00:22.417289726  1525   0x7f94000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f8803fbb0: got message type 2048 (new-clock)
0:00:22.417432234  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8803fbb0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 14:01:45.562] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:45.562] [INFO] Pushing raw buffer directly to appsrc...
0:00:22.417734606  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f84002580
0:00:22.417775517  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:45.562] [INFO] Raw frame fed to appsrc successfully, total frames served: 2
[2021-01-01 14:01:45.562] [INFO] feed_data call completed
0:00:22.417818971  1525   0x7f94000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:22.417824358  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:45.562] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:45.563] [INFO] appsrc: 0x7f88007680, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:45.563] [INFO] Calling feed_data...
[2021-01-01 14:01:45.563] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:45.563] [INFO] appsrc: 0x7f88007680
[2021-01-01 14:01:45.563] [INFO] DDS reader is initialized
[2021-01-01 14:01:45.563] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:45.563] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:45.563] [INFO] Creating GstBuffer for raw frame data...
0:00:22.418120922  1525   0x7f94000de0 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:22.418171791  1525   0x7f94000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:22.418462962  1525   0x7f94000de0 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applying YUY2 1280x720 (2560x720)
[2021-01-01 14:01:45.563] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:45.563] [INFO] Pushing raw buffer directly to appsrc...
0:00:22.418540173  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f8412f800
0:00:22.418561013  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:45.563] [INFO] Raw frame fed to appsrc successfully, total frames served: 3
[2021-01-01 14:01:45.563] [INFO] feed_data call completed
0:00:22.418619444  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:45.563] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:45.563] [INFO] appsrc: 0x7f88007680, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:45.563] [INFO] Calling feed_data...
[2021-01-01 14:01:45.563] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:45.563] [INFO] appsrc: 0x7f88007680
[2021-01-01 14:01:45.563] [INFO] DDS reader is initialized
[2021-01-01 14:01:45.563] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:45.563] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:45.563] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:01:45.564] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:45.564] [INFO] Pushing raw buffer directly to appsrc...
0:00:22.419429846  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f841c6480
0:00:22.419455530  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:45.564] [INFO] Raw frame fed to appsrc successfully, total frames served: 4
[2021-01-01 14:01:45.564] [INFO] feed_data call completed
0:00:22.419547980  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:45.564] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:45.564] [INFO] appsrc: 0x7f88007680, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:45.564] [INFO] Calling feed_data...
[2021-01-01 14:01:45.564] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:45.564] [INFO] appsrc: 0x7f88007680
[2021-01-01 14:01:45.564] [INFO] DDS reader is initialized
[2021-01-01 14:01:45.564] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:45.564] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:45.564] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:01:45.565] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:45.565] [INFO] Pushing raw buffer directly to appsrc...
0:00:22.420344647  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f8425ce40
0:00:22.420365171  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:45.565] [INFO] Raw frame fed to appsrc successfully, total frames served: 5
[2021-01-01 14:01:45.565] [INFO] feed_data call completed
0:00:22.420677818  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:45.565] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:45.565] [INFO] appsrc: 0x7f88007680, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:45.565] [INFO] Calling feed_data...
[2021-01-01 14:01:45.565] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:45.565] [INFO] appsrc: 0x7f88007680
[2021-01-01 14:01:45.565] [INFO] DDS reader is initialized
[2021-01-01 14:01:45.565] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:45.565] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:45.565] [INFO] Creating GstBuffer for raw frame data...
0:00:22.421001132  1525   0x7f94000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
[2021-01-01 14:01:45.566] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:45.566] [INFO] Pushing raw buffer directly to appsrc...
0:00:22.421472091  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f842f3070
0:00:22.421496162  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:45.566] [INFO] Raw frame fed to appsrc successfully, total frames served: 6
[2021-01-01 14:01:45.566] [INFO] feed_data call completed
0:00:22.421552796  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:45.566] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:45.566] [INFO] appsrc: 0x7f88007680, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:45.566] [INFO] Calling feed_data...
[2021-01-01 14:01:45.566] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:45.566] [INFO] appsrc: 0x7f88007680
[2021-01-01 14:01:45.566] [INFO] DDS reader is initialized
[2021-01-01 14:01:45.566] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:22.439890492  1525   0x7f94000de0 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f8801c3c0 on task 0x7f80036180
0:00:22.439917852  1525   0x7f94000de0 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f80036180
0:00:22.443663386  1525   0x7f94001200 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:22.445811611  1525   0x7f94001200 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse0> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:22.445932687  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:22.445962902  1525   0x7f94000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:22.446146211  1525   0x7f94001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:22.446578086  1525   0x7f94001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)3751509392, timestamp-offset=(uint)3529822196, seqnum-offset=(uint)18972, a-framerate=(string)30
0:00:22.446667636  1525   0x7f94001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:22.446749786  1525   0x7f94001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:22.446792036  1525   0x7f94001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:22.447000011  1525   0x7f94001200 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f88041a60: new sender source 0x7f74011060
0:00:22.447132611  1525   0x7f94001200 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3751509392, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)18972, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:22.447207011  1525   0x7f94001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:22.447296011  1525   0x7f94001200 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f88041a60 received caps 0x7f7400cda0, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)3751509392, timestamp-offset=(uint)3529822196, seqnum-offset=(uint)18972, a-framerate=(string)30
0:00:22.447777311  1525   0x7f94001200 INFO               videometa gstvideometa.c:1100:gst_video_time_code_meta_api_get_type: registering
0:00:22.447950786  1525   0x7f94001200 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:00:22.448070036  1525   0x7f94001200 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin0:send_rtp_src_0> Now blocking
0:00:22.448124636  1525   0x7f94001200 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f88041a60> position: 447086:01:45.389382000
0:00:22.448202189  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f8803fbb0> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:22.448217149  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:22.448226193  1525   0x7f94000d80 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:22.448235679  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:22.448261753  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:22.448277164  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f8803fbb0 is prerolled
0:00:22.448303875  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:22.448355905  1525   0x7f94000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:22.448423588  1525   0x7f94000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:22.448436693  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f8803fbb0 no suspend
0:00:22.448446798  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:22.448465423  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://***********5:8554/stream/
[2021-01-01 14:01:45.599] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:45.599] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:01:45.600] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:45.600] [INFO] Pushing raw buffer directly to appsrc...
0:00:22.455005798  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f843893f0
0:00:22.455028488  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:45.600] [INFO] Raw frame fed to appsrc successfully, total frames served: 7
[2021-01-01 14:01:45.600] [INFO] feed_data call completed
0:00:22.455072457  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:45.600] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:45.600] [INFO] appsrc: 0x7f88007680, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:45.600] [INFO] Calling feed_data...
[2021-01-01 14:01:45.600] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:45.600] [INFO] appsrc: 0x7f88007680
[2021-01-01 14:01:45.600] [INFO] DDS reader is initialized
[2021-01-01 14:01:45.600] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:22.468207513  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request SETUP rtsp://***********5:8554/stream/stream=0 1.0
0:00:22.468238356  1525   0x7f94000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556a002460 for path /stream/stream=0
0:00:22.468257897  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f8803fbb0 for path /stream
0:00:22.468270659  1525   0x7f94000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:22.468284085  1525   0x7f94000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f8803fbb0 was not prepared
0:00:22.468366174  1525   0x7f94000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f880420f0
0:00:22.468384668  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f880420f0
0:00:22.468417206  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=49154-49155
0:00:22.468430112  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:22.468439223  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:22.468468400  1525   0x7f94000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f8803fbb0 in session 0x7f8804b9d0
0:00:22.468482810  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:1866:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f88041a60> GST_RTSP_LOWER_TRANS_UDP, ipv4
0:00:22.468618063  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f88041a60> allocated address: 0.0.0.0 and ports: 49070, 49071
0:00:22.468632113  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:1880:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f88041a60> GST_RTSP_LOWER_TRANS_UDP, ipv6
0:00:22.468698258  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f88041a60> allocated address: :: and ports: 34446, 34447
0:00:22.470601055  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request PLAY rtsp://***********5:8554/stream/ 1.0
0:00:22.470635581  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f8803fbb0> complete pipeline
0:00:22.470648032  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f88041a60> complete stream
0:00:22.470658580  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f88041a60> create receiver part
0:00:22.470679519  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f88041a60> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:22.470694549  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:22.470772121  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f8804d3e0> adding pad 'src'
0:00:22.470809253  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:00:22.470834051  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:22.470873566  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:00:22.470884617  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.470897882  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:00:22.470915432  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3778:create_receiver_part:<GstRTSPStream@0x7f88041a60> udp IPv4, create and configure udpsources
0:00:22.471714218  1525   0x7f94000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:00:22.471738662  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:22.471934146  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f88050100> adding pad 'src'
0:00:22.472010271  1525   0x7f94000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:00:22.472037879  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:00:22.472052378  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.472077461  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:22.472111830  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:22.472147281  1525   0x7f94000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:22.472172588  1525   0x7f94000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f88050500 on task 0x7f88050e30
0:00:22.472185157  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f88050e30
0:00:22.472352945  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:22.472372350  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:22.472402302  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:22.472420944  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:00:22.472436434  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.472448945  1525   0x7f94001410 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:00:22.472507599  1525   0x7f94001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:22.472529846  1525   0x7f94001410 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:22.472541751  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:00:22.472551421  1525   0x7f94001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:22.472579713  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:00:22.472625496  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:00:22.472637420  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.472661490  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3802:create_receiver_part:<GstRTSPStream@0x7f88041a60> udp IPv6, create and configure udpsources
0:00:22.472676840  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:22.472678826  1525   0x7f94001410 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:22.472706842  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f88051b10> adding pad 'src'
0:00:22.472765815  1525   0x7f94001410 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:00:22.472771931  1525   0x7f94000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:00:22.472820396  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:00:22.472834760  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:22.472856993  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:22.472886617  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:22.472914733  1525   0x7f94000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:22.472938178  1525   0x7f94000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f88051f10 on task 0x7f880525a0
0:00:22.472950603  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f880525a0
0:00:22.473076177  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:22.473091869  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:22.473115241  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:00:22.473130369  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:00:22.473143751  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.473183286  1525   0x7f94001620 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:00:22.473192386  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:00:22.473239743  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:00:22.473279086  1525   0x7f94001620 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:22.473318336  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:00:22.473334861  1525   0x7f94001620 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:22.473340019  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.473392332  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:22.473405726  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:22.473426850  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:00:22.473447040  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:22.473459398  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:22.473477826  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:00:22.473489747  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:00:22.473501663  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.473521645  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f88041a60> create sender part
0:00:22.473537051  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f88041a60> tcp: 0, udp: 1, mcast: 0 (ttl: 0)
0:00:22.473560811  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:22.473652160  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f880534b0> adding pad 'sink'
0:00:22.473690207  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:22.473865185  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f88055ab0> adding pad 'sink'
0:00:22.473918404  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f88041a60> udp IPv4, configure udpsinks
0:00:22.473933940  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f88041a60> udp IPv6, configure udpsinks
0:00:22.473949431  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f88041a60> plug udp sink
0:00:22.473971813  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f88041a60> creating first stream
0:00:22.474006119  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:00:22.474021329  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:00:22.474041137  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:00:22.474057209  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:00:22.474078892  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:00:22.474089315  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.474099769  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:00:22.474135181  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:22.474149295  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:22.474171465  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:00:22.474190659  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:22.474220135  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:22.474226902  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8803fbb0: went from PAUSED to PAUSED (pending PAUSED)
0:00:22.474253940  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:00:22.474273183  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:00:22.474290333  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:22.474303160  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.474319998  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:00:22.474339098  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:00:22.474395950  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:00:22.474406459  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.474453085  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:22.474490270  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f88058170> adding pad 'sink'
0:00:22.474522553  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:22.474546833  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f880588c0> adding pad 'sink'
0:00:22.474584192  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f88041a60> udp IPv4, configure udpsinks
0:00:22.474598165  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f88041a60> udp IPv6, configure udpsinks
0:00:22.474612792  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f88041a60> plug udp sink
0:00:22.474632997  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f88041a60> creating first stream
0:00:22.474656045  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:00:22.474669537  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:00:22.474686659  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:00:22.474701028  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:00:22.474720124  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:00:22.474729564  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.474739556  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:00:22.474770307  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:22.474785252  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:22.474800930  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:00:22.474819978  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:22.474833159  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.474852208  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:22.474864742  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:22.474879770  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:00:22.474896107  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:22.474908634  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:22.474923772  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:00:22.474940735  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:22.474970975  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:00:22.474981446  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:22.475002522  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f88041a60> pipeline successfully updated
0:00:22.475017567  1525   0x7f94000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:49154-49155
0:00:22.475103508  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:22.475115414  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:22.475143589  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f88041a60> Enabling rate control
0:00:22.475175174  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:22.475208379  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:22.475249370  1525   0x7f94000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:22.475330147  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f8803fbb0> seekable:0
0:00:22.475345584  1525   0x7f94000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f8803fbb0> Handle going back to 0 for none live not seekable streams.
0:00:22.475358384  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:22.475371929  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:22.475382961  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:22.475410321  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:22.475488405  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f8803fbb0, target state PAUSED
0:00:22.475502761  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:22.475515175  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f8803fbb0 set blocked 0
0:00:22.475528029  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f88041a60> blocked: 0
0:00:22.475546726  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f8803fbb0 do_state 1
0:00:22.475558137  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f8803fbb0
0:00:22.475569410  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f8803fbb0
0:00:22.475581257  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f8803fbb0 set blocked 0
0:00:22.475592923  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f88041a60> blocked: 0
0:00:22.475604379  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f8803fbb0
0:00:22.475615224  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:22.475710422  1525   0x7f94000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f8803fbb0: got message type 16 (tag)
0:00:22.475771861  1525   0x7f94001200 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:22.475812986  1525   0x7f94001200 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:22.475833611  1525   0x7f94001200 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:22.475890806  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8803fbb0: went from PAUSED to PAUSED (pending PLAYING)
0:00:22.475928851  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f8803fbb0> got async-done
0:00:22.476045959  1525   0x7f94001830 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:22.476151211  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:22.476157424  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:22.476219587  1525   0x7f94001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:22.476256687  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.476271775  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:00:22.476290422  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.476313914  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:22.476329533  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476341681  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:22.476353254  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:22.476367679  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.476380521  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:00:22.476393634  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.476410221  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:22.476424875  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:22.476437303  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:00:22.476452830  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.476468480  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:22.476484840  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476510213  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476521929  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:22.476533549  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:22.476548536  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476559848  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:22.476572847  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:22.476586855  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476598315  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:22.476609585  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:22.476623008  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:22.476636217  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:22.476649963  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476675560  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476687281  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:22.476699278  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:22.476713609  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476725197  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:22.476736371  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:22.476750170  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476761267  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:22.476773540  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:22.476787363  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476798721  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:22.476811181  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:22.476825153  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476836590  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:22.476847874  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:22.476861732  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476872613  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:22.476883976  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:22.476897077  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476908157  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:22.476919779  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:22.476932531  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:22.476945187  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:22.476960792  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.476971965  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:22.476984623  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:22.476998895  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:22.477012494  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:22.477026507  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:22.477037971  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:22.477113088  1525   0x7f94000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:22.477179805  1525   0x7f94000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8803fbb0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 14:01:45.634] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:45.634] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:01:45.634] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:45.634] [INFO] Pushing raw buffer directly to appsrc...
0:00:22.489171872  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f84389660
0:00:22.489190379  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:45.634] [INFO] Raw frame fed to appsrc successfully, total frames served: 8
[2021-01-01 14:01:45.634] [INFO] feed_data call completed
0:00:22.489235012  1525   0x7f94000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:24.716407561  1525   0x7f8c0171f0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:24.716558911  1525   0x7f8c0171f0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:24.716780846  1525   0x7f94000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:24.716848036  1525   0x7f8c0171f0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3751509392, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)18972, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400326211088936375, sr-rtptime=(uint)3041522059, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:24.716852392  1525   0x7f94000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:25.535131710  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f88041a60: new source 0x7f680139c0
0:00:25.535266021  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1932324150, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:49155, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:25.535307706  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:49155 in 1 transports
0:00:25.535324497  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f88041a60: found transport 0x7f8804c6e0 for source  0x7f680139c0
0:00:25.535342179  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f88041a60: source 0x7f680139c0 for transport 0x7f8804c6e0
0:00:25.535369926  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f88041a60: source 0x7f680139c0 in transport 0x7f8804c6e0 is active
0:00:25.535382035  1525   0x7f94001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f880420f0 alive
0:00:25.535438567  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f88041a60: new SDES 0x7f680139c0
0:00:29.650307033  1525   0x7f8c0171f0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3751509392, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)18972, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400326232279235566, sr-rtptime=(uint)3041966097, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 14:01:53.362] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:01:53.362] [INFO] Uptime: 30.0 seconds
[2021-01-01 14:01:53.362] [INFO] Total connections: 1
[2021-01-01 14:01:53.362] [INFO] Active connections: 1
[2021-01-01 14:01:53.362] [INFO] Frames served: 8
[2021-01-01 14:01:53.362] [INFO] Clients connected: 0
[2021-01-01 14:01:53.362] [INFO] Error count: 0
0:00:30.712588257  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f88041a60: source 0x7f680139c0 in transport 0x7f8804c6e0 is active
0:00:30.712619458  1525   0x7f94001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f880420f0 alive
0:00:33.069680431  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f88041a60: source 0x7f680139c0 in transport 0x7f8804c6e0 is active
0:00:33.069710876  1525   0x7f94001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f880420f0 alive
0:00:33.069742194  1525   0x7f94001410 INFO              rtspstream rtsp-stream.c:2488:on_bye_ssrc: 0x7f88041a60: source 0x7f680139c0 bye
0:00:33.069795849  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request TEARDOWN rtsp://***********5:8554/stream/ 1.0
0:00:33.069842368  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f8803fbb0, target state PLAYING
0:00:33.069857738  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:33.069882427  1525   0x7f94000b70 INFO              rtspstream rtsp-stream.c:4774:update_transport: removing ***********:49154-49155
0:00:33.069920688  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f8803fbb0 do_state 1
0:00:33.069932295  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f8803fbb0
0:00:33.069943535  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f8803fbb0
0:00:33.069955500  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:33.069966019  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:33.069977224  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f8803fbb0
0:00:33.070089768  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:33.070151392  1525   0x7f94000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:33.070185139  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070198107  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink1> skipping transition from PLAYING to  PLAYING
0:00:33.070210521  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:33.070225389  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070237159  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:33.070248211  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:33.070262736  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070274803  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee1> skipping transition from PLAYING to  PLAYING
0:00:33.070286343  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:33.070300631  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070311719  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee0> skipping transition from PLAYING to  PLAYING
0:00:33.070322996  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:33.070337861  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070363660  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070375357  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:33.070386923  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:33.070401720  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070413207  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:33.070424594  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:33.070439584  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070450996  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:33.070462003  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:33.070476009  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:33.070488613  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:33.070501729  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070526660  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070538058  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:33.070549296  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:33.070563521  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070574610  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:33.070585375  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:33.070599714  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070611074  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:33.070623469  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:33.070637389  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070649591  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:33.070661163  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:33.070675673  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070686591  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:33.070696189  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:33.070709536  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070722597  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:33.070734489  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:33.070747046  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070758203  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:33.070769087  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:33.070781823  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:33.070794447  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:33.070809538  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.070820266  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:33.070831430  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:33.070845287  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:33.070858829  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:33.070873305  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:33.070883678  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:33.070962407  1525   0x7f94001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = flushing
0:00:33.071003407  1525   0x7f94001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:33.071049607  1525   0x7f94001620 INFO                    task gsttask.c:370:gst_task_func:<udpsrc1:src> Task resume from paused
0:00:33.071067353  1525   0x7f94001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = flushing
0:00:33.071085464  1525   0x7f94001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:33.071111899  1525   0x7f94001410 INFO                    task gsttask.c:370:gst_task_func:<udpsrc0:src> Task resume from paused
0:00:33.071126025  1525   0x7f94001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = eos
0:00:33.071128799  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f880572c0, time 99:99:99.999999999, seq-num 182, (NULL)
0:00:33.071148504  1525   0x7f94001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:33.071168980  1525   0x7f94000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f8804b9d0
0:00:33.071181783  1525   0x7f94000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f8803fbb0 was not prepared
0:00:33.071192564  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f8803fbb0 is already unpreparing
0:00:33.071213594  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f98006630: sending close message
0:00:33.071285266  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f98006630: session 0x7f880420f0 removed
0:00:33.071299141  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f98006630: unwatch session 0x7f880420f0
0:00:33.071306282  1525   0x7f94001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = eos
0:00:33.071317972  1525   0x7f94000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f880420f0
0:00:33.071375996  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f98006630: connection closed
0:00:33.071392089  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f98006630: watch destroyed
0:00:33.071409539  1525   0x7f94000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x556a000050> unmanage client 0x7f98006630
0:00:33.071431543  1525   0x7f94000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f98006cb0
0:00:33.071447786  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f98006630
0:00:33.071461132  1525   0x7f94001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:33.071518073  1525   0x7f94000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f98006f10
0:00:33.073915907  1525   0x7f98000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f98006630 connected to server ip ***********5, ipv6 = 0
0:00:33.073945657  1525   0x7f98000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f98006630 ip ***********:50983
0:00:33.073964057  1525   0x7f98000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x556a000050> manage client 0x7f98006630
[2021-01-01 14:01:56.219] [INFO] === CLIENT CONNECTED ===
[2021-01-01 14:01:56.219] [INFO] Active connections: 2, Total connections: 2
0:00:33.074101424  1525   0x7f94000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f98008020
0:00:33.074118457  1525   0x7f98000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f98006630: attaching to context 0x7f98008140
0:00:33.083461115  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request OPTIONS rtsp://***********5:8554/stream 1.0
0:00:33.086301849  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request DESCRIBE rtsp://***********5:8554/stream 1.0
0:00:33.086328760  1525   0x7f94000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556a002460 for path /stream
0:00:33.086346727  1525   0x7f94000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=false format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:33.086428420  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:33.086471006  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f8805c230> adding pad 'src'
0:00:33.086492967  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 0
0:00:33.086520377  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:33.086557923  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:33.086595849  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f8805ca70> adding pad 'sink'
0:00:33.086626155  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f8805ca70> adding pad 'src'
0:00:33.086656567  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:33.086681834  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f8805f040> adding pad 'sink'
0:00:33.086703241  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f8805f040> adding pad 'src'
0:00:33.086755348  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:33.086784016  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f8805fa50> adding pad 'sink'
0:00:33.086804320  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f8805fa50> adding pad 'src'
0:00:33.086835081  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:33.086861048  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f88060960> adding pad 'sink'
0:00:33.086881674  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f88060960> adding pad 'src'
0:00:33.086902429  1525   0x7f94000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f88060960> PTS interpolation: no
0:00:33.086914041  1525   0x7f94000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f88060960> TS inferring: no
0:00:33.086944136  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:33.086968625  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f88064580> adding pad 'src'
0:00:33.086989447  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f88064580> adding pad 'sink'
0:00:33.087015265  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:33.087094398  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue1 (0/0) with caps "(NULL)"
0:00:33.087109820  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue1:(any)
0:00:33.087125283  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue1:sink
0:00:33.087143330  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:33.087162641  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue1:src> pad has no peer
0:00:33.087180341  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue1 in same bin, no need for ghost pads
0:00:33.087200594  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue1:sink
0:00:33.087214540  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:33.087228865  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue1:src> pad has no peer
0:00:33.087246894  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue1:sink, successful
0:00:33.087257698  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.087272246  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:33.087295432  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue1 to some pad of GstVideoScale named videoscale1 (0/0) with caps "(NULL)"
0:00:33.087308357  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue1:(any) to element videoscale1:(any)
0:00:33.087323100  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue1:src and videoscale1:sink
0:00:33.087338111  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:33.087354542  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale1:src> pad has no peer
0:00:33.087723943  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue1 and videoscale1 in same bin, no need for ghost pads
0:00:33.087742195  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue1:src and videoscale1:sink
0:00:33.087757509  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:33.087772927  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale1:src> pad has no peer
0:00:33.088113355  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue1:src and videoscale1:sink, successful
0:00:33.088123490  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.088133278  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue1:src> Received event on flushing pad. Discarding
0:00:33.088162184  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale1 to some pad of GstMppH264Enc named mpph264enc1 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:33.088180626  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:33.088210045  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f88066bc0> adding pad 'sink'
0:00:33.088232018  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f88066bc0> adding pad 'src'
0:00:33.088249046  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin1> getting state
0:00:33.088275750  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to NULL
0:00:33.088291523  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.088308881  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale1:(any) to element capsfilter1:sink
0:00:33.088321090  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:sink
0:00:33.088333154  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale1 and capsfilter1 in same bin, no need for ghost pads
0:00:33.088350006  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale1:src and capsfilter1:sink
0:00:33.088368842  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:33.088696759  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter1:src> pad has no peer
0:00:33.088734952  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale1:src and capsfilter1:sink, successful
0:00:33.088744774  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.088754899  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale1:src> Received event on flushing pad. Discarding
0:00:33.088772999  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter1:src to element mpph264enc1:(any)
0:00:33.088784904  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter1:src
0:00:33.088799294  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter1:src and mpph264enc1:sink
0:00:33.088816847  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:33.089238639  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:33.089307116  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter1 and mpph264enc1 in same bin, no need for ghost pads
0:00:33.089332434  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter1:src and mpph264enc1:sink
0:00:33.089364463  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:33.089829951  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc1:src> pad has no peer
0:00:33.089949183  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter1:src and mpph264enc1:sink, successful
0:00:33.089962750  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.089974580  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter1:src> Received event on flushing pad. Discarding
0:00:33.090007376  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc1 to some pad of GstH264Parse named h264parse1 (0/0) with caps "(NULL)"
0:00:33.090021169  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc1:(any) to element h264parse1:(any)
0:00:33.090037556  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc1:src and h264parse1:sink
0:00:33.090057906  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:00:33.090079180  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc1 and h264parse1 in same bin, no need for ghost pads
0:00:33.090096987  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc1:src and h264parse1:sink
0:00:33.090112155  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse1:src> pad has no peer
0:00:33.090131382  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc1:src and h264parse1:sink, successful
0:00:33.090140500  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.090150111  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc1:src> Received event on flushing pad. Discarding
0:00:33.090171355  1525   0x7f94000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse1 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:33.090183637  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse1:(any) to element pay0:(any)
0:00:33.090197669  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse1:src and pay0:sink
0:00:33.090215608  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:33.090233652  1525   0x7f94000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse1 and pay0 in same bin, no need for ghost pads
0:00:33.090250449  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse1:src and pay0:sink
0:00:33.090265898  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:33.090281620  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse1:src and pay0:sink, successful
0:00:33.090290635  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.090299957  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse1:src> Received event on flushing pad. Discarding
0:00:33.090335437  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay0
0:00:33.090353686  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f88064580
0:00:33.090365800  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:33.090380325  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f8806acc0: creating stream with index 0 and payloader <pay0>
0:00:33.090429878  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad5
0:00:33.090444076  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad5, successful
0:00:33.090452922  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.090462322  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:33.090482925  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin1> adding pad 'src_0'
0:00:33.090498153  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f8806d3a0
0:00:33.090513664  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f8806d3a0> set retransmission time 0
0:00:33.090525696  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f8806d3a0> Enabling rate control
0:00:33.090547958  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f8806d3a0> set retransmission pt 97
0:00:33.090561749  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay0
0:00:33.090583350  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay0
0:00:33.090601927  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element pay1
0:00:33.090620062  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element dynpay1
0:00:33.090638573  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element depay1
0:00:33.090662484  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:33.090754962  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f8806d3a0> set retransmission time 0
[2021-01-01 14:01:56.235] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 14:01:56.235] [INFO] factory: 0x556a002460, media: 0x7f8806acc0, user_data: 0x5569f3ae20
[2021-01-01 14:01:56.235] [INFO] Calling configure_media...
[2021-01-01 14:01:56.235] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 14:01:56.235] [INFO] Got media pipeline: 0x7f880661f0
0:00:33.090846336  1525   0x7f94000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin1]: looking up child element source
[2021-01-01 14:01:56.235] [INFO] Found appsrc element: 0x7f8805c230
[2021-01-01 14:01:56.235] [INFO] Connected appsrc signals
0:00:33.090908341  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 545460846592
0:00:33.090923754  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:33.090942948  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:33.090958911  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2022:gst_app_src_set_stream_type:<source> setting stream_type of 0
[2021-01-01 14:01:56.236] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 14:01:56.236] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:33.091511169  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f88068950
0:00:33.091540763  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 14:01:56.236] [INFO] Initial frame push result: 0
[2021-01-01 14:01:56.236] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 14:01:56.236] [INFO] Configured appsrc properties
[2021-01-01 14:01:56.236] [INFO] Pipeline latency query result: live=0, min=0 ns, max=18446744073709551615 ns
[2021-01-01 14:01:56.236] [INFO] Setting manual latency: min=33333333 ns, max=100000000 ns
0:00:33.091656384  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.033333333
0:00:33.091688151  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
[2021-01-01 14:01:56.236] [INFO] Sent latency event to pipeline
[2021-01-01 14:01:56.236] [INFO] Media configured successfully
[2021-01-01 14:01:56.236] [INFO] configure_media call completed
0:00:33.091758012  1525   0x7f94000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f8806acc0 for url /stream
0:00:33.091795869  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f8806acc0
0:00:33.091808566  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:33.091808049  1525   0x7f94001830 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f88067800
0:00:33.091827669  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:33.091935072  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:33.091946781  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:33.091961282  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:33.091985347  1525   0x7f94001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:33.092027483  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:33.092047006  1525   0x7f94001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:33.092073508  1525   0x7f94001830 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f8806d3a0 joining bin as session 0
0:00:33.092100086  1525   0x7f94001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:33.092168841  1525   0x7f94001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:33.092202078  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f60005970> adding pad 'sink'
0:00:33.092228242  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f60005970> adding pad 'rtcp_sink'
0:00:33.092246645  1525   0x7f94001830 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:33.092295612  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f600061a0> adding pad 'src'
0:00:33.092308734  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f600061a0> adding pad 'sink'
0:00:33.092410595  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to NULL
0:00:33.092425607  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to NULL
0:00:33.092437725  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to NULL
0:00:33.092477955  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_sink'
0:00:33.092509776  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtp_src'
0:00:33.092524509  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:send_rtp_src
0:00:33.092575773  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6
0:00:33.092592381  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtp_src and send_rtp_src_0:proxypad6, successful
0:00:33.092602214  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.092630035  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_src_0'
0:00:33.092663578  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink
0:00:33.092678196  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad7 and rtpsession1:send_rtp_sink, successful
0:00:33.092687608  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.092705419  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtp_sink_0'
0:00:33.092728772  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin1:src_0 and rtpbin1:send_rtp_sink_0
0:00:33.092772143  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin1:src_0 and rtpbin1:send_rtp_sink_0, successful
0:00:33.092782554  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.092794945  1525   0x7f94001830 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:33.092813706  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin1:send_rtp_src_0
0:00:33.092852634  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'send_rtcp_src'
0:00:33.092892740  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8
0:00:33.092911268  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:send_rtcp_src and send_rtcp_src_0:proxypad8, successful
0:00:33.092920587  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.092939577  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'send_rtcp_src_0'
0:00:33.092972450  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'recv_rtcp_sink'
0:00:33.093000066  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession1> adding pad 'sync_src'
0:00:33.093019119  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession1:sync_src
0:00:33.093030845  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux1:rtcp_sink
0:00:33.093046526  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink
0:00:33.093059586  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession1:sync_src and rtpssrcdemux1:rtcp_sink, successful
0:00:33.093068143  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.093107704  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink
0:00:33.093122244  1525   0x7f94001830 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad9 and rtpsession1:recv_rtcp_sink, successful
0:00:33.093130953  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.093147792  1525   0x7f94001830 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin1> adding pad 'recv_rtcp_sink_0'
0:00:33.093186040  1525   0x7f94001830 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f8806d3a0> successfully joined bin
0:00:33.093207178  1525   0x7f94001830 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f8806acc0
0:00:33.093219627  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f8806acc0 set blocked 1
0:00:33.093230982  1525   0x7f94001830 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f8806d3a0> blocked: 1
0:00:33.093244988  1525   0x7f94001830 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f8806acc0
0:00:33.093257562  1525   0x7f94001830 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f8806acc0
0:00:33.093282713  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093315041  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093330547  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to READY
0:00:33.093342501  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093365440  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 2(READY) successfully
0:00:33.093381737  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093394905  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to READY
0:00:33.093407419  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093436721  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 2(READY) successfully
0:00:33.093452982  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093465672  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to READY
0:00:33.093477287  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093493412  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 2(READY) successfully
0:00:33.093506974  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to READY
0:00:33.093518357  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093533853  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 2(READY) successfully
0:00:33.093547230  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093582625  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:33.093597575  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:33.093608681  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093625015  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 2(READY) successfully
0:00:33.093639648  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093652926  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to READY
0:00:33.093663854  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093679788  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 2(READY) successfully
0:00:33.093694260  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093708856  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to READY
0:00:33.093719798  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093738044  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 2(READY) successfully
0:00:33.093753529  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093767636  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to READY
0:00:33.093779505  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093795937  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 2(READY) successfully
0:00:33.093811896  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093825762  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to READY
0:00:33.093837115  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093852961  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 2(READY) successfully
0:00:33.093888215  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current NULL pending VOID_PENDING, desired next READY
0:00:33.093903907  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to READY
0:00:33.093915170  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093931638  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 2(READY) successfully
0:00:33.093947860  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:33.093961232  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:33.093972660  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.093988295  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 2(READY) successfully
0:00:33.094002141  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to READY
0:00:33.094014542  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:33.094031456  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 2(READY) successfully
0:00:33.094048061  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:33.094060649  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:33.094074407  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:33.094096417  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.094128084  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.094146872  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PAUSED
0:00:33.094159247  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.094175937  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 3(PAUSED) successfully
0:00:33.094191249  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.094209460  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PAUSED
0:00:33.094221212  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.094236949  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 3(PAUSED) successfully
0:00:33.094251476  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.094268909  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PAUSED
0:00:33.094281379  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.094301252  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 3(PAUSED) successfully
0:00:33.094317674  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PAUSED
0:00:33.094329970  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.094346923  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 3(PAUSED) successfully
0:00:33.094360741  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.094391602  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.094414880  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:33.094427261  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.094443766  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 3(PAUSED) successfully
0:00:33.094458844  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.094666149  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PAUSED
0:00:33.094681462  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.094710216  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 3(PAUSED) successfully
0:00:33.094728073  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.095072493  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PAUSED
0:00:33.095093538  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.095131636  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 3(PAUSED) successfully
0:00:33.095152045  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.095174050  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PAUSED
0:00:33.095186104  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.095202686  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 3(PAUSED) successfully
0:00:33.095218565  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.095238890  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to PAUSED
0:00:33.095251507  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.095267563  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 3(PAUSED) successfully
0:00:33.095282855  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.095314733  1525   0x7f94001830 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f8805d110 on task 0x7f600577f0
0:00:33.095327636  1525   0x7f94001830 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue1:src> created task 0x7f600577f0
0:00:33.095453794  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to PAUSED
0:00:33.095472279  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.095492522  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 3(PAUSED) successfully
0:00:33.095507917  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:33.095525288  1525   0x7f94001830 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:33.095564628  1525   0x7f94001830 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:33.095595892  1525   0x7f94001830 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f8805c5a0 on task 0x7f600580a0
0:00:33.095608803  1525   0x7f94001830 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f600580a0
0:00:33.095718535  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:33.095734099  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.095752884  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin1> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:33.095772295  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PAUSED
0:00:33.095784028  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.095800528  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 3(PAUSED) successfully without preroll
0:00:33.095816330  1525   0x7f94001830 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:33.095827081  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:33.095840413  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.095856345  1525   0x7f94001830 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f8806acc0
0:00:33.095874407  1525   0x7f94001c50 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:33.095878194  1525   0x7f94001830 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f8806acc0
0:00:33.095925957  1525   0x7f94001c50 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:33.095968397  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:33.096005012  1525   0x7f94001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:33.096034170  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096059455  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096072915  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage1> completed state change to PLAYING
0:00:33.096085177  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096103047  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:33.096118594  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096132243  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux1> completed state change to PLAYING
0:00:33.096143924  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096160250  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:33.096174729  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096256502  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession1> completed state change to PLAYING
0:00:33.096271108  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096289301  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:33.096304243  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:33.096316719  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096335389  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:33.096367666  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096382376  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:33.096394904  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096423196  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:33.096439296  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096452709  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse1> completed state change to PLAYING
0:00:33.096463759  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096479021  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:33.096493622  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096507022  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc1> completed state change to PLAYING
0:00:33.096518591  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096535347  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:33.096550177  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096562687  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter1> completed state change to PLAYING
0:00:33.096573781  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096589675  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:33.096603807  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096616125  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale1> completed state change to PLAYING
0:00:33.096627180  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096642421  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:33.096656878  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.096670925  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue1> completed state change to PLAYING
0:00:33.096682361  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096698289  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:33.096716281  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:33.096730139  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096747826  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:33.096760887  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:33.096772378  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.096813607  1525   0x7f94001c50 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:33.096822393  1525   0x7f94001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:33.096857747  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:33.096871852  1525   0x7f94001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.097017707  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:33.097018307  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8806acc0: went from NULL to READY (pending PAUSED)
0:00:33.097069157  1525   0x7f94001c50 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:33.097103582  1525   0x7f94001c50 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 14:01:56.242] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:56.242] [INFO] appsrc: 0x7f8805c230, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:56.242] [INFO] Calling feed_data...
[2021-01-01 14:01:56.242] [INFO] === FEED DATA CALLED ===
0:00:33.097223880  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8806acc0: went from READY to PAUSED (pending VOID_PENDING)
[2021-01-01 14:01:56.242] [INFO] appsrc: 0x7f8805c230
[2021-01-01 14:01:56.242] [INFO] DDS reader is initialized
0:00:33.097252363  1525   0x7f94001830 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f8806acc0: got message type 2048 (new-clock)
[2021-01-01 14:01:56.242] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:56.242] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:56.242] [INFO] Creating GstBuffer for raw frame data...
0:00:33.097378997  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8806acc0: went from PAUSED to PLAYING (pending VOID_PENDING)
0:00:33.097655023  1525   0x7f94001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:33.097926609  1525   0x7f94001a40 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter1> reuse caps
0:00:33.097957360  1525   0x7f94001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 14:01:56.243] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:56.243] [INFO] Pushing raw buffer directly to appsrc...
0:00:33.098052832  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f58002580
0:00:33.098110007  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:56.243] [INFO] Raw frame fed to appsrc successfully, total frames served: 10
[2021-01-01 14:01:56.243] [INFO] feed_data call completed
0:00:33.098150185  1525   0x7f94001a40 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc1> applying YUY2 1280x720 (2560x720)
0:00:33.098174757  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:56.243] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:56.243] [INFO] appsrc: 0x7f8805c230, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:56.243] [INFO] Calling feed_data...
[2021-01-01 14:01:56.243] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:56.243] [INFO] appsrc: 0x7f8805c230
[2021-01-01 14:01:56.243] [INFO] DDS reader is initialized
[2021-01-01 14:01:56.243] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:56.243] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:56.243] [INFO] Creating GstBuffer for raw frame data...
0:00:33.098514987  1525   0x7f94001a40 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
[2021-01-01 14:01:56.244] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:56.244] [INFO] Pushing raw buffer directly to appsrc...
0:00:33.099251707  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f5812f800
0:00:33.099280907  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:56.244] [INFO] Raw frame fed to appsrc successfully, total frames served: 11
[2021-01-01 14:01:56.244] [INFO] feed_data call completed
0:00:33.099380857  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:56.244] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:56.244] [INFO] appsrc: 0x7f8805c230, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:56.244] [INFO] Calling feed_data...
[2021-01-01 14:01:56.244] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:56.244] [INFO] appsrc: 0x7f8805c230
[2021-01-01 14:01:56.244] [INFO] DDS reader is initialized
[2021-01-01 14:01:56.244] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:56.244] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:56.244] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:01:56.245] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:56.245] [INFO] Pushing raw buffer directly to appsrc...
0:00:33.100491207  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f581c6550
0:00:33.100522657  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:56.245] [INFO] Raw frame fed to appsrc successfully, total frames served: 12
[2021-01-01 14:01:56.245] [INFO] feed_data call completed
0:00:33.100620707  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:56.245] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:56.245] [INFO] appsrc: 0x7f8805c230, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:56.245] [INFO] Calling feed_data...
[2021-01-01 14:01:56.245] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:56.245] [INFO] appsrc: 0x7f8805c230
[2021-01-01 14:01:56.245] [INFO] DDS reader is initialized
[2021-01-01 14:01:56.245] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 14:01:56.245] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:56.245] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:01:56.246] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:56.246] [INFO] Pushing raw buffer directly to appsrc...
0:00:33.101494357  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f5825d0d0
0:00:33.101526757  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:56.246] [INFO] Raw frame fed to appsrc successfully, total frames served: 13
[2021-01-01 14:01:56.246] [INFO] feed_data call completed
0:00:33.101592007  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:56.246] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:56.246] [INFO] appsrc: 0x7f8805c230, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:56.246] [INFO] Calling feed_data...
[2021-01-01 14:01:56.246] [INFO] === FEED DATA CALLED ===
[2021-01-01 14:01:56.246] [INFO] appsrc: 0x7f8805c230
[2021-01-01 14:01:56.246] [INFO] DDS reader is initialized
[2021-01-01 14:01:56.246] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:33.117313121  1525   0x7f94001a40 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f88060380 on task 0x7f64051970
0:00:33.117340560  1525   0x7f94001a40 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc1:src> created task 0x7f64051970
0:00:33.121052932  1525   0x7f94001e60 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:33.123272882  1525   0x7f94001e60 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse1> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:33.123422181  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:33.123456903  1525   0x7f94001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:33.123801907  1525   0x7f94001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:33.124593007  1525   0x7f94001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrai[2021-01-01 14:01:56.269] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
ned-baseline, payload=(int)96, ssrc=(uint)875253523, timestamp-offset=(uint)1457554402, seqnum-offset=(uint)6970, a-framerate=(string)30
[2021-01-01 14:01:56.269] [INFO] Creating GstBuffer for raw frame data...
0:00:33.124728957  1525   0x7f94001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:33.124885457  1525   0x7f94001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:33.124963682  1525   0x7f94001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:33.125102357  1525   0x7f94001e60 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f8806d3a0: new sender source 0x7f48010c20
[2021-01-01 14:01:56.270] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:56.270] [INFO] Pushing raw buffer directly to appsrc...
0:00:33.125396457  1525   0x7f94001e60 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)875253523, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)6970, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:33.125405407  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f582f3290
0:00:33.125483032  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:56.270] [INFO] Raw frame fed to appsrc successfully, total frames served: 14
[2021-01-01 14:01:56.270] [INFO] feed_data call completed
0:00:33.125530807  1525   0x7f94001e60 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin1:send_rtp_src_0> pad has no peer
0:00:33.125554232  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 14:01:56.270] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 14:01:56.270] [INFO] appsrc: 0x7f8805c230, unused: 4096, user_data: 0x5569f3ae20
[2021-01-01 14:01:56.270] [INFO] Calling feed_data...
0:00:33.125678282  1525   0x7f94001e60 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f8806d3a0 received[2021-01-01 14:01:56.270] [INFO] === FEED DATA CALLED ===
 caps 0x7f4800cc40, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028,[2021-01-01 14:01:56.270] [INFO] appsrc: 0x7f8805c230
 profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)875253523, timestamp-offset=(uint)1457554402, seqnum-offset=(uint)6970, a-framerate=(string)30
[2021-01-01 14:01:56.270] [INFO] DDS reader is initialized
[2021-01-01 14:01:56.270] [INFO] Attempting to read DDS frame with 100ms timeout...
0:00:33.126387157  1525   0x7f94001e60 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession1> Can't determine running time for this packet without knowing configured latency
0:00:33.126470882  1525   0x7f94001e60 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin1:send_rtp_src_0> Now blocking
0:00:33.126528857  1525   0x7f94001e60 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f8806d3a0> position: 447086:01:56.094377000
0:00:33.126622333  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f8806acc0> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:33.126640403  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:33.126650438  1525   0x7f94001830 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:33.126660817  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:33.126689607  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:33.126712032  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f8806acc0 is prerolled
0:00:33.126754082  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:33.126817182  1525   0x7f94000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:33.126888932  1525   0x7f94000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:33.126912807  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f8806acc0 no suspend
0:00:33.126928407  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:33.126956482  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://***********5:8554/stream/
0:00:33.130413607  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request SETUP rtsp://***********5:8554/stream/stream=0 1.0
0:00:33.130459907  1525   0x7f94000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556a002460 for path /stream/stream=0
0:00:33.130481657  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f8806acc0 for path /stream
0:00:33.130500382  1525   0x7f94000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:33.130518207  1525   0x7f94000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f8806acc0 was not prepared
0:00:33.130573232  1525   0x7f94000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f88067ba0
0:00:33.130597007  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f88067ba0
0:00:33.130652932  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP/TCP;unicast;interleaved=0-1
0:00:33.130677082  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:33.130690357  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:33.130710857  1525   0x7f94000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f8806acc0 in session 0x7f88069d60
0:00:33.134679757  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request PLAY rtsp://***********5:8554/stream/ 1.0
0:00:33.134743332  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f8806acc0> complete pipeline
0:00:33.134765957  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f8806d3a0> complete stream
0:00:33.134783282  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f8806d3a0> create receiver part
0:00:33.134817732  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f8806d3a0> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:33.134840757  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:33.134918732  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f8806b480> adding pad 'src'
0:00:33.134978632  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel1:src
0:00:33.135021057  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel1:src and rtpbin1:recv_rtcp_sink_0
0:00:33.135086982  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel1:src and rtpbin1:recv_rtcp_sink_0, successful
0:00:33.135102682  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.135122007  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel1:src> Received event on flushing pad. Discarding
0:00:33.135152007  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:33.135205057  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f8806bb70> adding pad 'src'
0:00:33.135258807  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsrc0:src
0:00:33.135288332  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsrc0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:33.135310132  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:33.135348957  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsrc0> continue state change READY to PAUSED, final PLAYING
0:00:33.135372557  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<appsrc0> starting
0:00:33.135410007  1525   0x7f94000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<appsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:33.135451582  1525   0x7f94000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f8806bee0 on task 0x7f8806c0d0
0:00:33.135472757  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<appsrc0:src> created task 0x7f8806c0d0
0:00:33.135638282  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:33.135660432  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:33.135699457  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:33.135740957  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsrc0> completed state change to PLAYING
0:00:33.135763082  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.135772482  1525   0x7f94002070 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "appsrc0"
0:00:33.135817457  1525   0x7f94002070 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<appsrc0:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:33.135862132  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel1> adding pad 'funnelpad2'
0:00:33.135874107  1525   0x7f94002070 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<appsrc0:src> pad has no peer
0:00:33.135916232  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link appsrc0:src and funnel1:funnelpad2
0:00:33.135940057  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<appsrc0> caps: (NULL)
0:00:33.136001332  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked appsrc0:src and funnel1:funnelpad2, successful
0:00:33.136016457  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.136057082  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel1> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:33.136075882  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed NULL to READY (PLAYING pending)
0:00:33.136103157  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel1> continue state change READY to PAUSED, final PLAYING
0:00:33.136130882  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:33.136148732  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:33.136176107  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel1> continue state change PAUSED to PLAYING, final PLAYING
0:00:33.136192382  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel1> completed state change to PLAYING
0:00:33.136209157  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.136238682  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f8806d3a0> create sender part
0:00:33.136259507  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f8806d3a0> tcp: 1, udp: 0, mcast: 0 (ttl: 0)
0:00:33.136297182  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:33.136358057  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f8806ca00> adding pad 'sink'
0:00:33.136415757  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsink"
0:00:33.136682882  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f8806ef80> adding pad 'sink'
0:00:33.136731782  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3472:plug_tcp_sink:<GstRTSPStream@0x7f8806d3a0> plug tcp sink
0:00:33.136794182  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee2> adding pad 'src_0'
0:00:33.136814782  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsink0:sink
0:00:33.136844582  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee2:src_0 and appsink0:sink
0:00:33.136869032  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee2:sink> pad has no peer
0:00:33.136903782  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee2:src_0 and appsink0:sink, successful
0:00:33.136920907  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.136936557  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee2:src_0> Received event on flushing pad. Discarding
0:00:33.136975557  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:33.136998357  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:33.137029432  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsink0> continue state change READY to PAUSED, final PLAYING
0:00:33.137052782  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:33.137098632  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee2> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:33.137118757  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed NULL to READY (PAUSED pending)
0:00:33.137146982  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee2> continue state change READY to PAUSED, final PAUSED
0:00:33.137124507  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8806acc0: went from PAUSED to PAUSED (pending PAUSED)
0:00:33.137173632  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee2> completed state change to PAUSED
0:00:33.137209507  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.137232857  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee2:sink
0:00:33.137260207  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin1:send_rtp_src_0 and tee2:sink
0:00:33.137361832  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin1:send_rtp_src_0 and tee2:sink, successful
0:00:33.137377457  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.137457357  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:33.137525257  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f880706c0> adding pad 'sink'
0:00:33.137581157  1525   0x7f94000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsink"
0:00:33.137620807  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f88071040> adding pad 'sink'
0:00:33.137668157  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:3472:plug_tcp_sink:<GstRTSPStream@0x7f8806d3a0> plug tcp sink
0:00:33.137713632  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee3> adding pad 'src_0'
0:00:33.137733182  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad appsink1:sink
0:00:33.137758107  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee3:src_0 and appsink1:sink
0:00:33.137780582  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee3:sink> pad has no peer
0:00:33.137811082  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee3:src_0 and appsink1:sink, successful
0:00:33.137825807  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.137840282  1525   0x7f94000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee3:src_0> Received event on flushing pad. Discarding
0:00:33.137895382  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<appsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:33.137915332  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:33.137939207  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<appsink1> continue state change READY to PAUSED, final PAUSED
0:00:33.137967432  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsink1> completed state change to PAUSED
0:00:33.137985832  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.138012507  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee3> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:33.138030882  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed NULL to READY (PAUSED pending)
0:00:33.138052132  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee3> continue state change READY to PAUSED, final PAUSED
0:00:33.138074007  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee3> completed state change to PAUSED
0:00:33.138091582  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:33.138112407  1525   0x7f94000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee3:sink
0:00:33.138137157  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin1:send_rtcp_src_0 and tee3:sink
0:00:33.138185507  1525   0x7f94000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin1:send_rtcp_src_0 and tee3:sink, successful
0:00:33.138200532  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:33.138233357  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f8806d3a0> pipeline successfully updated
0:00:33.138252482  1525   0x7f94000b70 INFO              rtspstream rtsp-stream.c:4783:update_transport: adding TCP ***********
0:00:33.138320657  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:33.138336582  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:33.138371357  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f8806d3a0> Enabling rate control
0:00:33.138423557  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:33.138531207  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:33.138614182  1525   0x7f94001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:33.138740457  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f8806acc0> seekable:0
0:00:33.138759882  1525   0x7f94000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f8806acc0> Handle going back to 0 for none live not seekable streams.
0:00:33.138776107  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:33.138791382  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:33.138804957  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:33.138845407  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:33.138979807  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f8806acc0, target state PAUSED
0:00:33.139004882  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:33.139023407  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f8806acc0 set blocked 0
0:00:33.139041007  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f8806d3a0> blocked: 0
0:00:33.139068332  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f8806acc0 do_state 1
0:00:33.139085407  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f8806acc0
0:00:33.139101582  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f8806acc0
0:00:33.139119732  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f8806acc0 set blocked 0
0:00:33.139134607  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f8806d3a0> blocked: 0
0:00:33.139150307  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f8806acc0
0:00:33.139151406  1525   0x7f94001e60 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)875253523, timestamp-offset=(uint)1457554402, seqnum-offset=(uint)6970, a-framerate=(string)30
0:00:33.139169057  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:33.139252357  1525   0x7f94001830 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f8806acc0: got message type 16 (tag)
0:00:33.139254155  1525   0x7f94001e60 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:33.139289280  1525   0x7f94001e60 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:33.139302840  1525   0x7f94001e60 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:33.139339332  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8806acc0: went from PAUSED to PAUSED (pending PLAYING)
0:00:33.139387007  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f8806acc0> got async-done
0:00:33.139499643  1525   0x7f94002280 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:33.139618400  1525   0x7f94002280 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:33.139668357  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:33.139692743  1525   0x7f94002280 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:33.139729504  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.139745051  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<appsink1> completed state change to PLAYING
0:00:33.139758271  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<appsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.139784142  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink1' changed state to 4(PLAYING) successfully
0:00:33.139800374  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.139812299  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink0> skipping transition from PLAYING to  PLAYING
0:00:33.139824025  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink0' changed state to 4(PLAYING) successfully
0:00:33.139839048  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee3> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.139852232  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee3> completed state change to PLAYING
0:00:33.139864321  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee3> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.139885379  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee3' changed state to 4(PLAYING) successfully
0:00:33.139901129  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee2> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:33.139914251  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee2> completed state change to PLAYING
0:00:33.139925661  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee2> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.139941273  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee2' changed state to 4(PLAYING) successfully
0:00:33.139957247  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.139982001  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.139993678  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage1> skipping transition from PLAYING to  PLAYING
0:00:33.140006297  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:33.140021240  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140032188  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux1> skipping transition from PLAYING to  PLAYING
0:00:33.140043385  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:33.140057152  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140067754  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession1> skipping transition from PLAYING to  PLAYING
0:00:33.140078979  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:33.140091892  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:33.140106432  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:33.140119841  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140146186  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140157570  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:33.140170140  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:33.140184114  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140193488  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse1> skipping transition from PLAYING to  PLAYING
0:00:33.140205111  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:33.140217579  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140229184  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc1> skipping transition from PLAYING to  PLAYING
0:00:33.140242575  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:33.140256596  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140267866  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:33.140279161  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:33.140293700  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140304538  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale1> skipping transition from PLAYING to  PLAYING
0:00:33.140315792  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:33.140331067  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140341972  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue1> skipping transition from PLAYING to  PLAYING
0:00:33.140353305  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:33.140365868  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140376497  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:33.140387803  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:33.140400127  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:33.140412338  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:33.140426951  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:33.140437695  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel1> skipping transition from PLAYING to  PLAYING
0:00:33.140448363  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel1' changed state to 4(PLAYING) successfully
0:00:33.140461289  1525   0x7f94002280 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsrc0' changed state to 4(PLAYING) successfully
0:00:33.140476262  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:33.140488297  1525   0x7f94002280 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:33.140648932  1525   0x7f94001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:33.140789807  1525   0x7f94001830 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f8806acc0: went from PAUSED to PLAYING (pending VOID_PENDING)
[2021-01-01 14:01:56.304] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 14:01:56.304] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 14:01:56.304] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 14:01:56.304] [INFO] Pushing raw buffer directly to appsrc...
0:00:33.159631007  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f582f33b0
0:00:33.159664757  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.033333333
[2021-01-01 14:01:56.304] [INFO] Raw frame fed to appsrc successfully, total frames served: 15
[2021-01-01 14:01:56.304] [INFO] feed_data call completed
0:00:33.159730582  1525   0x7f94001c50 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:35.134961198  1525   0x7f60000e90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:35.135035659  1525   0x7f60000e90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:35.135071873  1525   0x7f60000e90 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:35.135348546  1525   0x7f60000e90 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)875253523, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)6970, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400326255836723163, sr-rtptime=(uint)970194203, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:35.135448857  1525   0x7f50001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f8806d3a0> message send complete
0:00:35.135479457  1525   0x7f50001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f88067ba0 alive
0:00:35.135489532  1525   0x7f94001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:35.135598282  1525   0x7f94001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:35.425677132  1525   0x7f8c0171f0 INFO              rtspstream rtsp-stream.c:2496:on_bye_timeout: 0x7f88041a60: source 0x7f680139c0 bye timeout
0:00:35.426039657  1525   0x7f8c0171f0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3751509392, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)18972, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400326232279235566, sr-rtptime=(uint)3041966097, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:35.943057507  1525   0x7f94000b70 DEBUG             rtspstream rtsp-stream.c:4664:gst_rtsp_stream_recv_rtcp: stream 0x7f8806d3a0: first buffer at time 2:01:59.697154268, base 2:01:56.850055698
0:00:35.943108582  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<appsrc0> queueing buffer 0x7f88052e70
0:00:35.943138782  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<appsrc0> Currently queued: 36 bytes, 1 buffers, 0:00:00.000000000
0:00:35.943187932  1525   0x7f94002070 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<appsrc0> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:35.943244207  1525   0x7f94002070 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:35.943298132  1525   0x7f94002070 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<appsrc0> marking pending DISCONT
0:00:35.943373357  1525   0x7f94002070 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:35.943539882  1525   0x7f94002070 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f8806d3a0: new source 0x7f4c0021d0
0:00:35.943708257  1525   0x7f94002070 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)812415999, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:50983, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:35.943781332  1525   0x7f94002070 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:50983 in 1 transports
0:00:35.943802382  1525   0x7f94002070 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f8806d3a0: found transport 0x7f88069fa0 for source  0x7f4c0021d0
0:00:35.943824982  1525   0x7f94002070 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f8806d3a0: source 0x7f4c0021d0 for transport 0x7f88069fa0
0:00:35.943856907  1525   0x7f94002070 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f8806d3a0: source 0x7f4c0021d0 in transport 0x7f88069fa0 is active
0:00:35.943879807  1525   0x7f94002070 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f88067ba0 alive
0:00:35.943954182  1525   0x7f94002070 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f8806d3a0: new SDES 0x7f4c0021d0
0:00:37.690244916  1525   0x7f60000e90 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)875253523, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)6970, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400326266811399691, sr-rtptime=(uint)970424175, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:37.690321506  1525   0x7f50001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f8806d3a0> message send complete
0:00:37.690355956  1525   0x7f50001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f88067ba0 alive
0:00:39.314351381  1525   0x7f8c0171f0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3751509392, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)18972, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400326232279235566, sr-rtptime=(uint)3041966097, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 14:02:03.363] [INFO] === RTSP Server Statistics ===
[2021-01-01 14:02:03.363] [INFO] Uptime: 40.0 seconds
[2021-01-01 14:02:03.363] [INFO] Total connections: 2
[2021-01-01 14:02:03.363] [INFO] Active connections: 2
[2021-01-01 14:02:03.363] [INFO] Frames served: 15
[2021-01-01 14:02:03.363] [INFO] Clients connected: 0
[2021-01-01 14:02:03.363] [INFO] Error count: 0
0:00:41.363338431  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<appsrc0> queueing buffer 0x7f88051910
0:00:41.363395431  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<appsrc0> Currently queued: 36 bytes, 1 buffers, 0:00:00.000000000
0:00:41.363441431  1525   0x7f94002070 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<appsrc0> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:41.363549606  1525   0x7f94002070 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f8806d3a0: source 0x7f4c0021d0 in transport 0x7f88069fa0 is active
0:00:41.363570631  1525   0x7f94002070 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f88067ba0 alive
0:00:42.637051540  1525   0x7f60000e90 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)875253523, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)6970, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400326288057765386, sr-rtptime=(uint)970869387, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:42.637125705  1525   0x7f50001b60 DEBUG             rtspstream rtsp-stream.c:4821:on_message_sent:<GstRTSPStream@0x7f8806d3a0> message send complete
0:00:42.637175555  1525   0x7f50001b60 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f88067ba0 alive
0:00:43.603028255  1525   0x7f8c0171f0 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)3751509392, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)18972, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400326232279235566, sr-rtptime=(uint)3041966097, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:43.665802380  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f98006630: received a request TEARDOWN rtsp://***********5:8554/stream/ 1.0
0:00:43.665913280  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f8806acc0, target state PLAYING
0:00:43.665935730  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:43.665953130  1525   0x7f94000b70 INFO              rtspstream rtsp-stream.c:4787:update_transport: removing TCP ***********
0:00:43.665974080  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f8806acc0 do_state 1
0:00:43.665990080  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f8806acc0
0:00:43.666007305  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f8806acc0
0:00:43.666024155  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:43.666039905  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:43.666056480  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f8806acc0
0:00:43.666247505  1525   0x7f94000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:43.666353105  1525   0x7f94000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:43.666409280  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666427030  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink1> skipping transition from PLAYING to  PLAYING
0:00:43.666444980  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink1' changed state to 4(PLAYING) successfully
0:00:43.666466955  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<appsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666483230  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<appsink0> skipping transition from PLAYING to  PLAYING
0:00:43.666512555  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsink0' changed state to 4(PLAYING) successfully
0:00:43.666534030  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee3> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666550230  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee3> skipping transition from PLAYING to  PLAYING
0:00:43.666567230  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee3' changed state to 4(PLAYING) successfully
0:00:43.666588455  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee2> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666604955  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee2> skipping transition from PLAYING to  PLAYING
0:00:43.666621430  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee2' changed state to 4(PLAYING) successfully
0:00:43.666644130  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666680355  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666696980  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage1> skipping transition from PLAYING to  PLAYING
0:00:43.666713930  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpstorage1' changed state to 4(PLAYING) successfully
0:00:43.666736255  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666752130  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux1> skipping transition from PLAYING to  PLAYING
0:00:43.666769080  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpssrcdemux1' changed state to 4(PLAYING) successfully
0:00:43.666790205  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666806155  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession1> skipping transition from PLAYING to  PLAYING
0:00:43.666823105  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin1> child 'rtpsession1' changed state to 4(PLAYING) successfully
0:00:43.666847305  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin1> completed state change to PLAYING
0:00:43.666866655  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin1' changed state to 4(PLAYING) successfully
0:00:43.666886355  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666922255  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666940330  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:43.666957705  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'pay0' changed state to 4(PLAYING) successfully
0:00:43.666978930  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.666995180  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse1> skipping transition from PLAYING to  PLAYING
0:00:43.667011855  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'h264parse1' changed state to 4(PLAYING) successfully
0:00:43.667032580  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.667048480  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc1> skipping transition from PLAYING to  PLAYING
0:00:43.667065130  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'mpph264enc1' changed state to 4(PLAYING) successfully
0:00:43.667086255  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.667102380  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter1> skipping transition from PLAYING to  PLAYING
0:00:43.667119030  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'capsfilter1' changed state to 4(PLAYING) successfully
0:00:43.667139880  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.667156030  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale1> skipping transition from PLAYING to  PLAYING
0:00:43.667173105  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'videoscale1' changed state to 4(PLAYING) successfully
0:00:43.667194055  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.667211430  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue1> skipping transition from PLAYING to  PLAYING
0:00:43.667228930  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'queue1' changed state to 4(PLAYING) successfully
0:00:43.667248055  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.667263680  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:43.667280305  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin1> child 'source' changed state to 4(PLAYING) successfully
0:00:43.667298530  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin1> completed state change to PLAYING
0:00:43.667316455  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin1' changed state to 4(PLAYING) successfully
0:00:43.667338380  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:43.667354380  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel1> skipping transition from PLAYING to  PLAYING
0:00:43.667371105  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel1' changed state to 4(PLAYING) successfully
0:00:43.667390755  1525   0x7f94000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'appsrc0' changed state to 4(PLAYING) successfully
0:00:43.667411605  1525   0x7f94000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:43.667425955  1525   0x7f94000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:43.667462680  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<appsrc0> queue event: eos event: 0x7f88059c70, time 99:99:99.999999999, seq-num 354, (NULL)
0:00:43.667505130  1525   0x7f94000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f88059c70, time 99:99:99.999999999, seq-num 354, (NULL)
0:00:43.667503705  1525   0x7f94002070 DEBUG                 appsrc gstappsrc.c:1683:gst_app_src_create:<appsrc0> pop event eos event: 0x7f88059c70, time 99:99:99.999999999, seq-num 354, (NULL)
0:00:43.667547955  1525   0x7f94000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f88069d60
0:00:43.667566080  1525   0x7f94000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f8806acc0 was not prepared
0:00:43.667581530  1525   0x7f94000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f8806acc0 is already unpreparing
0:00:43.667617905  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f98006630: sending close message
0:00:43.667739380  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f98006630: session 0x7f88067ba0 removed
0:00:43.667758430  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f98006630: unwatch session 0x7f88067ba0
0:00:43.667787555  1525   0x7f94000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f88067ba0
0:00:43.667894405  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f98006630: connection closed
0:00:43.667917305  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f98006630: watch destroyed
0:00:43.667940630  1525   0x7f94000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x556a000050> unmanage client 0x7f98006630
0:00:43.667970855  1525   0x7f94000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f98007550
0:00:43.667992180  1525   0x7f94000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f98006630
0:00:43.668110405  1525   0x7f94000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f98008020
^C[2021-01-01 14:02:10.153] [INFO] Received signal 2, shutting down...
[2021-01-01 14:02:10.153] [INFO] RTSP server main loop stopped
[2021-01-01 14:02:10.155] [INFO] RTSP server stopped
[2021-01-01 14:02:10.366] [INFO] RTSP server shutdown complete