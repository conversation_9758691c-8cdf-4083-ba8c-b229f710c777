root@rk3576-buildroot:/# rtsp_server --hw-encoder --gst-debug 4
[2021-01-01 16:43:37.586] [INFO] === RTSP Server Configuration ===
[2021-01-01 16:43:37.586] [INFO] DDS Topic: Video_Frames
[2021-01-01 16:43:37.586] [INFO] Server: 0.0.0.0:8554/stream
[2021-01-01 16:43:37.586] [INFO] Output: 1280x720@30fps, H264, 2000000 bps
[2021-01-01 16:43:37.586] [INFO] GOP Size: 30
[2021-01-01 16:43:37.586] [INFO] Hardware Encoder: yes
[2021-01-01 16:43:37.586] [INFO] Max Clients: 10
[2021-01-01 16:43:37.586] [INFO] Buffer Size: 5
[2021-01-01 16:43:37.586] [INFO] Zero Copy: yes
[2021-01-01 16:43:37.586] [INFO] GStreamer Debug Level: 4
[2021-01-01 16:43:37.586] [INFO] Adaptive Bitrate: 500000 - 5000000 bps
[2021-01-01 16:43:37.586] [INFO] GStreamer debug level set to: 4 (*:4)
0:00:00.000083651  1563   0x556eff28f0 INFO                GST_INIT gst.c:576:init_pre: Initializing GStreamer Core Library version 1.22.9
0:00:00.000099971  1563   0x556eff28f0 INFO                GST_INIT gst.c:577:init_pre: Using library installed in /lib
0:00:00.000112628  1563   0x556eff28f0 INFO                GST_INIT gst.c:595:init_pre: Linux rk3576-buildroot 6.1.99-rk3576 #1 SMP Mon Jun 30 10:03:13 CST 2025 aarch64
0:00:00.000344696  1563   0x556eff28f0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.000842021  1563   0x556eff28f0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001068353  1563   0x556eff28f0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001174405  1563   0x556eff28f0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001189683  1563   0x556eff28f0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001253777  1563   0x556eff28f0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007380093  1563   0x556eff28f0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.006079 seconds
0:00:00.007452438  1563   0x556eff28f0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.008166555  1563   0x556eff28f0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.008217755  1563   0x556eff28f0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.008237434  1563   0x556eff28f0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.008247971  1563   0x556eff28f0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.008254941  1563   0x556eff28f0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
[2021-01-01 16:43:37.595] [INFO] GStreamer initialized successfully
Start init DDS reader: Video_Frames
Create share memery qos success
Create participant success
Register type success
Create subscriber success
Create topic success
DDS Reader initialized for topic: Video_Frames[2021-01-01 16:43:37.601] [INFO] Waiting for first frame from DDS topic: Video_Frames
Subscriber matched
[2021-01-01 16:43:37.801] [INFO] First frame received: 640x480 format=1448695129, output will be: 1280x720@30fps
[2021-01-01 16:43:37.802] [INFO] RTSPMediaFactory initialized for topic: Video_Frames
[2021-01-01 16:43:37.802] [INFO] Pipeline: ( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=2 max-size-time=0 max-size-bytes=0 ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=30 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )
0:00:00.215290666  1563   0x556eff28f0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x556f0d2280 for path /stream

(<unknown>:1563): GLib-GObject-CRITICAL **: 16:43:37.802: g_object_set_is_valid_property: object class 'GstRTSPServer' has no property named 'timeout'
[2021-01-01 16:43:37.802] [INFO] RTSP server initialized: 0.0.0.0:8554/stream
0:00:00.215409577  1563   0x556eff28f0 DEBUG             rtspserver rtsp-server.c:882:gst_rtsp_server_create_socket:<GstRTSPServer@0x556f0cfe70> getting address info of 0.0.0.0/8554
0:00:00.215837711  1563   0x556eff28f0 DEBUG             rtspserver rtsp-server.c:967:gst_rtsp_server_create_socket:<GstRTSPServer@0x556f0cfe70> opened sending server socket
0:00:00.215869367  1563   0x556eff28f0 DEBUG             rtspserver rtsp-server.c:994:gst_rtsp_server_create_socket:<GstRTSPServer@0x556f0cfe70> listening on server socket 0x556f221e90 with queue of 5
[2021-01-01 16:43:37.802] [INFO] RTSP server started on 0.0.0.0:8554/stream
[2021-01-01 16:43:37.802] [INFO] RTSP server is running. Access stream at: rtsp://0.0.0.0:8554/stream
[2021-01-01 16:43:37.802] [INFO] Press Ctrl+C to stop the server
[2021-01-01 16:43:37.802] [INFO] RTSP server main loop started
[2021-01-01 16:43:47.803] [INFO] === RTSP Server Statistics ===
[2021-01-01 16:43:47.803] [INFO] Uptime: 10.0 seconds
[2021-01-01 16:43:47.803] [INFO] Total connections: 0
[2021-01-01 16:43:47.803] [INFO] Active connections: 0
[2021-01-01 16:43:47.803] [INFO] Frames served: 0
[2021-01-01 16:43:47.803] [INFO] Clients connected: 0
[2021-01-01 16:43:47.803] [INFO] Error count: 0
0:00:11.285792705  1563   0x7f78000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f78006630 connected to server ip ***********5, ipv6 = 0
0:00:11.285839430  1563   0x7f78000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f78006630 ip ***********:50935
0:00:11.285865230  1563   0x7f78000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x556f0cfe70> manage client 0x7f78006630
[2021-01-01 16:43:48.872] [INFO] === CLIENT CONNECTED ===
[2021-01-01 16:43:48.872] [INFO] Active connections: 1, Total connections: 1
0:00:11.286308189  1563   0x7f74000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f78006f10
0:00:11.286317105  1563   0x7f78000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f78006630: attaching to context 0x7f78007440
0:00:11.286574939  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f78006630: received a request OPTIONS rtsp://***********5:8554/stream 1.0
0:00:11.289129248  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f78006630: received a request DESCRIBE rtsp://***********5:8554/stream 1.0
0:00:11.289161086  1563   0x7f74000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556f0d2280 for path /stream
0:00:11.289179841  1563   0x7f74000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( appsrc name=source is-live=true do-timestamp=true format=time max-bytes=0 block=false caps="video/x-raw,format=YUY2,width=640,height=480,framerate=30/1" ! queue max-size-buffers=2 max-size-time=0 max-size-bytes=0 ! videoscale ! video/x-raw,width=1280,height=720 ! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=30 rc-mode=1 ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )'
0:00:11.289642067  1563   0x7f74000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstapp.so" loaded
0:00:11.289907830  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "appsrc"
0:00:11.289944220  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f68007810> adding pad 'src'
0:00:11.289963706  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 0
0:00:11.289994148  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:1854:gst_app_src_set_caps:<source> setting caps to video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:11.291105049  1563   0x7f74000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:11.291248196  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "queue"
0:00:11.291293414  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f6800d160> adding pad 'sink'
0:00:11.291322339  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstQueue@0x7f6800d160> adding pad 'src'
0:00:11.291910156  1563   0x7f74000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoconvertscale.so" loaded
0:00:11.292248873  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "videoscale"
0:00:11.292285978  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f680179c0> adding pad 'sink'
0:00:11.292309239  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f680179c0> adding pad 'src'
0:00:11.294397952  1563   0x7f74000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:11.294656583  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:11.294693599  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f6801c400> adding pad 'sink'
0:00:11.294716650  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f6801c400> adding pad 'src'
0:00:11.296531952  1563   0x7f74000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideoparsersbad.so" loaded
0:00:11.296740298  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "h264parse"
0:00:11.296776555  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f68024c70> adding pad 'sink'
0:00:11.296799412  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseParse@0x7f68024c70> adding pad 'src'
0:00:11.296835695  1563   0x7f74000b70 INFO               baseparse gstbaseparse.c:4064:gst_base_parse_set_pts_interpolation:<GstH264Parse@0x7f68024c70> PTS interpolation: no
0:00:11.296849180  1563   0x7f74000b70 INFO               baseparse gstbaseparse.c:4082:gst_base_parse_set_infer_ts:<GstH264Parse@0x7f68024c70> TS inferring: no
0:00:11.298534712  1563   0x7f74000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:11.298758757  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:11.298814286  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f68034a90> adding pad 'src'
0:00:11.298855883  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f68034a90> adding pad 'sink'
0:00:11.298902094  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:11.298998358  1563   0x7f74000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstAppSrc named source to some pad of GstQueue named queue0 (0/0) with caps "(NULL)"
0:00:11.299014046  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element source:(any) to element queue0:(any)
0:00:11.299029666  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link source:src and queue0:sink
0:00:11.299047752  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:11.299067596  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:11.299085396  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: source and queue0 in same bin, no need for ghost pads
0:00:11.299114213  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link source:src and queue0:sink
0:00:11.299127890  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:11.299142703  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<queue0:src> pad has no peer
0:00:11.299167722  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked source:src and queue0:sink, successful
0:00:11.299178601  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.299191106  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<source:src> Received event on flushing pad. Discarding
0:00:11.299228661  1563   0x7f74000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstQueue named queue0 to some pad of GstVideoScale named videoscale0 (0/0) with caps "(NULL)"
0:00:11.299242351  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element queue0:(any) to element videoscale0:(any)
0:00:11.299257208  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link queue0:src and videoscale0:sink
0:00:11.299275184  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:11.299294114  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:11.299671586  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: queue0 and videoscale0 in same bin, no need for ghost pads
0:00:11.299692217  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link queue0:src and videoscale0:sink
0:00:11.299708730  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:11.299726086  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<videoscale0:src> pad has no peer
0:00:11.300047760  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked queue0:src and videoscale0:sink, successful
0:00:11.300058531  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.300069052  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<queue0:src> Received event on flushing pad. Discarding
0:00:11.300100843  1563   0x7f74000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstVideoScale named videoscale0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "video/x-raw, width=(int)1280, height=(int)720"
0:00:11.300121893  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:11.300207721  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f6803e3c0> adding pad 'sink'
0:00:11.300230214  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f6803e3c0> adding pad 'src'
0:00:11.300248183  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:11.300277563  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:11.300293815  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.300312374  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element videoscale0:(any) to element capsfilter0:sink
0:00:11.300324445  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:11.300336617  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: videoscale0 and capsfilter0 in same bin, no need for ghost pads
0:00:11.300353912  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link videoscale0:src and capsfilter0:sink
0:00:11.300373739  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:11.300716248  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:11.300756561  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked videoscale0:src and capsfilter0:sink, successful
0:00:11.300766652  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.300777640  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<videoscale0:src> Received event on flushing pad. Discarding
0:00:11.300797932  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mpph264enc0:(any)
0:00:11.300809992  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:11.300823880  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mpph264enc0:sink
0:00:11.300841789  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:11.301232627  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:11.301283118  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mpph264enc0 in same bin, no need for ghost pads
0:00:11.301302600  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mpph264enc0:sink
0:00:11.301323552  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:863:gst_app_src_internal_get_caps:<source> caps: (NULL)
0:00:11.301709743  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:11.301768201  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mpph264enc0:sink, successful
0:00:11.301778366  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.301788637  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:11.301814439  1563   0x7f74000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstH264Parse named h264parse0 (0/0) with caps "(NULL)"
0:00:11.301827500  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element h264parse0:(any)
0:00:11.301841353  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and h264parse0:sink
0:00:11.301859517  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:11.301878021  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and h264parse0 in same bin, no need for ghost pads
0:00:11.301894142  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and h264parse0:sink
0:00:11.301908942  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<h264parse0:src> pad has no peer
0:00:11.301927740  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and h264parse0:sink, successful
0:00:11.301936759  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.301946295  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:11.301967054  1563   0x7f74000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstH264Parse named h264parse0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:11.301979225  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element h264parse0:(any) to element pay0:(any)
0:00:11.301992307  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link h264parse0:src and pay0:sink
0:00:11.302010464  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:11.302027641  1563   0x7f74000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: h264parse0 and pay0 in same bin, no need for ghost pads
0:00:11.302042982  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link h264parse0:src and pay0:sink
0:00:11.302058008  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:11.302073042  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked h264parse0:src and pay0:sink, successful
0:00:11.302082007  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.302091261  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<h264parse0:src> Received event on flushing pad. Discarding
0:00:11.302214615  1563   0x7f74000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:11.302234430  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f68034a90
0:00:11.302245901  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:11.302276366  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2383:gst_rtsp_media_create_stream: media 0x7f6803ffb0: creating stream with index 0 and payloader <pay0>
0:00:11.302402563  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:11.302417629  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:11.302426697  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.302436721  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:11.302456922  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:11.302494584  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:329:gst_rtsp_stream_init: new stream 0x7f68041d70
0:00:11.302510587  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f68041d70> set retransmission time 0
0:00:11.302522346  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f68041d70> Enabling rate control
0:00:11.302543258  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:2120:gst_rtsp_stream_set_retransmission_pt:<GstRTSPStream@0x7f68041d70> set retransmission pt 97
0:00:11.302557534  1563   0x7f74000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:11.302578560  1563   0x7f74000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:11.302608622  1563   0x7f74000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:11.302627737  1563   0x7f74000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:11.302644940  1563   0x7f74000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:11.302680940  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:11.302770102  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:2076:gst_rtsp_stream_set_retransmission_time:<GstRTSPStream@0x7f68041d70> set retransmission time 0
[2021-01-01 16:43:48.889] [INFO] === MEDIA CONFIGURE CALLBACK TRIGGERED ===
[2021-01-01 16:43:48.889] [INFO] factory: 0x556f0d2280, media: 0x7f6803ffb0, user_data: 0x556f00ae20
[2021-01-01 16:43:48.889] [INFO] Calling configure_media...
[2021-01-01 16:43:48.889] [INFO] === Media Configure Callback Triggered ===
[2021-01-01 16:43:48.889] [INFO] Got media pipeline: 0x7f680366e0
0:00:11.302862448  1563   0x7f74000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element source
[2021-01-01 16:43:48.889] [INFO] Found appsrc element: 0x7f68007810
[2021-01-01 16:43:48.889] [INFO] Connected appsrc signals
0:00:11.302923898  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:2074:gst_app_src_set_max_bytes:<source> setting max-bytes to 545460846592
0:00:11.302938639  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
0:00:11.302958601  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:2337:gst_app_src_set_latencies:<source> posting latency changed
[2021-01-01 16:43:48.889] [INFO] Attempting to push initial frame to trigger pipeline...
[2021-01-01 16:43:48.889] [INFO] Got initial frame for pipeline trigger: 640x480, format=0x56595559
0:00:11.303458577  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f6802f290
0:00:11.303487630  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 16:43:48.890] [INFO] Initial frame push result: 0
[2021-01-01 16:43:48.890] [INFO] Successfully pushed initial raw frame to trigger pipeline
[2021-01-01 16:43:48.890] [INFO] Configured appsrc properties
[2021-01-01 16:43:48.890] [INFO] Media configured successfully
[2021-01-01 16:43:48.890] [INFO] configure_media call completed
0:00:11.303594535  1563   0x7f74000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f6803ffb0 for url /stream
0:00:11.303753127  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f6803ffb0
0:00:11.303765904  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 2
0:00:11.303792286  1563   0x7f74000d80 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f68039a30
0:00:11.305079555  1563   0x7f74000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:11.305117962  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:11.305637781  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:3623:wait_preroll: wait to preroll pipeline
0:00:11.305652691  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2834:gst_rtsp_media_get_status: waiting for status change
0:00:11.305676114  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:11.305700437  1563   0x7f74000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:11.305747821  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:11.305767429  1563   0x7f74000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:11.305798112  1563   0x7f74000d80 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f68041d70 joining bin as session 0
0:00:11.305825336  1563   0x7f74000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:11.306243234  1563   0x7f74000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:11.306354958  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f6400b3b0> adding pad 'sink'
0:00:11.306378677  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f6400b3b0> adding pad 'rtcp_sink'
0:00:11.306396647  1563   0x7f74000d80 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:11.306484283  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f6400c830> adding pad 'src'
0:00:11.306498641  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f6400c830> adding pad 'sink'
0:00:11.306633383  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:11.306654585  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:11.306666963  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:11.306724502  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:11.306763435  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:11.306777764  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:11.306826192  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:11.306841266  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:11.306850956  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.306883665  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:11.306915642  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:11.306931487  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:11.306940990  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.306957651  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:11.306976834  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:11.307018821  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:11.307028749  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.307040858  1563   0x7f74000d80 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:11.307059382  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:11.307222904  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:11.307267764  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:11.307281912  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:11.307292447  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.307319972  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:11.307363573  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:11.307392129  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:11.307411482  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:11.307423993  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:11.307440437  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:11.307454335  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:11.307463508  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.307505614  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:11.307519101  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:11.307528063  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.307546197  1563   0x7f74000d80 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:11.307588440  1563   0x7f74000d80 DEBUG             rtspstream rtsp-stream.c:4061:gst_rtsp_stream_join_bin:<GstRTSPStream@0x7f68041d70> successfully joined bin
0:00:11.307609850  1563   0x7f74000d80 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f6803ffb0
0:00:11.307621743  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6803ffb0 set blocked 1
0:00:11.307633341  1563   0x7f74000d80 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f68041d70> blocked: 1
0:00:11.307647868  1563   0x7f74000d80 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f6803ffb0
0:00:11.307660350  1563   0x7f74000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f6803ffb0
0:00:11.307686625  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:11.307719157  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:11.307732522  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:11.307744575  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.307770033  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:11.307786556  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:11.307799208  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:11.307810894  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.307825470  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:11.307840300  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:11.307852559  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:11.307863862  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.307884927  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:11.307898718  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:11.307909814  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.307925663  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:11.307938936  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:11.307975869  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:11.307989436  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:11.308000848  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.308017041  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:11.308031239  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current NULL pending VOID_PENDING, desired next READY
0:00:11.308044573  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to READY
0:00:11.308056100  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.308072081  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 2(READY) successfully
0:00:11.308086902  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:11.308101907  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:11.308113123  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.308129238  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:11.308143234  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:11.308155522  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:11.308166479  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.308182519  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:11.308197229  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current NULL pending VOID_PENDING, desired next READY
0:00:11.308209640  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to READY
0:00:11.308220580  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.308241105  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 2(READY) successfully
0:00:11.308256062  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current NULL pending VOID_PENDING, desired next READY
0:00:11.308268593  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to READY
0:00:11.308279320  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.308294314  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 2(READY) successfully
0:00:11.308307210  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current NULL pending VOID_PENDING, desired next READY
0:00:11.308319569  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to READY
0:00:11.308330472  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.308346224  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 2(READY) successfully
0:00:11.308358775  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:11.308370241  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.308385606  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:11.308400269  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:11.308412092  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:11.308426033  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:11.308446022  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.308474940  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.308493254  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:11.308504904  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.308524693  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:11.308540246  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.308557268  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:11.308569028  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.308584362  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:11.308598124  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.308614533  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:11.308625888  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.308641748  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:11.308657355  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:11.308668704  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.308684583  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:11.308697406  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.308725026  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.308748191  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:11.308759902  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.308776487  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:11.308790738  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.309045360  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PAUSED
0:00:11.309061713  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.309081545  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 3(PAUSED) successfully
0:00:11.309098703  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.309480157  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:11.309509786  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.309536295  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:11.309557197  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.309578982  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:11.309591493  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.309608590  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:11.309625145  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.309643646  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PAUSED
0:00:11.309655468  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.309671648  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 3(PAUSED) successfully
0:00:11.309686601  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.309721267  1563   0x7f74000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f6800d970 on task 0x7f6405ea00
0:00:11.309734408  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<queue0:src> created task 0x7f6405ea00
0:00:11.309924757  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PAUSED
0:00:11.309941051  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.309971601  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 3(PAUSED) successfully
0:00:11.309987817  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current READY pending VOID_PENDING, desired next PAUSED
0:00:11.310004588  1563   0x7f74000d80 DEBUG                 appsrc gstappsrc.c:1082:gst_app_src_start:<source> starting
0:00:11.310031883  1563   0x7f74000d80 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<source> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:11.310057156  1563   0x7f74000d80 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f68007b80 on task 0x7f6405efc0
0:00:11.310068575  1563   0x7f74000d80 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<source:src> created task 0x7f6405efc0
0:00:11.310178065  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PAUSED
0:00:11.310191698  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.310210139  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'source' changed state to 3(PAUSED) successfully without preroll
0:00:11.310229836  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:11.310242238  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.310276730  1563   0x7f74000ff0 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "source"
0:00:11.310282128  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:11.310322246  1563   0x7f74000d80 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:11.310331305  1563   0x7f74000ff0 FIXME                default gstutils.c:4036:gst_pad_create_stream_id_internal:<source:src> Creating random stream-id, consider implementing a deterministic way of creating a stream-id
0:00:11.310333850  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:11.310366732  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.310382891  1563   0x7f74000d80 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f6803ffb0
0:00:11.310396383  1563   0x7f74000d80 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6803ffb0
0:00:11.310462279  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:11.310483553  1563   0x7f74000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:11.310507683  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.310531779  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.310544639  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:11.310556107  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.310574327  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:11.310589397  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.310613467  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:11.310625182  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.310642895  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:11.310657783  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.310726501  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:11.310741180  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.310762392  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:11.310778595  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:11.310791005  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.310818017  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:11.310849861  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.310863727  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:11.310874791  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.310891445  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:11.310907079  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.310920298  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<h264parse0> completed state change to PLAYING
0:00:11.310931821  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<h264parse0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.310948530  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:11.310963510  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.310977294  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:11.310988542  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.311004140  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:11.311018168  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.311030741  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:11.311041401  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.311056615  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:11.311070427  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.311082211  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<videoscale0> completed state change to PLAYING
0:00:11.311092909  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<videoscale0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.311109467  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:11.311123649  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.311135513  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<queue0> completed state change to PLAYING
0:00:11.311146332  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<queue0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.311174635  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:11.311195394  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<source> completed state change to PLAYING
0:00:11.311208891  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<source> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.311225395  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:11.311238875  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:11.311245955  1563   0x7f74000ff0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, framerate=(fraction)30/1
0:00:11.311250915  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.311297399  1563   0x7f74000d80 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:11.311311204  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:11.311322531  1563   0x7f74000d80 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.311425530  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:11.311472582  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6803ffb0: went from NULL to READY (pending PAUSED)
0:00:11.311481005  1563   0x7f74000ff0 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:11.311536255  1563   0x7f74000ff0 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<source> marking pending DISCONT
[2021-01-01 16:43:48.898] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 16:43:48.898] [INFO] appsrc: 0x7f68007810, unused: 4096, user_data: 0x556f00ae20
[2021-01-01 16:43:48.898] [INFO] Calling feed_data...
0:00:11.311641324  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6803ffb0: went from READY to PAUSED (pending VOID_PENDING)
[2021-01-01 16:43:48.898] [INFO] === FEED DATA CALLED ===
[2021-01-01 16:43:48.898] [INFO] appsrc: 0x7f68007810
0:00:11.311672020  1563   0x7f74000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f6803ffb0: got message type 2048 (new-clock)
[2021-01-01 16:43:48.898] [INFO] DDS reader is initialized
[2021-01-01 16:43:48.898] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 16:43:48.898] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 16:43:48.898] [INFO] Creating GstBuffer for raw frame data...
0:00:11.311802672  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6803ffb0: went from PAUSED to PLAYING (pending VOID_PENDING)
0:00:11.312114992  1563   0x7f74000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
0:00:11.312355229  1563   0x7f74000de0 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:11.312379497  1563   0x7f74000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)30/1, pixel-aspect-ratio=(fraction)3/4
[2021-01-01 16:43:48.899] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 16:43:48.899] [INFO] Pushing raw buffer directly to appsrc...
0:00:11.312523930  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f60001f70
0:00:11.312553255  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
0:00:11.312556755  1563   0x7f74000de0 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applyin[2021-01-01 16:43:48.899] [INFO] Raw frame fed to appsrc successfully, total frames served: 2
g YUY2 1280x720 (2560x720)
[2021-01-01 16:43:48.899] [INFO] feed_data call completed
0:00:11.312627355  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
[2021-01-01 16:43:48.899] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 16:43:48.899] [INFO] appsrc: 0x7f68007810, unused: 4096, user_data: 0x556f00ae20
[2021-01-01 16:43:48.899] [INFO] Calling feed_data...
[2021-01-01 16:43:48.899] [INFO] === FEED DATA CALLED ===
[2021-01-01 16:43:48.899] [INFO] appsrc: 0x7f68007810
[2021-01-01 16:43:48.899] [INFO] DDS reader is initialized
[2021-01-01 16:43:48.899] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 16:43:48.899] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 16:43:48.899] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 16:43:48.900] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 16:43:48.900] [INFO] Pushing raw buffer directly to appsrc...
0:00:11.313522755  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f6012e800
0:00:11.313553905  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 16:43:48.900] [INFO] Raw frame fed to appsrc successfully, total frames served: 3
[2021-01-01 16:43:48.900] [INFO] feed_data call completed
0:00:11.313616805  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:11.314181789  1563   0x7f74000de0 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2
[2021-01-01 16:43:48.901] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 16:43:48.901] [INFO] appsrc: 0x7f68007810, unused: 4096, user_data: 0x556f00ae20
[2021-01-01 16:43:48.901] [INFO] Calling feed_data...
[2021-01-01 16:43:48.901] [INFO] === FEED DATA CALLED ===
[2021-01-01 16:43:48.901] [INFO] appsrc: 0x7f68007810
[2021-01-01 16:43:48.901] [INFO] DDS reader is initialized
[2021-01-01 16:43:48.901] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 16:43:48.901] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 16:43:48.901] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 16:43:48.902] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 16:43:48.902] [INFO] Pushing raw buffer directly to appsrc...
0:00:11.315883605  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f601c4c00
0:00:11.315923305  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 16:43:48.902] [INFO] Raw frame fed to appsrc successfully, total frames served: 4
[2021-01-01 16:43:48.902] [INFO] feed_data call completed
0:00:11.316087530  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:11.333250865  1563   0x7f74000de0 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f68021130 on task 0x7f5c0367c0
0:00:11.333278684  1563   0x7f74000de0 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f5c0367c0
[2021-01-01 16:43:48.920] [INFO] === NEED DATA CALLBACK TRIGGERED ===
[2021-01-01 16:43:48.920] [INFO] appsrc: 0x7f68007810, unused: 4096, user_data: 0x556f00ae20
[2021-01-01 16:43:48.920] [INFO] Calling feed_data...
[2021-01-01 16:43:48.920] [INFO] === FEED DATA CALLED ===
[2021-01-01 16:43:48.920] [INFO] appsrc: 0x7f68007810
[2021-01-01 16:43:48.920] [INFO] DDS reader is initialized
[2021-01-01 16:43:48.920] [INFO] Attempting to read DDS frame with 100ms timeout...
[2021-01-01 16:43:48.920] [INFO] Successfully read DDS frame: 640x480, format=0x56595559, size=614400 bytes
[2021-01-01 16:43:48.920] [INFO] Creating GstBuffer for raw frame data...
[2021-01-01 16:43:48.921] [INFO] GstBuffer filled with 614400 bytes of raw frame data
[2021-01-01 16:43:48.921] [INFO] Pushing raw buffer directly to appsrc...
0:00:11.335091305  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:2684:gst_app_src_push_internal:<source> queueing buffer 0x7f6025afb0
0:00:11.335163055  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1556:gst_app_src_update_queued_push:<source> Currently queued: 614400 bytes, 1 buffers, 0:00:00.000000000
[2021-01-01 16:43:48.922] [INFO] Raw frame fed to appsrc successfully, total frames served: 5
[2021-01-01 16:43:48.922] [INFO] feed_data call completed
0:00:11.335971605  1563   0x7f74000ff0 DEBUG                 appsrc gstappsrc.c:1452:gst_app_src_update_queued_pop:<source> Currently queued: 0 bytes, 0 buffers, 0:00:00.000000000
0:00:11.337168692  1563   0x7f74001200 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:11.338489127  1563   0x7f74001200 INFO               baseparse gstbaseparse.c:4108:gst_base_parse_set_latency:<h264parse0> min/max latency 0:00:00.000000000, 0:00:00.000000000
0:00:11.338577638  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:11.338623025  1563   0x7f74000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:11.338670702  1563   0x7f74001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)avc, alignment=(string)au, profile=(string)constrained-baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)3/4, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)bt709, chroma-site=(string)mpeg2, coded-picture-structure=(string)frame, chroma-format=(string)4:2:0, bit-depth-luma=(uint)8, bit-depth-chroma=(uint)8, parsed=(boolean)true, codec_data=(buffer)0142c028ffe100196742c0288d8d502802d908000003000800000301e47840215001000468ce31b2
0:00:11.338893206  1563   0x7f74001200 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2763916419, timestamp-offset=(uint)776672932, seqnum-offset=(uint)10370, a-framerate=(string)30
0:00:11.338936090  1563   0x7f74001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:11.338967130  1563   0x7f74001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:11.338993898  1563   0x7f74001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:11.339090772  1563   0x7f74001200 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f68041d70: new sender source 0x7f4c011780
0:00:11.339158825  1563   0x7f74001200 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2763916419, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)10370, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:11.339211757  1563   0x7f74001200 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:11.339253631  1563   0x7f74001200 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f68041d70 received caps 0x7f4c00d340, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", profile-level-id=(string)42c028, profile=(string)constrained-baseline, payload=(int)96, ssrc=(uint)2763916419, timestamp-offset=(uint)776672932, seqnum-offset=(uint)10370, a-framerate=(string)30
0:00:11.339498053  1563   0x7f74001200 INFO               videometa gstvideometa.c:1100:gst_video_time_code_meta_api_get_type: registering
0:00:11.339608318  1563   0x7f74001200 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:00:11.339666916  1563   0x7f74001200 DEBUG             rtspstream rtsp-stream.c:5376:rtp_pad_blocking:<rtpbin0:send_rtp_src_0> Now blocking
0:00:11.339687413  1563   0x7f74001200 DEBUG             rtspstream rtsp-stream.c:5378:rtp_pad_blocking:<GstRTSPStream@0x7f68041d70> position: 447088:43:48.746532000
0:00:11.339750003  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3342:default_handle_message:<GstRTSPMedia@0x7f6803ffb0> media received blocking message, num_complete_sender_streams = 0, is_complete = 0
0:00:11.339764388  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3355:default_handle_message:<pay0> media is blocking
0:00:11.339773973  1563   0x7f74000d80 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:11.339783500  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:11.339810399  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:11.339824814  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f6803ffb0 is prerolled
0:00:11.339848853  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:11.339900441  1563   0x7f74000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:11.339970402  1563   0x7f74000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:11.339984857  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:4564:default_suspend: media 0x7f6803ffb0 no suspend
0:00:11.339995230  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 4
0:00:11.340013944  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://***********5:8554/stream/
0:00:11.361180728  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f78006630: received a request SETUP rtsp://***********5:8554/stream/stream=0 1.0
0:00:11.361216138  1563   0x7f74000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556f0d2280 for path /stream/stream=0
0:00:11.361231254  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f6803ffb0 for path /stream
0:00:11.361242619  1563   0x7f74000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:11.361253810  1563   0x7f74000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f6803ffb0 was not prepared
0:00:11.361339115  1563   0x7f74000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f68042c00
0:00:11.361357342  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f68042c00
0:00:11.361388624  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=49858-49859
0:00:11.361401505  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:11.361409897  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:11.361439293  1563   0x7f74000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f6803ffb0 in session 0x7f68042320
0:00:11.361453057  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:1866:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f68041d70> GST_RTSP_LOWER_TRANS_UDP, ipv4
0:00:11.361548112  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f68041d70> allocated address: 0.0.0.0 and ports: 39270, 39271
0:00:11.361562206  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:1880:gst_rtsp_stream_allocate_udp_sockets:<GstRTSPStream@0x7f68041d70> GST_RTSP_LOWER_TRANS_UDP, ipv6
0:00:11.361668129  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:1639:alloc_ports_one_family:<GstRTSPStream@0x7f68041d70> allocated address: :: and ports: 55390, 55391
0:00:11.364400752  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f78006630: received a request PLAY rtsp://***********5:8554/stream/ 1.0
0:00:11.364474415  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:5101:gst_rtsp_media_complete_pipeline:<GstRTSPMedia@0x7f6803ffb0> complete pipeline
0:00:11.364493092  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:5807:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f68041d70> complete stream
0:00:11.364504391  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3714:create_receiver_part:<GstRTSPStream@0x7f68041d70> create receiver part
0:00:11.364534246  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3732:create_receiver_part:<GstRTSPStream@0x7f68041d70> RTP caps: application/x-rtp RTCP caps: application/x-rtcp
0:00:11.364553658  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:11.364661553  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f6804d3e0> adding pad 'src'
0:00:11.364715072  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:00:11.364742080  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:11.364785225  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:00:11.364796161  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.364807424  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:00:11.364824557  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3778:create_receiver_part:<GstRTSPStream@0x7f68041d70> udp IPv4, create and configure udpsources
0:00:11.366682764  1563   0x7f74000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:00:11.366710928  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:11.366911037  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f68050060> adding pad 'src'
0:00:11.366987140  1563   0x7f74000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:00:11.367014343  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:00:11.367028792  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.367053015  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:11.367087537  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:11.367121349  1563   0x7f74000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:11.367149492  1563   0x7f74000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f68050460 on task 0x7f68050cd0
0:00:11.367162511  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f68050cd0
0:00:11.367402406  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:11.367417195  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:11.367439603  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:11.367455027  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:00:11.367468135  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.367501880  1563   0x7f74001410 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:00:11.367522062  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:00:11.367562256  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:00:11.367608155  1563   0x7f74001410 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:11.367634467  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:00:11.367649363  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.367640205  1563   0x7f74001410 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:11.367674756  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3802:create_receiver_part:<GstRTSPStream@0x7f68041d70> udp IPv6, create and configure udpsources
0:00:11.367699170  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:11.367728102  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f68051b50> adding pad 'src'
0:00:11.367794066  1563   0x7f74000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:00:11.367822801  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:00:11.367837643  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:11.367861084  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:11.367891357  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:11.367919290  1563   0x7f74000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:11.367926605  1563   0x7f74001410 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:11.367947669  1563   0x7f74000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f68051f50 on task 0x7f680523a0
0:00:11.367990214  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f680523a0
0:00:11.367989980  1563   0x7f74001410 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:00:11.368192712  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:11.368206862  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:11.368227872  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:00:11.368242527  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:00:11.368254273  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.368293830  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:00:11.368319130  1563   0x7f74001620 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:00:11.368359897  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:00:11.368406034  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:00:11.368416606  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.368444349  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:11.368451630  1563   0x7f74001620 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:11.368485450  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:11.368509595  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:00:11.368533294  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:11.368545472  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:11.368563969  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:00:11.368574996  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:00:11.368586710  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.368607349  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3551:create_sender_part:<GstRTSPStream@0x7f68041d70> create sender part
0:00:11.368622709  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3562:create_sender_part:<GstRTSPStream@0x7f68041d70> tcp: 0, udp: 1, mcast: 0 (ttl: 0)
0:00:11.368645824  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:11.368740437  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f68053150> adding pad 'sink'
0:00:11.368781550  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:11.368962174  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f68055820> adding pad 'sink'
0:00:11.369004042  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f68041d70> udp IPv4, configure udpsinks
0:00:11.369018548  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f68041d70> udp IPv6, configure udpsinks
0:00:11.369033701  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f68041d70> plug udp sink
0:00:11.369057256  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f68041d70> creating first stream
0:00:11.369093139  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:00:11.369108584  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:00:11.369130268  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:00:11.369148778  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:00:11.369171860  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:00:11.369182014  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.369195394  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:00:11.369232209  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:11.369245988  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:11.369269403  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:00:11.369286590  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:11.369315952  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:11.369317009  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6803ffb0: went from PAUSED to PAUSED (pending PAUSED)
0:00:11.369333222  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:00:11.369375077  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:00:11.369392955  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:11.369405791  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.369421712  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:00:11.369440143  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:00:11.369504184  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:00:11.369515605  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.369564324  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:11.369605994  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f68058280> adding pad 'sink'
0:00:11.369644573  1563   0x7f74000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:11.369670844  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f68058ab0> adding pad 'sink'
0:00:11.369710356  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:1343:create_and_configure_udpsink:<GstRTSPStream@0x7f68041d70> udp IPv4, configure udpsinks
0:00:11.369725687  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:1348:create_and_configure_udpsink:<GstRTSPStream@0x7f68041d70> udp IPv6, configure udpsinks
0:00:11.369740059  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3418:plug_udp_sink:<GstRTSPStream@0x7f68041d70> plug udp sink
0:00:11.369759382  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:3454:plug_udp_sink:<GstRTSPStream@0x7f68041d70> creating first stream
0:00:11.369784344  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:00:11.369797940  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:00:11.369815502  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:00:11.369831475  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:00:11.369851451  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:00:11.369861607  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.369871814  1563   0x7f74000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:00:11.369904799  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:11.369917722  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:11.369932813  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:00:11.369951857  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:11.369964569  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.369982854  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:11.369995103  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:11.370009249  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:00:11.370024739  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:11.370036658  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:11.370051034  1563   0x7f74000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:00:11.370069303  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:11.370101694  1563   0x7f74000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:00:11.370111961  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:11.370132559  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:5826:gst_rtsp_stream_complete_stream:<GstRTSPStream@0x7f68041d70> pipeline successfully updated
0:00:11.370146573  1563   0x7f74000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:49858-49859
0:00:11.370236364  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 4
0:00:11.370247952  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 3
0:00:11.370274932  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:6346:gst_rtsp_stream_set_rate_control:<GstRTSPStream@0x7f68041d70> Enabling rate control
0:00:11.370303268  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2885:gst_rtsp_media_seek_trickmode: flags=4  rate=1.000000
0:00:11.370335448  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:11.370376905  1563   0x7f74000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:11.370453518  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:854:check_seekable:<GstRTSPMedia@0x7f6803ffb0> seekable:0
0:00:11.370468397  1563   0x7f74000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f6803ffb0> Handle going back to 0 for none live not seekable streams.
0:00:11.370481751  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:11.370493572  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:11.370503111  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2842:gst_rtsp_media_get_status: got status 3
0:00:11.370529662  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:11.370624806  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f6803ffb0, target state PAUSED
0:00:11.370639447  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 1, deactivate 0
0:00:11.370650498  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6803ffb0 set blocked 0
0:00:11.370662188  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f68041d70> blocked: 0
0:00:11.370679214  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f6803ffb0 do_state 1
0:00:11.370691793  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f6803ffb0
0:00:11.370700791  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f6803ffb0
0:00:11.370712425  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2794:media_streams_set_blocked: media 0x7f6803ffb0 set blocked 0
0:00:11.370724055  1563   0x7f74000b70 DEBUG             rtspstream rtsp-stream.c:5433:set_blocked:<GstRTSPStream@0x7f68041d70> blocked: 0
0:00:11.370735141  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6803ffb0
0:00:11.370746737  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:11.370776479  1563   0x7f74000d80 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f6803ffb0: got message type 16 (tag)
0:00:11.370778739  1563   0x7f74001200 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:11.370810073  1563   0x7f74001200 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:11.370828159  1563   0x7f74001200 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:11.370858970  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6803ffb0: went from PAUSED to PAUSED (pending PLAYING)
0:00:11.370885966  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3377:default_handle_message:<GstRTSPMedia@0x7f6803ffb0> got async-done
0:00:11.371129580  1563   0x7f74001830 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:11.371159887  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:11.371300130  1563   0x7f74001830 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:11.371405855  1563   0x7f74001830 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:11.371461555  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.371483955  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:00:11.371501180  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.371537730  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:11.371559805  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.371580830  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:11.371598780  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:11.371621555  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.371640355  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:00:11.371657805  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.371684030  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:11.371708780  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:11.371727480  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:00:11.371744855  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.371771305  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:11.371799830  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.371837730  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.371858880  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:11.371879230  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:11.371906730  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.371927630  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:11.371947655  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:11.371973055  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.371994430  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:11.372014980  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:11.372037830  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:11.372060730  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:11.372080905  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372118530  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372134980  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:11.372151855  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:11.372172805  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372189030  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:11.372206080  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:11.372226755  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372243055  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:11.372259730  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:11.372280355  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372296605  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:11.372311380  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:11.372332355  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372348030  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:11.372364630  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:11.372385730  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372401430  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:11.372418130  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:11.372436930  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372452180  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:11.372468280  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:11.372486130  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:11.372504380  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:11.372527755  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:11.372543355  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:11.372560005  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:11.372579780  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:11.372599255  1563   0x7f74001830 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:11.372620455  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:11.372638005  1563   0x7f74001830 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:11.372729699  1563   0x7f74000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:11.372803030  1563   0x7f74000d80 DEBUG              rtspmedia rtsp-media.c:3243:default_handle_message: 0x7f6803ffb0: went from PAUSED to PLAYING (pending VOID_PENDING)
0:00:12.826154225  1563   0x7f64015d20 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:12.826242248  1563   0x7f64015d20 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:12.826421924  1563   0x7f74000d80 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:12.826426308  1563   0x7f64015d20 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2763916419, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)10370, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400367969130134997, sr-rtptime=(uint)1163404331, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:12.826495010  1563   0x7f74000d80 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:13.804937054  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f68041d70: new source 0x7f50013a40
0:00:13.805209429  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)318780883, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:49859, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:13.805279829  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:49859 in 1 transports
0:00:13.805303879  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f68041d70: found transport 0x7f6804c710 for source  0x7f50013a40
0:00:13.805327454  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f68041d70: source 0x7f50013a40 for transport 0x7f6804c710
0:00:13.805371229  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f68041d70: source 0x7f50013a40 in transport 0x7f6804c710 is active
0:00:13.805389104  1563   0x7f74001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f68042c00 alive
0:00:13.805494629  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f68041d70: new SDES 0x7f50013a40
0:00:17.845304819  1563   0x7f64015d20 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2763916419, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)10370, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400367990686876503, sr-rtptime=(uint)1163856047, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:18.755406953  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f68041d70: source 0x7f50013a40 in transport 0x7f6804c710 is active
0:00:18.755457678  1563   0x7f74001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f68042c00 alive
[2021-01-01 16:43:57.804] [INFO] === RTSP Server Statistics ===
[2021-01-01 16:43:57.804] [INFO] Uptime: 20.0 seconds
[2021-01-01 16:43:57.804] [INFO] Total connections: 1
[2021-01-01 16:43:57.804] [INFO] Active connections: 1
[2021-01-01 16:43:57.804] [INFO] Frames served: 5
[2021-01-01 16:43:57.804] [INFO] Clients connected: 0
[2021-01-01 16:43:57.804] [INFO] Error count: 0
0:00:22.104731078  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f68041d70: source 0x7f50013a40 in transport 0x7f6804c710 is active
0:00:22.104775478  1563   0x7f74001410 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f68042c00 alive
0:00:22.104788609  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f78006630: received a request TEARDOWN rtsp://***********5:8554/stream/ 1.0
0:00:22.104812353  1563   0x7f74001410 INFO              rtspstream rtsp-stream.c:2488:on_bye_ssrc: 0x7f68041d70: source 0x7f50013a40 bye
0:00:22.104839209  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state NULL media 0x7f6803ffb0, target state PLAYING
0:00:22.104855652  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:4917:gst_rtsp_media_set_state: 1 transports, activate 0, deactivate 1
0:00:22.104869105  1563   0x7f74000b70 INFO              rtspstream rtsp-stream.c:4774:update_transport: removing ***********:49858-49859
0:00:22.104908887  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 1 active 0 media 0x7f6803ffb0 do_state 1
0:00:22.104921261  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:4147:gst_rtsp_media_unprepare: unprepare media 0x7f6803ffb0
0:00:22.104933905  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to NULL for media 0x7f6803ffb0
0:00:22.104943705  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:2809:gst_rtsp_media_set_status: setting new status to 1
0:00:22.104952326  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:4107:default_unprepare: Temporarily go to PLAYING again for sending EOS
0:00:22.104961066  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f6803ffb0
0:00:22.105074245  1563   0x7f74000b70 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.020000000
0:00:22.105138255  1563   0x7f74000b70 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.020000000
0:00:22.105173544  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105185889  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink1> skipping transition from PLAYING to  PLAYING
0:00:22.105197905  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:22.105212516  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105224023  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:22.105237730  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:22.105251101  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105261795  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee1> skipping transition from PLAYING to  PLAYING
0:00:22.105273405  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:22.105286931  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105297516  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<tee0> skipping transition from PLAYING to  PLAYING
0:00:22.105310481  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:22.105325297  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105348707  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105359954  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:22.105371042  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:22.105385311  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105396113  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:22.105407931  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:22.105421786  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105434022  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:22.105447309  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:22.105460503  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:22.105473533  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:22.105485916  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105509299  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105520102  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:22.105531368  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:22.105544684  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<h264parse0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105555097  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<h264parse0> skipping transition from PLAYING to  PLAYING
0:00:22.105567044  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'h264parse0' changed state to 4(PLAYING) successfully
0:00:22.105580209  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105591287  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:22.105603402  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:22.105616396  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105627235  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:22.105639041  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:22.105652481  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<videoscale0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105664520  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<videoscale0> skipping transition from PLAYING to  PLAYING
0:00:22.105675400  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'videoscale0' changed state to 4(PLAYING) successfully
0:00:22.105688858  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<queue0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105699588  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<queue0> skipping transition from PLAYING to  PLAYING
0:00:22.105711030  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'queue0' changed state to 4(PLAYING) successfully
0:00:22.105723095  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<source> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105733394  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<source> skipping transition from PLAYING to  PLAYING
0:00:22.105745580  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'source' changed state to 4(PLAYING) successfully
0:00:22.105757073  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:22.105768974  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:22.105783167  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:22.105794564  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:22.105805766  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:22.105818978  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:22.105832197  1563   0x7f74000b70 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:22.105846128  1563   0x7f74000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:22.105856348  1563   0x7f74000b70 DEBUG              rtspmedia rtsp-media.c:4109:default_unprepare: sending EOS for shutdown
0:00:22.105924653  1563   0x7f74001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = flushing
0:00:22.105956428  1563   0x7f74001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:22.106003878  1563   0x7f74001620 INFO                    task gsttask.c:370:gst_task_func:<udpsrc1:src> Task resume from paused
0:00:22.106024153  1563   0x7f74001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = flushing
0:00:22.106046778  1563   0x7f74001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:22.106074203  1563   0x7f74001410 INFO                    task gsttask.c:370:gst_task_func:<udpsrc0:src> Task resume from paused
0:00:22.106086750  1563   0x7f74000b70 DEBUG                 appsrc gstappsrc.c:1028:gst_app_src_send_event:<source> queue event: eos event: 0x7f6805bae0, time 99:99:99.999999999, seq-num 181, (NULL)
0:00:22.106093403  1563   0x7f74001410 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc0> pausing after gst_base_src_get_range() = eos
0:00:22.106113419  1563   0x7f74000b70 INFO        rtspsessionmedia rtsp-session-media.c:104:gst_rtsp_session_media_finalize: free session media 0x7f68042320
0:00:22.106142578  1563   0x7f74001410 INFO                    task gsttask.c:368:gst_task_func:<udpsrc0:src> Task going to paused
0:00:22.106146971  1563   0x7f74000b70 WARN               rtspmedia rtsp-media.c:4975:gst_rtsp_media_set_state: media 0x7f6803ffb0 was not prepared
0:00:22.106180615  1563   0x7f74000b70 INFO               rtspmedia rtsp-media.c:4175:gst_rtsp_media_unprepare: media 0x7f6803ffb0 is already unpreparing
0:00:22.106203006  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:4922:do_send_messages: client 0x7f78006630: sending close message
0:00:22.106272228  1563   0x7f74001620 INFO                 basesrc gstbasesrc.c:2918:gst_base_src_loop:<udpsrc1> pausing after gst_base_src_get_range() = eos
0:00:22.106283472  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:3885:client_session_removed: client 0x7f78006630: session 0x7f68042c00 removed
0:00:22.106308493  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:693:client_unwatch_session: client 0x7f78006630: unwatch session 0x7f68042c00
0:00:22.106328019  1563   0x7f74000b70 INFO             rtspsession rtsp-session.c:176:gst_rtsp_session_finalize: finalize session 0x7f68042c00
0:00:22.106380478  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f78006630: connection closed
0:00:22.106396764  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f78006630: watch destroyed
0:00:22.106420045  1563   0x7f74000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x556f0cfe70> unmanage client 0x7f78006630
0:00:22.106442760  1563   0x7f74000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f78006cb0
0:00:22.106451828  1563   0x7f74001620 INFO                    task gsttask.c:368:gst_task_func:<udpsrc1:src> Task going to paused
0:00:22.106457986  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f78006630
0:00:22.106550373  1563   0x7f74000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f78006f10
0:00:22.112092978  1563   0x7f78000d70 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x7f78006630 connected to server ip ***********5, ipv6 = 0
0:00:22.112135328  1563   0x7f78000d70 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x7f78006630 ip ***********:50960
0:00:22.112155453  1563   0x7f78000d70 DEBUG             rtspserver rtsp-server.c:1104:manage_client:<GstRTSPServer@0x556f0cfe70> manage client 0x7f78006630
[2021-01-01 16:43:59.699] [INFO] === CLIENT CONNECTED ===
[2021-01-01 16:43:59.699] [INFO] Active connections: 2, Total connections: 2
0:00:22.112382091  1563   0x7f74000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f78007fc0
0:00:22.112400403  1563   0x7f78000d70 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x7f78006630: attaching to context 0x7f78008160
0:00:22.123523879  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f78006630: received a request OPTIONS rtsp://***********5:8554/stream 1.0
0:00:22.125910493  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x7f78006630: received a request DESCRIBE rtsp://***********5:8554/stream 1.0
0:00:22.125941976  1563   0x7f74000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556f0d2280 for path /stream
0:00:22.125960393  1563   0x7f74000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f6803ffb0 for url /stream
0:00:22.126000204  1563   0x7f74000b70 WARN               rtspmedia rtsp-media.c:3977:gst_rtsp_media_prepare: media 0x7f6803ffb0 was not unprepared
0:00:22.126014420  1563   0x7f74000b70 ERROR             rtspclient rtsp-client.c:1115:find_media: client 0x7f78006630: can't prepare media
0:00:22.126021578  1563   0x7f74001830 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f6805bc60
0:00:22.126064665  1563   0x7f74000b70 ERROR             rtspclient rtsp-client.c:3412:handle_describe_request: client 0x7f78006630: no media
0:00:22.126065578  1563   0x7f74001830 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f6805bc60
0:00:22.128604776  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:5012:closed: client 0x7f78006630: connection closed
0:00:22.128623086  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:5292:client_watch_notify: client 0x7f78006630: watch destroyed
0:00:22.128646967  1563   0x7f74000b70 DEBUG             rtspserver rtsp-server.c:1075:unmanage_client:<GstRTSPServer@0x556f0cfe70> unmanage client 0x7f78006630
0:00:22.128670028  1563   0x7f74000b70 DEBUG             rtspserver rtsp-server.c:1055:free_client_context: free context 0x7f78007550
0:00:22.128686287  1563   0x7f74000b70 INFO              rtspclient rtsp-client.c:763:gst_rtsp_client_finalize: finalize client 0x7f78006630
0:00:22.128761709  1563   0x7f74000b70 INFO          rtspthreadpool rtsp-thread-pool.c:331:do_loop: exit mainloop of thread 0x7f78007fc0
0:00:23.130610574  1563   0x7f64015d20 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2763916419, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)10370, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400367990686876503, sr-rtptime=(uint)1163856047, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:26.011572325  1563   0x7f64015d20 INFO              rtspstream rtsp-stream.c:2496:on_bye_timeout: 0x7f68041d70: source 0x7f50013a40 bye timeout
0:00:29.072687499  1563   0x7f64015d20 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2763916419, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)10370, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400367990686876503, sr-rtptime=(uint)1163856047, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 16:44:07.805] [INFO] === RTSP Server Statistics ===
[2021-01-01 16:44:07.805] [INFO] Uptime: 30.0 seconds
[2021-01-01 16:44:07.805] [INFO] Total connections: 2
[2021-01-01 16:44:07.805] [INFO] Active connections: 2
[2021-01-01 16:44:07.805] [INFO] Frames served: 5
[2021-01-01 16:44:07.805] [INFO] Clients connected: 0
[2021-01-01 16:44:07.805] [INFO] Error count: 0
0:00:35.100204159  1563   0x7f64015d20 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2763916419, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)10370, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400367990686876503, sr-rtptime=(uint)1163856047, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
[2021-01-01 16:44:17.805] [INFO] === RTSP Server Statistics ===
[2021-01-01 16:44:17.805] [INFO] Uptime: 40.0 seconds
[2021-01-01 16:44:17.805] [INFO] Total connections: 2
[2021-01-01 16:44:17.805] [INFO] Active connections: 2
[2021-01-01 16:44:17.805] [INFO] Frames served: 5
[2021-01-01 16:44:17.805] [INFO] Clients connected: 0
[2021-01-01 16:44:17.805] [INFO] Error count: 0
0:00:40.449084945  1563   0x7f64015d20 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2763916419, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)10370, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400367990686876503, sr-rtptime=(uint)1163856047, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
0:00:46.535820319  1563   0x7f64015d20 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)2763916419, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)10370, clock-rate=(int)90000, octets-sent=(guint64)25, packets-sent=(guint64)1, octets-received=(guint64)25, packets-received=(guint64)1, bytes-received=(guint64)65, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400367990686876503, sr-rtptime=(uint)1163856047, sr-octet-count=(uint)25, sr-packet-count=(uint)1;
^C[2021-01-01 16:44:24.787] [INFO] Received signal 2, shutting down...
[2021-01-01 16:44:24.787] [INFO] RTSP server main loop stopped
[2021-01-01 16:44:24.789] [INFO] RTSP server stopped
[2021-01-01 16:44:24.809] [INFO] RTSP server shutdown complete