root@rk3576-buildroot:/# rtsp_server_pipe --gst-debug=4 -p 8556 -u /stream -i  "( v4l2src device=/dev/video0 ! image/jpeg,format=MJPG,width=1280,height=720,framerate=30/1 ! mppjpegdec ! mpph264enc profile=baseline ! rtph264pay name=pay0 pt=96 )"
0:00:00.000441645  1427   0x556bb7cdc0 INFO                GST_INIT gstmessage.c:129:_priv_gst_message_initialize: init messages
0:00:00.000976379  1427   0x556bb7cdc0 INFO                GST_INIT gstcontext.c:86:_priv_gst_context_initialize: init contexts
0:00:00.001211673  1427   0x556bb7cdc0 INFO      GST_PLUGIN_LOADING gstplugin.c:324:_priv_gst_plugin_initialize: registering 0 static plugins
0:00:00.001306012  1427   0x556bb7cdc0 INFO      GST_PLUGIN_LOADING gstplugin.c:232:gst_plugin_register_static: registered static plugin "staticelements"
0:00:00.001321679  1427   0x556bb7cdc0 INFO      GST_PLUGIN_LOADING gstplugin.c:234:gst_plugin_register_static: added static plugin "staticelements", result: 1
0:00:00.001374200  1427   0x556bb7cdc0 INFO            GST_REGISTRY gstregistry.c:1836:ensure_current_registry: reading registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007175840  1427   0x556bb7cdc0 INFO            GST_REGISTRY gstregistrybinary.c:683:priv_gst_registry_binary_read_cache: loaded /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin in 0.005767 seconds
0:00:00.007249788  1427   0x556bb7cdc0 INFO            GST_REGISTRY gstregistry.c:1703:scan_and_update_registry: Validating plugins from registry cache: /root/.cache/gstreamer-1.0/registry.cortex-a72.cortex-a53.bin
0:00:00.007817185  1427   0x556bb7cdc0 INFO            GST_REGISTRY gstregistry.c:1795:scan_and_update_registry: Registry cache has not changed
0:00:00.007833018  1427   0x556bb7cdc0 INFO            GST_REGISTRY gstregistry.c:1871:ensure_current_registry: registry reading and updating done
0:00:00.007846015  1427   0x556bb7cdc0 INFO                GST_INIT gst.c:805:init_post: GLib runtime version: 2.76.1
0:00:00.007855154  1427   0x556bb7cdc0 INFO                GST_INIT gst.c:807:init_post: GLib headers version: 2.76.1
0:00:00.007862304  1427   0x556bb7cdc0 INFO                GST_INIT gst.c:809:init_post: initialized GStreamer successfully
0:00:00.008117702  1427   0x556bb7cdc0 INFO         rtspmountpoints rtsp-mount-points.c:360:gst_rtsp_mount_points_add_factory: adding media factory 0x556bc61fd0 for path /stream
stream ready at rtsp://0.0.0.0:8556/stream
0:00:16.679836669  1427   0x556bb7cdc0 INFO              rtspclient rtsp-client.c:4693:gst_rtsp_client_set_connection: client 0x556bc6c9a0 connected to server ip ************, ipv6 = 0
0:00:16.679863772  1427   0x556bb7cdc0 INFO              rtspclient rtsp-client.c:4697:gst_rtsp_client_set_connection: added new client 0x556bc6c9a0 ip ***********:61232
0:00:16.680134397  1427   0x556bb7cdc0 INFO              rtspclient rtsp-client.c:5349:gst_rtsp_client_attach: client 0x556bc6c9a0: attaching to context 0x556bc6d170
0:00:16.680187752  1427   0x7fa0000b70 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x556bc6cf50
0:00:16.680514727  1427   0x7fa0000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x556bc6c9a0: received a request OPTIONS rtsp://************:8556/stream 1.0
0:00:16.684205777  1427   0x7fa0000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x556bc6c9a0: received a request DESCRIBE rtsp://************:8556/stream 1.0
0:00:16.684251527  1427   0x7fa0000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556bc61fd0 for path /stream
0:00:16.684275477  1427   0x7fa0000b70 INFO            GST_PIPELINE gstparse.c:344:gst_parse_launch_full: parsing pipeline description '( v4l2src device=/dev/video0 ! image/jpeg,format=MJPG,width=1280,height=720,framerate=30/1 ! mppjpegdec ! mpph264enc profile=baseline ! rtph264pay name=pay0 pt=96 )'
0:00:16.687314527  1427   0x7fa0000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstvideo4linux2.so" loaded
0:00:16.690485902  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "v4l2src"
0:00:16.690545052  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f9800da50> adding pad 'src'
0:00:16.696924852  1427   0x7fa0000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrockchipmpp.so" loaded
0:00:16.697268852  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mppjpegdec"
0:00:16.697317002  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f98011060> adding pad 'sink'
0:00:16.697347552  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoDecoder@0x7f98011060> adding pad 'src'
0:00:16.697680527  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "mpph264enc"
0:00:16.697732927  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f98028200> adding pad 'sink'
0:00:16.697761827  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstVideoEncoder@0x7f98028200> adding pad 'src'
0:00:16.699888577  1427   0x7fa0000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtp.so" loaded
0:00:16.700231277  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtph264pay"
0:00:16.700308927  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f98034ae0> adding pad 'src'
0:00:16.700340952  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRTPBasePayload@0x7f98034ae0> adding pad 'sink'
0:00:16.700392502  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "bin"
0:00:16.700537627  1427   0x7fa0000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstV4l2Src named v4l2src0 to some pad of GstMppJpegDec named mppjpegdec0 (0/0) with caps "image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1"
0:00:16.702030977  1427   0x7fa0000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstcoreelements.so" loaded
0:00:16.702063727  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "capsfilter"
0:00:16.702275827  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f9803ac70> adding pad 'sink'
0:00:16.702310352  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseTransform@0x7f9803ac70> adding pad 'src'
0:00:16.702337452  1427   0x7fa0000b70 INFO              GST_STATES gstbin.c:2070:gst_bin_get_state_func:<bin0> getting state
0:00:16.702404452  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to NULL
0:00:16.702429377  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.702455152  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element v4l2src0:(any) to element capsfilter0:sink
0:00:16.702473052  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:sink
0:00:16.702489877  1427   0x7fa0000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: v4l2src0 and capsfilter0 in same bin, no need for ghost pads
0:00:16.702532202  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link v4l2src0:src and capsfilter0:sink
0:00:16.702563977  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<capsfilter0:src> pad has no peer
0:00:16.702597877  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked v4l2src0:src and capsfilter0:sink, successful
0:00:16.702612152  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.702626127  1427   0x7fa0000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<v4l2src0:src> Received event on flushing pad. Discarding
0:00:16.702654577  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element capsfilter0:src to element mppjpegdec0:(any)
0:00:16.702672027  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad capsfilter0:src
0:00:16.702704227  1427   0x7fa0000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link capsfilter0:src and mppjpegdec0:sink
0:00:16.702789152  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec0:src> pad has no peer
0:00:16.702855652  1427   0x7fa0000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: capsfilter0 and mppjpegdec0 in same bin, no need for ghost pads
0:00:16.702895702  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link capsfilter0:src and mppjpegdec0:sink
0:00:16.702938752  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mppjpegdec0:src> pad has no peer
0:00:16.702986002  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked capsfilter0:src and mppjpegdec0:sink, successful
0:00:16.703003502  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.703018002  1427   0x7fa0000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<capsfilter0:src> Received event on flushing pad. Discarding
0:00:16.703068452  1427   0x7fa0000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppJpegDec named mppjpegdec0 to some pad of GstMppH264Enc named mpph264enc0 (0/0) with caps "(NULL)"
0:00:16.703090052  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mppjpegdec0:(any) to element mpph264enc0:(any)
0:00:16.703115227  1427   0x7fa0000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mppjpegdec0:src and mpph264enc0:sink
0:00:16.703139552  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:16.703189327  1427   0x7fa0000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mppjpegdec0 and mpph264enc0 in same bin, no need for ghost pads
0:00:16.703213477  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mppjpegdec0:src and mpph264enc0:sink
0:00:16.703234252  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<mpph264enc0:src> pad has no peer
0:00:16.703277002  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mppjpegdec0:src and mpph264enc0:sink, successful
0:00:16.703290752  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.703303952  1427   0x7fa0000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mppjpegdec0:src> Received event on flushing pad. Discarding
0:00:16.703337277  1427   0x7fa0000b70 INFO            GST_PIPELINE gst/parse/grammar.y:998:gst_parse_perform_link: linking some pad of GstMppH264Enc named mpph264enc0 to some pad of GstRtpH264Pay named pay0 (0/0) with caps "(NULL)"
0:00:16.703357852  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstutils.c:1818:gst_element_link_pads_full: trying to link element mpph264enc0:(any) to element pay0:(any)
0:00:16.703379677  1427   0x7fa0000b70 INFO                GST_PADS gstutils.c:1080:gst_pad_check_link: trying to link mpph264enc0:src and pay0:sink
0:00:16.703403027  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:16.703430477  1427   0x7fa0000b70 INFO                GST_PADS gstutils.c:1634:prepare_link_maybe_ghosting: mpph264enc0 and pay0 in same bin, no need for ghost pads
0:00:16.703452752  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link mpph264enc0:src and pay0:sink
0:00:16.703473152  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<pay0:src> pad has no peer
0:00:16.703498502  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked mpph264enc0:src and pay0:sink, successful
0:00:16.703511802  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.703524677  1427   0x7fa0000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<mpph264enc0:src> Received event on flushing pad. Discarding
0:00:16.703758952  1427   0x7fa0000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay0
0:00:16.703788152  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:2210:gst_rtsp_media_collect_streams: found stream 0 with payloader 0x7f98034ae0
0:00:16.703806052  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad pay0:src
0:00:16.703904977  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link pay0:src and src_0:proxypad0
0:00:16.703925777  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked pay0:src and src_0:proxypad0, successful
0:00:16.703940252  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.703955827  1427   0x7fa0000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:16.703984802  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<bin0> adding pad 'src_0'
0:00:16.704070077  1427   0x7fa0000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay0
0:00:16.704123727  1427   0x7fa0000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay0
0:00:16.704151727  1427   0x7fa0000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element pay1
0:00:16.704175827  1427   0x7fa0000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element dynpay1
0:00:16.704199127  1427   0x7fa0000b70 INFO           GST_PARENTAGE gstbin.c:4385:gst_bin_get_by_name: [bin0]: looking up child element depay1
0:00:16.704230752  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "pipeline"
0:00:16.704393052  1427   0x7fa0000b70 INFO        rtspmediafactory rtsp-media-factory.c:1452:gst_rtsp_media_factory_construct: constructed media 0x7f9803e260 for url /stream
0:00:16.704485477  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:3923:gst_rtsp_media_prepare: preparing media 0x7f9803e260
0:00:16.704551527  1427   0x7fa0000d30 INFO          rtspthreadpool rtsp-thread-pool.c:329:do_loop: enter mainloop of thread 0x7f9803fd70
0:00:16.706082202  1427   0x7fa0000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstrtpmanager.so" loaded
0:00:16.706113077  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpbin"
0:00:16.706915902  1427   0x7fa0000d30 INFO              rtspstream rtsp-stream.c:3970:gst_rtsp_stream_join_bin: stream 0x7f9803f840 joining bin as session 0
0:00:16.706962902  1427   0x7fa0000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpsession"
0:00:16.707578302  1427   0x7fa0000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpssrcdemux"
0:00:16.707727227  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f9c00acd0> adding pad 'sink'
0:00:16.707758527  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpSsrcDemux@0x7f9c00acd0> adding pad 'rtcp_sink'
0:00:16.707782227  1427   0x7fa0000d30 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "rtpstorage"
0:00:16.707894377  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f9c00c2b0> adding pad 'src'
0:00:16.707912927  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstRtpStorage@0x7f9c00c2b0> adding pad 'sink'
0:00:16.708082802  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to NULL
0:00:16.708102977  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to NULL
0:00:16.708119402  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to NULL
0:00:16.708170227  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_sink'
0:00:16.708213027  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtp_src'
0:00:16.708232802  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:send_rtp_src
0:00:16.708301952  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1
0:00:16.708328152  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtp_src and send_rtp_src_0:proxypad1, successful
0:00:16.708343177  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.708383552  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_src_0'
0:00:16.708431727  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink
0:00:16.708451527  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked send_rtp_sink_0:proxypad2 and rtpsession0:send_rtp_sink, successful
0:00:16.708464577  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.708486727  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtp_sink_0'
0:00:16.708514952  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link bin0:src_0 and rtpbin0:send_rtp_sink_0
0:00:16.708580202  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked bin0:src_0 and rtpbin0:send_rtp_sink_0, successful
0:00:16.708595052  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.708615327  1427   0x7fa0000d30 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<pay0:src> Received event on flushing pad. Discarding
0:00:16.708643402  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpbin0:send_rtp_src_0
0:00:16.708702702  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'send_rtcp_src'
0:00:16.708759702  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3
0:00:16.708780177  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:send_rtcp_src and send_rtcp_src_0:proxypad3, successful
0:00:16.708793052  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.708818102  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'send_rtcp_src_0'
0:00:16.708862552  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'recv_rtcp_sink'
0:00:16.708900302  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpsession0> adding pad 'sync_src'
0:00:16.708924752  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpsession0:sync_src
0:00:16.708941577  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad rtpssrcdemux0:rtcp_sink
0:00:16.708962827  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink
0:00:16.708984977  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpsession0:sync_src and rtpssrcdemux0:rtcp_sink, successful
0:00:16.708997927  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.709044527  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink
0:00:16.709064527  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked recv_rtcp_sink_0:proxypad4 and rtpsession0:recv_rtcp_sink, successful
0:00:16.709077377  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:16.709098552  1427   0x7fa0000d30 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<rtpbin0> adding pad 'recv_rtcp_sink_0'
0:00:16.709170477  1427   0x7fa0000d30 INFO               rtspmedia rtsp-media.c:3580:start_preroll: setting pipeline to PAUSED for media 0x7f9803e260
0:00:16.709194727  1427   0x7fa0000d30 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PAUSED for media 0x7f9803e260
0:00:16.709212677  1427   0x7fa0000d30 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PAUSED for media 0x7f9803e260
0:00:16.709249427  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709289727  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709307502  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to READY
0:00:16.709324702  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.709364152  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 2(READY) successfully
0:00:16.709386427  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709403727  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to READY
0:00:16.709420277  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.709442877  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 2(READY) successfully
0:00:16.709463627  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709480902  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to READY
0:00:16.709497052  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.709519027  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 2(READY) successfully
0:00:16.709538252  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to READY
0:00:16.709554577  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.709576477  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 2(READY) successfully
0:00:16.709595752  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709636127  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709654452  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to READY
0:00:16.709671002  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.709693727  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 2(READY) successfully
0:00:16.709737727  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709757227  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to READY
0:00:16.709775077  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.709798602  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 2(READY) successfully
0:00:16.709824952  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709843177  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to READY
0:00:16.709859527  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.709888652  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 2(READY) successfully
0:00:16.709914902  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current NULL pending VOID_PENDING, desired next READY
0:00:16.709931702  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to READY
0:00:16.709947852  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.709971627  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 2(READY) successfully
0:00:16.709991527  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current NULL pending VOID_PENDING, desired next READY
0:00:16.895094277  1427   0x7fa0000d30 INFO                    v4l2 v4l2_calls.c:592:gst_v4l2_open:<v4l2src0:src> Opened device 'FF Camera: FF Camera' (/dev/video0) successfully
0:00:16.895154052  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to READY
0:00:16.895174777  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.895218452  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 2(READY) successfully
0:00:16.895244527  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to READY
0:00:16.895261977  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:16.895285952  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 2(READY) successfully
0:00:16.895308677  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<media-pipeline> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:16.895325677  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed NULL to READY (PAUSED pending)
0:00:16.895345502  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<media-pipeline> continue state change READY to PAUSED, final PAUSED
0:00:16.895384702  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.895432427  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.895457752  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PAUSED
0:00:16.895475027  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.895501102  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 3(PAUSED) successfully
0:00:16.895523427  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.895549527  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PAUSED
0:00:16.895566827  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.895589252  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 3(PAUSED) successfully
0:00:16.895610127  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.895633877  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PAUSED
0:00:16.895650677  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.895677777  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 3(PAUSED) successfully
0:00:16.895701102  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PAUSED
0:00:16.895718177  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.895740277  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 3(PAUSED) successfully
0:00:16.895759502  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.895797702  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.895826902  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PAUSED
0:00:16.895844252  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.895866652  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 3(PAUSED) successfully
0:00:16.895887552  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.896434002  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PAUSED
0:00:16.896464827  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.896501677  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 3(PAUSED) successfully
0:00:16.896532102  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.896686502  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to PAUSED
0:00:16.896706602  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.896734402  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 3(PAUSED) successfully
0:00:16.896759302  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.896786952  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PAUSED
0:00:16.896804152  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.896829602  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 3(PAUSED) successfully
0:00:16.896850952  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current READY pending VOID_PENDING, desired next PAUSED
0:00:16.896899502  1427   0x7fa0000d30 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<v4l2src0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:16.896960752  1427   0x7fa0000d30 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f980101d0 on task 0x7f9c0259f0
0:00:16.896981927  1427   0x7fa0000d30 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<v4l2src0:src> created task 0x7f9c0259f0
0:00:16.897101052  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to PAUSED
0:00:16.897144202  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.897171552  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 3(PAUSED) successfully without preroll
0:00:16.897178693  1427   0x7fa0000d90 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "v4l2src0"
0:00:16.897209327  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PAUSED
0:00:16.897245152  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.897270552  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2979:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 3(PAUSED) successfully without preroll
0:00:16.897277890  1427   0x7fa0000d90 INFO                 v4l2src gstv4l2src.c:722:gst_v4l2src_query_preferred_size:<v4l2src0> Detect input 0 as `Input 1`
0:00:16.897295177  1427   0x7fa0000d30 INFO                pipeline gstpipeline.c:533:gst_pipeline_change_state:<media-pipeline> pipeline is live
0:00:16.897328252  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PAUSED
0:00:16.897335615  1427   0x7fa0000d90 INFO                    v4l2 gstv4l2object.c:1307:gst_v4l2_object_fill_format_list:<v4l2src0:src> got 2 format(s):
0:00:16.897348452  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:16.897364762  1427   0x7fa0000d90 INFO                    v4l2 gstv4l2object.c:1311:gst_v4l2_object_fill_format_list:<v4l2src0:src>   YUYV
0:00:16.897392027  1427   0x7fa0000d30 INFO               rtspmedia rtsp-media.c:3595:start_preroll: NO_PREROLL state change: live media 0x7f9803e260
0:00:16.897404844  1427   0x7fa0000d90 INFO                    v4l2 gstv4l2object.c:1311:gst_v4l2_object_fill_format_list:<v4l2src0:src>   MJPG
0:00:16.897428777  1427   0x7fa0000d30 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f9803e260
0:00:16.897517827  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:16.897545627  1427   0x7fa0000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:16.897579927  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:16.897610927  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:16.897613953  1427   0x7fa0000d90 INFO                    v4l2 gstv4l2object.c:4863:gst_v4l2_object_probe_caps:<v4l2src0:src> probed caps: video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 10/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 10/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)640, height=(int)480, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; video/x-raw, format=(string)YUY2, width=(int)320, height=(int)240, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1, 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1, 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)640, height=(int)480, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }; image/jpeg, parsed=(boolean)true, width=(int)320, height=(int)240, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction){ 30/1, 25/1, 20/1, 15/1, 10/1 }
0:00:16.897630652  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpstorage0> completed state change to PLAYING
0:00:16.897694877  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpstorage0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.897732202  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:16.897754152  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:16.897771377  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpssrcdemux0> completed state change to PLAYING
0:00:16.897790602  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpssrcdemux0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.897833427  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:16.897855427  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:16.897959577  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpsession0> completed state change to PLAYING
0:00:16.897978377  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpsession0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898001777  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:16.898020377  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:16.898036702  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<rtpbin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898059252  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:16.898094877  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:16.898112702  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<pay0> completed state change to PLAYING
0:00:16.898128652  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<pay0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898151102  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:16.898172227  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:16.898189377  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mpph264enc0> completed state change to PLAYING
0:00:16.898205252  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mpph264enc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898226952  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:16.898247327  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:16.898264677  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<mppjpegdec0> completed state change to PLAYING
0:00:16.898280702  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<mppjpegdec0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898305502  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 4(PLAYING) successfully
0:00:16.898326002  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:16.898342427  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<capsfilter0> completed state change to PLAYING
0:00:16.898358627  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<capsfilter0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898397927  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:16.898422402  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<v4l2src0> completed state change to PLAYING
0:00:16.898441002  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<v4l2src0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898466877  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 4(PLAYING) successfully
0:00:16.898485477  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:16.898501327  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<bin0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898524002  1427   0x7fa0000d30 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:16.898542452  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:16.898558602  1427   0x7fa0000d30 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:16.898868977  1427   0x7fa0000d30 INFO               rtspmedia rtsp-media.c:3391:default_handle_message: 0x7f9803e260: got message type 2048 (new-clock)
0:00:16.900644700  1427   0x7fa0000d90 INFO                 v4l2src gstv4l2src.c:868:gst_v4l2src_negotiate:<v4l2src0> fixated to: image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1, parsed=(boolean)true, pixel-aspect-ratio=(fraction)1/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:7:1
0:00:16.900674544  1427   0x7fa0000d90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1, parsed=(boolean)true, pixel-aspect-ratio=(fraction)1/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:7:1
0:00:16.900872062  1427   0x7fa0000d90 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:16.900896326  1427   0x7fa0000d90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1, parsed=(boolean)true, pixel-aspect-ratio=(fraction)1/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:7:1
0:00:16.901745925  1427   0x7fa0000d90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-raw, format=(string)NV12, width=(int)1280, height=(int)720, interlace-mode=(string)progressive, multiview-mode=(string)mono, multiview-flags=(GstVideoMultiviewFlagsSet)0:ffffffff:/right-view-first/left-flipped/left-flopped/right-flipped/right-flopped/half-aspect/mixed-mono, pixel-aspect-ratio=(fraction)1/1, colorimetry=(string)2:4:7:1, framerate=(fraction)30/1
0:00:16.901928148  1427   0x7fa0000d90 INFO                  mppenc gstmppenc.c:687:gst_mpp_enc_set_format:<mpph264enc0> applying NV12 1280x720 (1280x720)
0:00:16.903496223  1427   0x7fa0000d90 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event video/x-h264, stream-format=(string)byte-stream, alignment=(string)au, profile=(string)Baseline, level=(string)4, width=(int)1280, height=(int)720, pixel-aspect-ratio=(fraction)1/1, framerate=(fraction)30/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:7:1, multiview-mode=(string)mono, multiview-flags=(GstVideoMultiviewFlagsSet)0:ffffffff:/right-view-first/left-flipped/left-flopped/right-flipped/right-flopped/half-aspect/mixed-mono
0:00:16.903814562  1427   0x7fa0000d90 WARN                    v4l2 gstv4l2object.c:4518:gst_v4l2_object_set_crop:<v4l2src0:src> VIDIOC_S_CROP failed
0:00:16.906512844  1427   0x7fa0000d90 INFO                    v4l2 gstv4l2object.c:4112:gst_v4l2_object_set_format_full:<v4l2src0:src> Set capture framerate to 30/1
0:00:16.906551660  1427   0x7fa0000d90 WARN                    v4l2 gstv4l2object.c:3343:gst_v4l2_object_reset_compose_region:<v4l2src0:src> Failed to get default compose rectangle with VIDIOC_G_SELECTION: Invalid argument
0:00:16.906568407  1427   0x7fa0000d90 INFO                    v4l2 gstv4l2object.c:3279:gst_v4l2_object_setup_pool:<v4l2src0:src> accessing buffers via mode 4
0:00:16.906693681  1427   0x7fa0000d90 INFO          v4l2bufferpool gstv4l2bufferpool.c:586:gst_v4l2_buffer_pool_set_config:<v4l2src0:pool0:src> increasing minimum buffers to 2
0:00:16.906707825  1427   0x7fa0000d90 INFO          v4l2bufferpool gstv4l2bufferpool.c:599:gst_v4l2_buffer_pool_set_config:<v4l2src0:pool0:src> reducing maximum buffers to 64
0:00:16.906754275  1427   0x7fa0000d90 INFO          v4l2bufferpool gstv4l2bufferpool.c:599:gst_v4l2_buffer_pool_set_config:<v4l2src0:pool0:src> reducing maximum buffers to 64
0:00:16.908187178  1427   0x7fa0000d90 WARN          v4l2bufferpool gstv4l2bufferpool.c:850:gst_v4l2_buffer_pool_start:<v4l2src0:pool0:src> Uncertain or not enough buffers, enabling copy threshold
0:00:17.157824672  1427   0x7fa0000d90 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:17.157875266  1427   0x7fa0000d90 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<v4l2src0> marking pending DISCONT
0:00:17.157944399  1427   0x7fa0000d90 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f9801e200 on task 0x7f900201e0
0:00:17.157958742  1427   0x7fa0000d90 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mppjpegdec0:src> created task 0x7f900201e0
0:00:17.159949952  1427   0x7fa0000f70 WARN                  mppdec gstmppdec.c:630:gst_mpp_dec_get_frame:<mppjpegdec0> MPP is not able to generate pts
0:00:17.160003502  1427   0x7fa0000f70 INFO                  mppdec gstmppdec.c:518:gst_mpp_dec_apply_info_change:<mppjpegdec0> applying NV12 1280x720 (1280x720)
0:00:17.160525577  1427   0x7fa0000f70 INFO            videodecoder gstvideodecoder.c:3735:gst_video_decoder_clip_and_push_buf:<mppjpegdec0> First buffer since flush took 0:00:00.263957400 to produce
0:00:17.160597127  1427   0x7fa0000f70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f98028980 on task 0x7f880030d0
0:00:17.160617702  1427   0x7fa0000f70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<mpph264enc0:src> created task 0x7f880030d0
0:00:17.164459252  1427   0x7fa0001150 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:17.164682852  1427   0x7fa0001150 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, payload=(int)96, clock-rate=(int)90000, encoding-name=(string)H264, ssrc=(uint)1286577879, timestamp-offset=(uint)1354739697, seqnum-offset=(uint)12544, a-framerate=(string)30
0:00:17.164749802  1427   0x7fa0001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:17.164794402  1427   0x7fa0001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:17.164831502  1427   0x7fa0001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:17.164995452  1427   0x7fa0001150 INFO              rtspstream rtsp-stream.c:2520:on_new_sender_ssrc: 0x7f9803f840: new sender source 0x7f80002af0
0:00:17.165092552  1427   0x7fa0001150 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1286577879, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)12544, clock-rate=(int)90000, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0;
0:00:17.165155802  1427   0x7fa0001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:17.165203027  1427   0x7fa0001150 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f9803f840 received caps 0x7f80001bc0, application/x-rtp, media=(string)video, payload=(int)96, clock-rate=(int)90000, encoding-name=(string)H264, ssrc=(uint)1286577879, timestamp-offset=(uint)1354739697, seqnum-offset=(uint)12544, a-framerate=(string)30
0:00:17.167193352  1427   0x7fa0001150 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", payload=(int)96, seqnum-offset=(uint)12544, timestamp-offset=(uint)1354739697, ssrc=(uint)1286577879, a-framerate=(string)30
0:00:17.167252127  1427   0x7fa0001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:17.167297102  1427   0x7fa0001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:17.167333127  1427   0x7fa0001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:17.167371852  1427   0x7fa0001150 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<rtpbin0:send_rtp_src_0> pad has no peer
0:00:17.167418152  1427   0x7fa0001150 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f9803f840 received caps 0x7f800057c0, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", payload=(int)96, seqnum-offset=(uint)12544, timestamp-offset=(uint)1354739697, ssrc=(uint)1286577879, a-framerate=(string)30
0:00:17.167567052  1427   0x7fa0001150 WARN              rtpsession gstrtpsession.c:2441:gst_rtp_session_chain_send_rtp_common:<rtpsession0> Can't determine running time for this packet without knowing configured latency
0:00:17.167707352  1427   0x7fa0000d30 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:17.167758977  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:3950:gst_rtsp_media_prepare: object 0x7f9803e260 is prerolled
0:00:17.167819527  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:17.167886677  1427   0x7fa0000b70 INFO                 default gstmikey.c:2358:gst_mikey_message_new_from_caps: No srtp key
0:00:17.168083877  1427   0x7fa0000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:17.168116777  1427   0x7fa0000b70 INFO              rtspclient rtsp-client.c:3366:handle_describe_request: adding content-base: rtsp://************:8556/stream/
0:00:17.184058727  1427   0x7fa0000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x556bc6c9a0: received a request SETUP rtsp://************:8556/stream/stream=0 1.0
0:00:17.184102577  1427   0x7fa0000b70 INFO         rtspmountpoints rtsp-mount-points.c:305:gst_rtsp_mount_points_match: found media factory 0x556bc61fd0 for path /stream/stream=0
0:00:17.184121927  1427   0x7fa0000b70 INFO              rtspclient rtsp-client.c:1057:find_media: reusing cached media 0x7f9803e260 for path /stream
0:00:17.184137027  1427   0x7fa0000b70 FIXME              rtspmedia rtsp-media.c:4624:gst_rtsp_media_suspend: suspend for dynamic pipelines needs fixing
0:00:17.184152477  1427   0x7fa0000b70 WARN               rtspmedia rtsp-media.c:4663:gst_rtsp_media_suspend: media 0x7f9803e260 was not prepared
0:00:17.184298327  1427   0x7fa0000b70 INFO             rtspsession rtsp-session.c:157:gst_rtsp_session_init: init session 0x7f9804ae40
0:00:17.184323352  1427   0x7fa0000b70 INFO              rtspclient rtsp-client.c:669:client_watch_session: watching session 0x7f9804ae40
0:00:17.184394327  1427   0x7fa0000b70 INFO              rtspclient rtsp-client.c:2370:parse_transport: found valid transport RTP/AVP;unicast;client_port=65154-65155
0:00:17.184449852  1427   0x7fa0000b70 INFO             rtspsession rtsp-session.c:281:gst_rtsp_session_manage_media: manage new media 0x7f9803e260 in session 0x7f980499a0
0:00:17.186760177  1427   0x7fa0000b70 INFO              rtspclient rtsp-client.c:4016:handle_request: client 0x556bc6c9a0: received a request PLAY rtsp://************:8556/stream/ 1.0
0:00:17.186840002  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "funnel"
0:00:17.187016302  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstFunnel@0x7f9804c570> adding pad 'src'
0:00:17.187101627  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad funnel0:src
0:00:17.187135302  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link funnel0:src and rtpbin0:recv_rtcp_sink_0
0:00:17.187254727  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked funnel0:src and rtpbin0:recv_rtcp_sink_0, successful
0:00:17.187270577  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:17.187286477  1427   0x7fa0000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<funnel0:src> Received event on flushing pad. Discarding
0:00:17.188283202  1427   0x7fa0000b70 INFO      GST_PLUGIN_LOADING gstplugin.c:987:_priv_gst_plugin_load_file_for_registry: plugin "/lib/gstreamer-1.0/libgstudp.so" loaded
0:00:17.188316752  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:17.188713252  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f9804f0b0> adding pad 'src'
0:00:17.188838752  1427   0x7fa0000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc0> have udp buffer of 106496 bytes
0:00:17.188900702  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to READY
0:00:17.188920202  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:17.188975102  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:17.189028152  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc0:src
0:00:17.189091127  1427   0x7fa0000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc0> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:17.189169602  1427   0x7fa0000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f9804f4e0 on task 0x7f9804ffc0
0:00:17.189189477  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc0:src> created task 0x7f9804ffc0
0:00:17.189361552  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:17.189382877  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:17.189416356  1427   0x7fa0001330 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc0"
0:00:17.189430502  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc0> continue state change PAUSED to PLAYING, final PLAYING
0:00:17.189471477  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc0> completed state change to PLAYING
0:00:17.189491852  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:17.189500815  1427   0x7fa0001330 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:17.189535433  1427   0x7fa0001330 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:17.189556691  1427   0x7fa0001330 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc0:src> pad has no peer
0:00:17.189601252  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad0'
0:00:17.189635427  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc0:src and funnel0:funnelpad0
0:00:17.189681568  1427   0x7fa0001330 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:17.189731152  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc0:src and funnel0:funnelpad0, successful
0:00:17.189749152  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:17.189754100  1427   0x7fa0001330 INFO                 basesrc gstbasesrc.c:3023:gst_base_src_loop:<udpsrc0> marking pending DISCONT
0:00:17.189789377  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "udpsrc"
0:00:17.189887052  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSrc@0x7f98050d10> adding pad 'src'
0:00:17.190003702  1427   0x7fa0000b70 INFO                  udpsrc gstudpsrc.c:1652:gst_udpsrc_open:<udpsrc1> have udp buffer of 106496 bytes
0:00:17.190041452  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to READY
0:00:17.190060527  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed NULL to READY (VOID_PENDING pending)
0:00:17.190090152  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:17.190134202  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad udpsrc1:src
0:00:17.190213002  1427   0x7fa0000b70 INFO                 basesrc gstbasesrc.c:1435:gst_base_src_do_seek:<udpsrc1> seeking: time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:17.190250802  1427   0x7fa0000b70 INFO                    task gsttask.c:516:gst_task_set_lock: setting stream lock 0x7f980508d0 on task 0x7f980515d0
0:00:17.190284877  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:6376:gst_pad_start_task:<udpsrc1:src> created task 0x7f980515d0
0:00:17.190428577  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<udpsrc1> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:17.190450602  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:17.190479827  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<udpsrc1> continue state change PAUSED to PLAYING, final PLAYING
0:00:17.190510752  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<udpsrc1> completed state change to PLAYING
0:00:17.190530727  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<udpsrc1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:17.190535502  1427   0x7fa0001510 INFO        GST_ELEMENT_PADS gstelement.c:1013:gst_element_get_static_pad: no such pad 'sink' in element "udpsrc1"
0:00:17.190616027  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<funnel0> adding pad 'funnelpad1'
0:00:17.190618952  1427   0x7fa0001510 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:17.190692627  1427   0x7fa0001510 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:17.190720302  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link udpsrc1:src and funnel0:funnelpad1
0:00:17.190721752  1427   0x7fa0001510 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<udpsrc1:src> pad has no peer
0:00:17.190838902  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked udpsrc1:src and funnel0:funnelpad1, successful
0:00:17.190858252  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:17.190898027  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:17.190938852  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:17.190967527  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change READY to PAUSED, final PLAYING
0:00:17.190996452  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<funnel0> committing state from READY to PAUSED, pending PLAYING, next PLAYING
0:00:17.191014177  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed READY to PAUSED (PLAYING pending)
0:00:17.191038177  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<funnel0> continue state change PAUSED to PLAYING, final PLAYING
0:00:17.191053552  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<funnel0> completed state change to PLAYING
0:00:17.191086152  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<funnel0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:17.191134977  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:17.191316577  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f98052580> adding pad 'sink'
0:00:17.191379577  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:17.191925227  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f98054ab0> adding pad 'sink'
0:00:17.192071527  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee0> adding pad 'src_0'
0:00:17.192094552  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink0:sink
0:00:17.192146902  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee0:src_0 and multiudpsink0:sink
0:00:17.192175202  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee0:sink> pad has no peer
0:00:17.192210402  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee0:src_0 and multiudpsink0:sink, successful
0:00:17.192225127  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:17.192260102  1427   0x7fa0000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee0:src_0> Received event on flushing pad. Discarding
0:00:17.192317652  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink0> committing state from NULL to READY, pending PLAYING, next PAUSED
0:00:17.192339627  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink0> notifying about state-changed NULL to READY (PLAYING pending)
0:00:17.192375077  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink0> continue state change READY to PAUSED, final PLAYING
0:00:17.192396852  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PAUSED pending)
0:00:17.192437327  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee0> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:17.192477227  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed NULL to READY (PAUSED pending)
0:00:17.192501177  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee0> continue state change READY to PAUSED, final PAUSED
0:00:17.192525077  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PAUSED
0:00:17.192544327  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:17.192597127  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee0:sink
0:00:17.192622102  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtp_src_0 and tee0:sink
0:00:17.192713277  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtp_src_0 and tee0:sink, successful
0:00:17.192729002  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:17.192792677  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "tee"
0:00:17.192880877  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstTee@0x7f98057350> adding pad 'sink'
0:00:17.192958802  1427   0x7fa0000b70 INFO     GST_ELEMENT_FACTORY gstelementfactory.c:489:gst_element_factory_create_with_properties: creating element "multiudpsink"
0:00:17.193018752  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<GstBaseSink@0x7f98057990> adding pad 'sink'
0:00:17.193155477  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:758:gst_element_add_pad:<tee1> adding pad 'src_0'
0:00:17.193175752  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad multiudpsink1:sink
0:00:17.193219427  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link tee1:src_0 and multiudpsink1:sink
0:00:17.193241102  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:4412:gst_pad_peer_query:<tee1:sink> pad has no peer
0:00:17.193271027  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked tee1:src_0 and multiudpsink1:sink, successful
0:00:17.193285402  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:17.193299627  1427   0x7fa0000b70 INFO               GST_EVENT gstpad.c:6030:gst_pad_send_event_unchecked:<tee1:src_0> Received event on flushing pad. Discarding
0:00:17.193366552  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<multiudpsink1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:17.193385727  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:17.193427002  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<multiudpsink1> continue state change READY to PAUSED, final PAUSED
0:00:17.193453702  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PAUSED
0:00:17.193470902  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:17.193513577  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2788:gst_element_continue_state:<tee1> committing state from NULL to READY, pending PAUSED, next PAUSED
0:00:17.193533577  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed NULL to READY (PAUSED pending)
0:00:17.193553677  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2796:gst_element_continue_state:<tee1> continue state change READY to PAUSED, final PAUSED
0:00:17.193574677  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PAUSED
0:00:17.193591202  1427   0x7fa0000b70 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed READY to PAUSED (VOID_PENDING pending)
0:00:17.193611427  1427   0x7fa0000b70 INFO        GST_ELEMENT_PADS gstelement.c:1016:gst_element_get_static_pad: found pad tee1:sink
0:00:17.193634227  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2459:gst_pad_link_prepare: trying to link rtpbin0:send_rtcp_src_0 and tee1:sink
0:00:17.193675552  1427   0x7fa0000b70 INFO                GST_PADS gstpad.c:2640:gst_pad_link_full: linked rtpbin0:send_rtcp_src_0 and tee1:sink, successful
0:00:17.193743127  1427   0x7fa0000b70 INFO               GST_EVENT gstevent.c:1687:gst_event_new_reconfigure: creating reconfigure event
0:00:17.193832302  1427   0x7fa0000b70 INFO              rtspstream rtsp-stream.c:4770:update_transport: adding ***********:65154-65155
0:00:17.194096852  1427   0x7fa0000b70 FIXME              rtspmedia rtsp-media.c:2902:gst_rtsp_media_seek_trickmode:<GstRTSPMedia@0x7f9803e260> Handle going back to 0 for none live not seekable streams.
0:00:17.194118427  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:3069:gst_rtsp_media_seek_trickmode: pipeline is not seekable
0:00:17.194201927  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.000000000
0:00:17.194259352  1427   0x7fa0000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.000000000
0:00:17.194288252  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:17.194446927  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:4893:gst_rtsp_media_set_state: going to state PLAYING media 0x7f9803e260, target state PAUSED
0:00:17.194581002  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:4950:gst_rtsp_media_set_state: state 4 active 1 media 0x7f9803e260 do_state 1
0:00:17.194595977  1427   0x7fa0001150 INFO              GST_STATES gstbin.c:3405:bin_handle_async_done:<media-pipeline> committing state from PAUSED to PAUSED, old pending PLAYING
0:00:17.194602127  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:4803:media_set_pipeline_state_locked: state PLAYING media 0x7f9803e260
0:00:17.194618977  1427   0x7fa0001150 INFO              GST_STATES gstbin.c:3436:bin_handle_async_done:<media-pipeline> continue state change, pending PLAYING
0:00:17.194643827  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:3169:set_target_state: set target state to PLAYING for media 0x7f9803e260
0:00:17.194663327  1427   0x7fa0001150 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PAUSED (PLAYING pending)
0:00:17.194685027  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:3156:set_state: set state to PLAYING for media 0x7f9803e260
0:00:17.194833102  1427   0x7fa0000b70 INFO               rtspmedia rtsp-media.c:891:collect_media_stats: collect media stats
0:00:17.194940952  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:3232:gst_bin_continue_func:<media-pipeline> continue state change PAUSED to PLAYING, final PLAYING
0:00:17.195071402  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:17.195091902  1427   0x7fa00016f0 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:17.195173552  1427   0x7fa00016f0 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:17.195228677  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:17.195252302  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<multiudpsink1> completed state change to PLAYING
0:00:17.195274027  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<multiudpsink1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:17.195317802  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink1' changed state to 4(PLAYING) successfully
0:00:17.195343227  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<multiudpsink0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195364977  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<multiudpsink0> skipping transition from PLAYING to  PLAYING
0:00:17.195384327  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'multiudpsink0' changed state to 4(PLAYING) successfully
0:00:17.195410127  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee1> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:17.195432227  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee1> completed state change to PLAYING
0:00:17.195455327  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee1> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:17.195480852  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee1' changed state to 4(PLAYING) successfully
0:00:17.195502677  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<tee0> current PAUSED pending VOID_PENDING, desired next PLAYING
0:00:17.195524627  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<tee0> completed state change to PLAYING
0:00:17.195543452  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<tee0> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:17.195567702  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'tee0' changed state to 4(PLAYING) successfully
0:00:17.195594802  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpbin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195627952  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpstorage0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195644852  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpstorage0> skipping transition from PLAYING to  PLAYING
0:00:17.195663102  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpstorage0' changed state to 4(PLAYING) successfully
0:00:17.195685952  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpssrcdemux0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195702077  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpssrcdemux0> skipping transition from PLAYING to  PLAYING
0:00:17.195718477  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpssrcdemux0' changed state to 4(PLAYING) successfully
0:00:17.195738852  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<rtpsession0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195754702  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<rtpsession0> skipping transition from PLAYING to  PLAYING
0:00:17.195771527  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<rtpbin0> child 'rtpsession0' changed state to 4(PLAYING) successfully
0:00:17.195796402  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<rtpbin0> completed state change to PLAYING
0:00:17.195816602  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'rtpbin0' changed state to 4(PLAYING) successfully
0:00:17.195837127  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<bin0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195870577  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<pay0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195886877  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<pay0> skipping transition from PLAYING to  PLAYING
0:00:17.195903277  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'pay0' changed state to 4(PLAYING) successfully
0:00:17.195922952  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mpph264enc0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195938452  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mpph264enc0> skipping transition from PLAYING to  PLAYING
0:00:17.195954952  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mpph264enc0' changed state to 4(PLAYING) successfully
0:00:17.195974802  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<mppjpegdec0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.195990477  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<mppjpegdec0> skipping transition from PLAYING to  PLAYING
0:00:17.196006902  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'mppjpegdec0' changed state to 4(PLAYING) successfully
0:00:17.196027527  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<capsfilter0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.196044077  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<capsfilter0> skipping transition from PLAYING to  PLAYING
0:00:17.196060552  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'capsfilter0' changed state to 4(PLAYING) successfully
0:00:17.196081852  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<v4l2src0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.196097652  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<v4l2src0> skipping transition from PLAYING to  PLAYING
0:00:17.196114077  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<bin0> child 'v4l2src0' changed state to 4(PLAYING) successfully
0:00:17.196131427  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<bin0> completed state change to PLAYING
0:00:17.196150777  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'bin0' changed state to 4(PLAYING) successfully
0:00:17.196176702  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2480:gst_bin_element_set_state:<funnel0> current PLAYING pending VOID_PENDING, desired next PLAYING
0:00:17.196193602  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2605:gst_bin_element_set_state:<funnel0> skipping transition from PLAYING to  PLAYING
0:00:17.196211252  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'funnel0' changed state to 4(PLAYING) successfully
0:00:17.196233752  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc0' changed state to 4(PLAYING) successfully
0:00:17.196254277  1427   0x7fa00016f0 INFO              GST_STATES gstbin.c:2936:gst_bin_change_state_func:<media-pipeline> child 'udpsrc1' changed state to 4(PLAYING) successfully
0:00:17.196275177  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2816:gst_element_continue_state:<media-pipeline> completed state change to PLAYING
0:00:17.196292927  1427   0x7fa00016f0 INFO              GST_STATES gstelement.c:2716:_priv_gst_element_state_changed:<media-pipeline> notifying about state-changed PAUSED to PLAYING (VOID_PENDING pending)
0:00:17.196410352  1427   0x7fa0000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:17.199675170  1427   0x7fa0001150 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", payload=(int)96, seqnum-offset=(uint)12544, timestamp-offset=(uint)1354739697, ssrc=(uint)1286577879, a-framerate=(string)30
0:00:17.199807495  1427   0x7fa0001150 INFO              rtspstream rtsp-stream.c:2336:caps_notify: stream 0x7f9803f840 received caps 0x7f80004fb0, application/x-rtp, media=(string)video, clock-rate=(int)90000, encoding-name=(string)H264, packetization-mode=(string)1, sprop-parameter-sets=(string)"Z0LAKI2NUCgC2QgAAAMACAAAAwHkeEAhUA\=\=\,aM4xsg\=\=", payload=(int)96, seqnum-offset=(uint)12544, timestamp-offset=(uint)1354739697, ssrc=(uint)1286577879, a-framerate=(string)30
0:00:17.225010409  1427   0x7fa0000d90 INFO           basetransform gstbasetransform.c:1326:gst_base_transform_setcaps:<capsfilter0> reuse caps
0:00:17.225438683  1427   0x7fa0000d90 INFO                 v4l2src gstv4l2src.c:722:gst_v4l2src_query_preferred_size:<v4l2src0> Detect input 0 as `Input 1`
0:00:17.225671587  1427   0x7fa0000d90 INFO                 v4l2src gstv4l2src.c:868:gst_v4l2src_negotiate:<v4l2src0> fixated to: image/jpeg, format=(string)MJPG, width=(int)1280, height=(int)720, framerate=(fraction)30/1, parsed=(boolean)true, pixel-aspect-ratio=(fraction)1/1, interlace-mode=(string)progressive, colorimetry=(string)2:4:7:1
0:00:17.225734542  1427   0x7fa0000d90 INFO              bufferpool gstbufferpool.c:726:gst_buffer_pool_set_config:<v4l2src0:pool0:src> can't change config, we are active
0:00:18.611590673  1427   0x7f9c025780 INFO               GST_EVENT gstevent.c:918:gst_event_new_caps: creating caps event application/x-rtcp
0:00:18.611677365  1427   0x7f9c025780 INFO               GST_EVENT gstevent.c:998:gst_event_new_segment: creating segment event time segment start=0:00:00.000000000, offset=0:00:00.000000000, stop=99:99:99.999999999, rate=1.000000, applied_rate=1.000000, flags=0x00, time=0:00:00.000000000, base=0:00:00.000000000, position 0:00:00.000000000, duration 99:99:99.999999999
0:00:18.611855879  1427   0x7f9c025780 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1286577879, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)12544, clock-rate=(int)90000, octets-sent=(guint64)523978, packets-sent=(guint64)400, octets-received=(guint64)523978, packets-received=(guint64)400, bytes-received=(guint64)539978, bitrate=(guint64)0, packets-lost=(int)-9, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400336966028731682, sr-rtptime=(uint)1354889158, sr-octet-count=(uint)523978, sr-packet-count=(uint)400;
0:00:18.611914227  1427   0x7fa0000d30 INFO               GST_EVENT gstevent.c:1557:gst_event_new_latency: creating latency event 0:00:00.053333333
0:00:18.612006302  1427   0x7fa0000d30 INFO                     bin gstbin.c:2767:gst_bin_do_latency_func:<media-pipeline> configured latency of 0:00:00.053333333
0:00:19.926526266  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2448:on_new_ssrc: 0x7f9803f840: new source 0x7f740139d0
0:00:19.926685351  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)34906817, internal=(boolean)false, validated=(boolean)false, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)false, seqnum-base=(int)-1, clock-rate=(int)-1, rtcp-from=(string)***********:65155, octets-sent=(guint64)0, packets-sent=(guint64)0, octets-received=(guint64)0, packets-received=(guint64)0, bytes-received=(guint64)0, bitrate=(guint64)0, packets-lost=(int)0, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)false, sr-ntptime=(guint64)0, sr-rtptime=(uint)0, sr-octet-count=(uint)0, sr-packet-count=(uint)0, sent-rb=(boolean)false, sent-rb-fractionlost=(uint)0, sent-rb-packetslost=(int)0, sent-rb-exthighestseq=(uint)0, sent-rb-jitter=(uint)0, sent-rb-lsr=(uint)0, sent-rb-dlsr=(uint)0, have-rb=(boolean)false, rb-ssrc=(uint)0, rb-fractionlost=(uint)0, rb-packetslost=(int)0, rb-exthighestseq=(uint)0, rb-jitter=(uint)0, rb-lsr=(uint)0, rb-dlsr=(uint)0, rb-round-trip=(uint)0;
0:00:19.926729637  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2379:find_transport: finding ***********:65155 in 1 transports
0:00:19.926744525  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2431:check_transport: 0x7f9803f840: found transport 0x7f9804ba50 for source  0x7f740139d0
0:00:19.926760225  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2453:on_new_ssrc: 0x7f9803f840: source 0x7f740139d0 for transport 0x7f9804ba50
0:00:19.926784202  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f9803f840: source 0x7f740139d0 in transport 0x7f9804ba50 is active
0:00:19.926796109  1427   0x7fa0001330 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f9804ae40 alive
0:00:19.926848403  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2459:on_ssrc_sdes: 0x7f9803f840: new SDES 0x7f740139d0
0:00:23.199646719  1427   0x7f9c025780 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1286577879, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)12544, clock-rate=(int)90000, octets-sent=(guint64)2577461, packets-sent=(guint64)1942, octets-received=(guint64)2577461, packets-received=(guint64)1942, bytes-received=(guint64)2655141, bitrate=(guint64)3127139, packets-lost=(int)-13, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400336985733912787, sr-rtptime=(uint)1355302075, sr-octet-count=(uint)2577461, sr-packet-count=(uint)1942;
0:00:25.972565451  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f9803f840: source 0x7f740139d0 in transport 0x7f9804ba50 is active
0:00:25.972595815  1427   0x7fa0001330 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f9804ae40 alive
0:00:28.152542421  1427   0x7f9c025780 INFO              rtspstream rtsp-stream.c:2354:dump_structure: structure: application/x-rtp-source-stats, ssrc=(uint)1286577879, internal=(boolean)true, validated=(boolean)true, received-bye=(boolean)false, is-csrc=(boolean)false, is-sender=(boolean)true, seqnum-base=(int)12544, clock-rate=(int)90000, octets-sent=(guint64)4670047, packets-sent=(guint64)3516, octets-received=(guint64)4670047, packets-received=(guint64)3516, bytes-received=(guint64)4810687, bitrate=(guint64)3300781, packets-lost=(int)-8, jitter=(uint)0, sent-pli-count=(uint)0, recv-pli-count=(uint)0, sent-fir-count=(uint)0, recv-fir-count=(uint)0, sent-nack-count=(uint)0, recv-nack-count=(uint)0, recv-packet-rate=(uint)0, have-sr=(boolean)true, sr-ntptime=(guint64)16400337007006469192, sr-rtptime=(uint)1355747836, sr-octet-count=(uint)4670047, sr-packet-count=(uint)3516;
0:00:30.050244998  1427   0x7fa0001330 INFO              rtspstream rtsp-stream.c:2470:on_ssrc_active: 0x7f9803f840: source 0x7f740139d0 in transport 0x7f9804ba50 is active
0:00:30.050277948  1427   0x7fa0001330 INFO              rtspclient rtsp-client.c:2331:do_keepalive: keep session 0x7f9804ae40 alive